# App Store 合规性检查清单

## 📋 上架前必检项目

### ✅ 已修复的问题

#### 1. 敏感信息安全
- [x] 移除配置文件中的硬编码API密钥
- [x] 使用环境变量管理敏感配置
- [x] 添加.gitignore防止敏感文件提交
- [x] 创建.env.example作为配置模板

#### 2. 权限描述优化
- [x] 完善语音识别权限说明
- [x] 详细说明麦克风使用目的
- [x] 明确健康数据使用范围和隐私保护
- [x] 说明本地网络访问用途

#### 3. Bundle ID规范化
- [x] 统一测试Bundle ID为com.huiziqin.kcalfit.RunnerTests
- [x] 确保主应用Bundle ID为com.huiziqin.kcalfit

#### 4. 推送配置
- [x] 生产环境推送设置为production模式
- [x] 更新entitlements文件推送环境

#### 5. 法律文档更新
- [x] 更新隐私政策时间为2025年1月16日
- [x] 更新用户协议时间为2025年1月16日

#### 6. 代码清理
- [x] 移除生产环境中的print语句
- [x] 清理调试代码和TODO注释
- [x] 移除未使用的函数和变量
- [x] 优化错误处理逻辑

#### 7. 构建配置
- [x] 创建生产环境构建脚本
- [x] 配置Release模式构建参数
- [x] 添加代码分析和测试检查

### 🔍 需要人工检查的项目

#### 1. 应用内容审查
- [ ] 确认应用图标符合Apple设计规范
- [ ] 检查应用截图是否准确反映功能
- [ ] 验证应用描述与实际功能一致

#### 2. 功能合规性
- [ ] 确认健康数据使用符合HealthKit指南
- [ ] 验证语音识别功能正常工作
- [ ] 测试苹果登录功能
- [ ] 确认推送通知功能正常

#### 3. 隐私合规
- [ ] 确认隐私政策链接可访问
- [ ] 验证数据收集符合隐私政策描述
- [ ] 检查是否需要隐私营养标签

#### 4. 技术要求
- [ ] 确认最低iOS版本支持(当前15.6)
- [ ] 测试在不同设备上的兼容性
- [ ] 验证应用性能和稳定性

### ⚠️ 特别注意事项

#### 健康应用特殊要求
1. **医疗免责声明**：应用已包含"不能替代专业医疗建议"的声明
2. **数据准确性**：营养数据来源已声明可能存在误差
3. **用户控制**：用户可以控制健康数据访问权限

#### 推送通知要求
1. **用户同意**：首次使用时请求推送权限
2. **内容相关**：推送内容与应用功能相关
3. **频率合理**：避免过度推送

#### 语音功能要求
1. **权限说明**：已详细说明语音功能用途
2. **用户控制**：用户可以选择不使用语音功能
3. **数据处理**：语音数据仅用于文字转换

### 📝 上架前最终检查

#### App Store Connect配置
- [ ] 应用信息完整填写
- [ ] 价格和可用性设置
- [ ] 应用审查信息填写
- [ ] 版本发布信息准备

#### 构建和测试
- [ ] 使用Release模式构建
- [ ] 在真机上测试所有功能
- [ ] 验证推送通知在生产环境工作
- [ ] 测试健康数据权限申请流程

#### 法律和隐私
- [ ] 确认隐私政策URL正确
- [ ] 验证用户协议URL正确
- [ ] 检查应用内隐私设置功能

### 🚀 提交建议

1. **首次提交**：建议先提交审查，根据反馈进行调整
2. **版本说明**：详细说明新功能和改进
3. **审查备注**：如有特殊功能，在审查备注中说明
4. **联系信息**：确保联系信息准确，便于Apple联系

### 📞 紧急联系

如果审查被拒绝，请检查：
1. 拒绝原因是否在此清单中
2. 是否需要更新隐私政策
3. 是否需要调整权限描述
4. 是否需要修改应用功能

---

**最后更新**：2025年1月16日
**负责人**：开发团队
**状态**：待最终检查

# 用户引导功能说明

## 功能概述

用户引导功能旨在为首次使用应用的用户提供友好的引导体验，帮助用户快速了解应用功能并完成必要的设置。

## 主要特性

### 1. 首次启动检测
- 自动检测用户是否首次使用应用
- 使用Hive本地存储记录用户状态
- 避免重复显示引导内容

### 2. 身体数据录入引导
- 检测用户是否已录入身体数据
- 如果没有身体数据，显示引导对话框
- 提供立即录入和稍后设置两个选项

### 3. 引导对话框设计
- 美观的Material Design风格
- 清晰的功能说明和好处介绍
- 隐私保护承诺
- 用户友好的交互设计

## 技术实现

### 核心服务：UserGuideService

```dart
class UserGuideService extends GetxService {
  // 状态管理
  final RxBool isFirstLaunch = true.obs;
  final RxBool bodyDataGuideShown = false.obs;
  
  // 主要方法
  Future<void> checkAndShowGuide();
  Future<void> resetGuideStatus();
}
```

### 集成点

1. **应用初始化**：在 `AppInitializer` 中注册服务
2. **首页控制器**：在 `HomeController` 中触发引导检查
3. **身体数据表单**：保存数据后标记引导完成
4. **隐私设置**：提供重置引导功能

## 用户体验流程

### 首次使用流程
1. 用户首次启动应用
2. 进入首页后延迟1.5秒显示引导
3. 检查是否有身体数据
4. 如果没有，显示身体数据录入引导对话框
5. 用户选择立即录入或稍后设置

### 弹框式身体数据录入
- **标题**：完善身体数据 - 为您提供更精准的营养建议
- **录入字段**：
  - 性别：男/女选择（带图标的按钮式选择）
  - 出生日期：日期选择器
  - 身高：数字输入，单位cm，验证范围100-250
  - 体重：数字输入，单位kg，验证范围30-300
  - 体脂率：可选字段，数字输入，单位%，验证范围5-50
- **数据处理**：
  - 自动计算BMI指数
  - 性别和出生日期存储到用户配置文件
  - 身体数据存储到身体数据表
- **隐私承诺**：数据安全存储承诺
- **操作按钮**：稍后设置 / 保存

### 用户体验优化
- 弹框式设计，无需页面跳转
- 表单验证，确保数据有效性
- 加载状态显示，提供操作反馈
- 保存成功后自动关闭并显示成功消息
- 自动标记引导完成，不再重复显示

## 管理功能

### 重置引导状态
在隐私设置页面提供"重置用户引导"功能：
- 重置首次启动标记
- 重置身体数据引导状态
- 下次启动时重新显示引导

### 数据存储
使用Hive本地存储管理引导状态：
- `user_guide.first_launch`：首次启动标记
- `user_guide.body_data_guide_shown`：身体数据引导显示标记

## 扩展性

### 添加新的引导内容
1. 在 `UserGuideService` 中添加新的状态变量
2. 实现对应的检查和显示逻辑
3. 在适当的时机调用引导检查

### 自定义引导样式
- 修改 `_buildBodyDataGuideDialog()` 方法
- 调整对话框布局和样式
- 添加动画效果

## 注意事项

1. **性能考虑**：引导检查在UI加载完成后延迟执行
2. **错误处理**：所有引导相关操作都有异常处理
3. **用户体验**：避免强制性引导，提供跳过选项
4. **数据安全**：引导状态仅存储在本地
5. **测试友好**：提供重置功能便于测试

## 使用示例

### 触发引导检查
```dart
// 在控制器中调用
final userGuideService = Get.find<UserGuideService>();
await userGuideService.checkAndShowGuide();
```

### 重置引导状态
```dart
// 重置所有引导状态
await userGuideService.resetGuideStatus();
```

### 检查引导状态
```dart
// 检查是否首次启动
bool isFirst = userGuideService.isFirstLaunch.value;

// 检查身体数据引导是否已显示
bool shown = userGuideService.bodyDataGuideShown.value;
```

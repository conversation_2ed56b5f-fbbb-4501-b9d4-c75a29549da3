#!/bin/bash

# iOS App Store 发布构建脚本
# 用于构建符合App Store要求的Release版本

set -e  # 遇到错误立即退出

echo "🚀 开始构建 KcalFit iOS Release 版本..."

# 检查当前目录
if [ ! -f "pubspec.yaml" ]; then
    echo "❌ 错误：请在Flutter项目根目录运行此脚本"
    exit 1
fi

# 检查Flutter环境
echo "📋 检查Flutter环境..."
flutter doctor

# 清理项目
echo "🧹 清理项目..."
flutter clean
flutter pub cache clean

# 获取依赖
echo "📦 获取依赖..."
flutter pub get

# 代码分析
echo "🔍 代码分析..."
flutter analyze
if [ $? -ne 0 ]; then
    echo "❌ 代码分析发现问题，请修复后重试"
    exit 1
fi

# 运行测试
echo "🧪 运行测试..."
flutter test
if [ $? -ne 0 ]; then
    echo "❌ 测试失败，请修复后重试"
    exit 1
fi

# 清理iOS项目
echo "🧹 清理iOS项目..."
cd ios
pod deintegrate || true
pod cache clean --all || true
pod install
cd ..

# 构建Release版本
echo "🔨 构建Release版本..."
flutter build ios --release --no-codesign

# 检查构建结果
if [ $? -eq 0 ]; then
    echo "✅ 构建成功！"
    echo ""
    echo "📋 下一步操作："
    echo "1. 在Xcode中打开 ios/Runner.xcworkspace"
    echo "2. 选择 Product > Archive"
    echo "3. 上传到App Store Connect"
    echo ""
    echo "⚠️  上传前请确保："
    echo "- 已配置正确的Bundle ID: com.huiziqin.kcalfit"
    echo "- 已设置正确的开发者证书和Provisioning Profile"
    echo "- 版本号已更新（当前版本：$(grep version pubspec.yaml | cut -d' ' -f2)）"
else
    echo "❌ 构建失败！"
    exit 1
fi

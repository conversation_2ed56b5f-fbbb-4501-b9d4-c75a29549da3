name: kcal_fit
description: "卡路里记录（KcalFit）陪你轻松迈向健康生活"
publish_to: 'none'

version: 1.1.0+3

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations: 
    sdk: flutter
  cupertino_icons: ^1.0.8
  dio: ^5.8.0+1 # 网络请求
  logger: ^2.5.0 # 日志打印
  get: ^4.7.2 # 状态管理
  calendar_date_picker2: ^2.0.0 # 日历
  scroll_datetime_picker: ^0.2.2 # 滚动式日期时间选择器
  intl: ^0.20.2 # 国际化支持
  flutter_ruler_picker: ^1.0.6 # 尺子式选择器
  speech_to_text: ^7.0.0 # 语音转文字
  hive: ^2.2.3 # 本地数据库 NOSQL
  hive_flutter: ^1.1.0 # Hive 扩展。使在 Flutter 应用中使用 Hive 更加容易
  hive_generator: ^2.0.1 # Hive 生成器。用于在 Flutter 应用中生成 Hive 模型
  sqflite: ^2.4.2 # 本地数据库 SQLite 数据库
  path: ^1.9.1 # 路径操作
  path_provider: ^2.1.4 # 获取应用目录路径
  uuid: ^4.5.1 # 生成 UUID
  permission_handler: ^12.0.0+1 # 权限管理
  jpush_flutter: ^3.2.3 # 极光推送
  event_bus: ^2.0.1
  json_annotation: ^4.9.0
  sign_in_with_apple: ^7.0.1 # 苹果登录
  package_info_plus: ^8.3.0 # 获取应用信息
  device_info_plus: ^11.1.0 # 获取设备信息
  animated_bottom_navigation_bar: ^1.4.0 # 底部导航栏
  flutter_chat_core: ^2.6.1 # 聊天核心功能
  flutter_chat_ui: ^2.3.1 # 聊天UI组件
  health: ^13.1.0 # 健康数据获取
  pull_to_refresh: ^2.0.0 # 下拉刷新组件
  fl_chart: ^0.69.0 # 图表组件






dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner: ^2.4.15
  json_serializable: ^6.9.0

flutter:
  uses-material-design: true

  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 卡片式图标+文字按钮基础组件
/// 卡片 图标 + 文字
class CardIconText extends GetWidget<CardIconTextController> {
  final IconData icon;
  final String text;
  final Function() onTap; //将点击事件处理权交给使用组件的父组件
  /// 卡片式图标+文章按钮基础组件
  const CardIconText({
    super.key,
    required this.icon,
    required this.text,
    required this.onTap,
  });
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      // GestureDetector是一个手势检测器，用于检测用户的手势操作，如点击、滑动等。
      onTap: onTap,
      behavior: HitTestBehavior.opaque, //behavior 是一个枚举值，用于指定手势检测器的行为。
      // HitTestBehavior.opaque 表示当手势检测器检测到手势时，它会将手势传递给它的子控件。
      // HitTestBehavior.translucent 表示当手势检测器检测到手势时，它会将手势传递给它的子控件，但是它的子控件会忽略手势。
      child: Container(
        width: 100,
        padding: EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: Colors.grey[200]!),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 24, color: Colors.blue[600]),
            SizedBox(height: 6),
            Text(text, style: TextStyle(fontSize: 12, color: Colors.grey[700])),
          ],
        ),
      ),
    );
  }
}

class CardIconTextController extends GetxController {
  // 按钮相关的控制逻辑
}

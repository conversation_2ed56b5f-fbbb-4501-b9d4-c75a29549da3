import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CalendarController extends GetxController {
  final selectedDates = <DateTime>[].obs;

  void selectDate(DateTime date) {
    selectedDates.clear();
    selectedDates.add(date);
  }

  bool hasTopText(DateTime date) {
    // 默认实现，可根据需要重写
    return date.day >= 15 && date.day <= 26;
  }

  bool hasBottomMarker(DateTime date) {
    // 默认实现，可根据需要重写
    return date.day % 2 == 0;
  }

  String? topTextBuilder(DateTime date) {
    // 默认实现，返回null表示不显示顶部文字
    return null;
  }
}

class CalendarCard extends StatelessWidget {
  final CalendarController? controller;
  final Color markerColor;

  const CalendarCard({
    super.key,
    this.controller,
    this.markerColor = Colors.blue,
  });

  @override
  Widget build(BuildContext context) {
    final ctrl = controller ?? Get.find<CalendarController>();

    return Obx(
      () => CalendarDatePicker2(
        config: CalendarDatePicker2Config(
          selectedDayHighlightColor: Colors.blue[100],
          selectedRangeHighlightColor: Colors.blue[100],
          dayBuilder: ({
            required DateTime date,
            TextStyle? textStyle,
            BoxDecoration? decoration,
            bool? isSelected,
            bool? isDisabled,
            bool? isToday,
          }) {
            final isDateSelected = ctrl.selectedDates.any(
              (d) =>
                  d.year == date.year &&
                  d.month == date.month &&
                  d.day == date.day,
            );

            return GestureDetector(
              onTap: () => ctrl.selectDate(date),
              child: Container(
                decoration:
                    isDateSelected
                        ? BoxDecoration(
                          color: Colors.blue[100],
                          shape: BoxShape.circle,
                        )
                        : decoration,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (ctrl.hasTopText(date))
                          Text(
                            ctrl.topTextBuilder(date) ?? '',
                            style: TextStyle(
                              fontSize: 8,
                              color: const Color.fromARGB(255, 86, 54, 244),
                            ),
                          ),
                        Text(
                          '${date.day}',
                          style: textStyle?.copyWith(
                            fontWeight: isDateSelected ? FontWeight.bold : null,
                            color:
                                isDateSelected
                                    ? Colors.blue[800]
                                    : textStyle.color,
                          ),
                        ),
                        if (ctrl.hasBottomMarker(date))
                          Container(
                            margin: const EdgeInsets.only(top: 2),
                            height: 4,
                            width: 4,
                            decoration: BoxDecoration(
                              color: markerColor,
                              shape: BoxShape.circle,
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        ),
        value: ctrl.selectedDates.toList(),
        onValueChanged: (dates) {
          if (dates.isNotEmpty) {
            ctrl.selectDate(dates.last);
          }
        },
      ),
    );
  }
}

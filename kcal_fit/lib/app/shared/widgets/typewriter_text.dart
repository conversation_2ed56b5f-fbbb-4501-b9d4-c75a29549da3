import 'package:flutter/material.dart';
import 'dart:async';

/// 打字机效果文本组件
/// 逐字符显示文本，模拟打字机效果
class TypewriterText extends StatefulWidget {
  /// 要显示的文本
  final String text;
  
  /// 打字速度（每个字符的间隔时间，毫秒）
  final int speed;
  
  /// 文本样式
  final TextStyle? style;
  
  /// 是否显示光标
  final bool showCursor;
  
  /// 光标颜色
  final Color cursorColor;
  
  /// 动画完成回调
  final VoidCallback? onComplete;
  
  /// 是否自动开始动画
  final bool autoStart;

  const TypewriterText({
    super.key,
    required this.text,
    this.speed = 50,
    this.style,
    this.showCursor = true,
    this.cursorColor = Colors.black54,
    this.onComplete,
    this.autoStart = true,
  });

  @override
  State<TypewriterText> createState() => _TypewriterTextState();
}

class _TypewriterTextState extends State<TypewriterText>
    with TickerProviderStateMixin {
  /// 当前显示的文本
  String _displayedText = '';
  
  /// 打字机定时器
  Timer? _typewriterTimer;
  
  /// 光标动画控制器
  late AnimationController _cursorController;
  
  /// 光标动画
  late Animation<double> _cursorAnimation;
  
  /// 当前字符索引
  int _currentIndex = 0;
  
  /// 是否动画完成
  bool _isComplete = false;

  @override
  void initState() {
    super.initState();
    
    // 初始化光标动画
    _cursorController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _cursorAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_cursorController);
    
    // 开始光标闪烁
    _cursorController.repeat(reverse: true);
    
    // 自动开始打字机效果
    if (widget.autoStart) {
      _startTypewriter();
    }
  }

  @override
  void dispose() {
    _typewriterTimer?.cancel();
    _cursorController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(TypewriterText oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // 如果文本改变，重新开始动画
    if (oldWidget.text != widget.text) {
      _resetAnimation();
      if (widget.autoStart) {
        _startTypewriter();
      }
    }
  }

  /// 开始打字机效果
  void _startTypewriter() {
    _resetAnimation();
    
    _typewriterTimer = Timer.periodic(
      Duration(milliseconds: widget.speed),
      (timer) {
        if (_currentIndex < widget.text.length) {
          setState(() {
            _currentIndex++;
            _displayedText = widget.text.substring(0, _currentIndex);
          });
        } else {
          // 动画完成
          timer.cancel();
          _isComplete = true;
          
          // 停止光标闪烁
          if (!widget.showCursor) {
            _cursorController.stop();
          }
          
          // 调用完成回调
          widget.onComplete?.call();
        }
      },
    );
  }

  /// 重置动画状态
  void _resetAnimation() {
    _typewriterTimer?.cancel();
    _currentIndex = 0;
    _displayedText = '';
    _isComplete = false;
    
    if (mounted) {
      setState(() {});
    }
  }

  /// 立即完成动画
  void completeAnimation() {
    _typewriterTimer?.cancel();
    setState(() {
      _currentIndex = widget.text.length;
      _displayedText = widget.text;
      _isComplete = true;
    });
    
    if (!widget.showCursor) {
      _cursorController.stop();
    }
    
    widget.onComplete?.call();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 点击时立即完成动画
        if (!_isComplete) {
          completeAnimation();
        }
      },
      child: RichText(
        text: TextSpan(
          style: widget.style ?? DefaultTextStyle.of(context).style,
          children: [
            TextSpan(text: _displayedText),
            if (widget.showCursor && (!_isComplete || widget.showCursor))
              WidgetSpan(
                child: AnimatedBuilder(
                  animation: _cursorAnimation,
                  builder: (context, child) {
                    return Opacity(
                      opacity: _cursorAnimation.value,
                      child: Container(
                        width: 2,
                        height: (widget.style?.fontSize ?? 14) * 1.2,
                        color: widget.cursorColor,
                        margin: const EdgeInsets.only(left: 1),
                      ),
                    );
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// 打字机效果的富文本组件
/// 支持不同样式的文本段落
class TypewriterRichText extends StatefulWidget {
  /// 文本段落列表
  final List<TypewriterTextSpan> spans;
  
  /// 打字速度（每个字符的间隔时间，毫秒）
  final int speed;
  
  /// 是否显示光标
  final bool showCursor;
  
  /// 光标颜色
  final Color cursorColor;
  
  /// 动画完成回调
  final VoidCallback? onComplete;
  
  /// 是否自动开始动画
  final bool autoStart;

  const TypewriterRichText({
    super.key,
    required this.spans,
    this.speed = 50,
    this.showCursor = true,
    this.cursorColor = Colors.black54,
    this.onComplete,
    this.autoStart = true,
  });

  @override
  State<TypewriterRichText> createState() => _TypewriterRichTextState();
}

class _TypewriterRichTextState extends State<TypewriterRichText>
    with TickerProviderStateMixin {
  /// 当前显示的文本段落
  List<TypewriterTextSpan> _displayedSpans = [];
  
  /// 打字机定时器
  Timer? _typewriterTimer;
  
  /// 光标动画控制器
  late AnimationController _cursorController;
  
  /// 光标动画
  late Animation<double> _cursorAnimation;
  
  /// 当前字符索引
  int _currentIndex = 0;
  
  /// 是否动画完成
  bool _isComplete = false;
  
  /// 全部文本
  String _fullText = '';

  @override
  void initState() {
    super.initState();
    
    // 合并所有文本
    _fullText = widget.spans.map((span) => span.text).join();
    
    // 初始化光标动画
    _cursorController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _cursorAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_cursorController);
    
    // 开始光标闪烁
    _cursorController.repeat(reverse: true);
    
    // 自动开始打字机效果
    if (widget.autoStart) {
      _startTypewriter();
    }
  }

  @override
  void dispose() {
    _typewriterTimer?.cancel();
    _cursorController.dispose();
    super.dispose();
  }

  /// 开始打字机效果
  void _startTypewriter() {
    _resetAnimation();
    
    _typewriterTimer = Timer.periodic(
      Duration(milliseconds: widget.speed),
      (timer) {
        if (_currentIndex < _fullText.length) {
          setState(() {
            _currentIndex++;
            _updateDisplayedSpans();
          });
        } else {
          // 动画完成
          timer.cancel();
          _isComplete = true;
          
          // 停止光标闪烁
          if (!widget.showCursor) {
            _cursorController.stop();
          }
          
          // 调用完成回调
          widget.onComplete?.call();
        }
      },
    );
  }

  /// 更新显示的文本段落
  void _updateDisplayedSpans() {
    _displayedSpans.clear();
    int charCount = 0;
    
    for (final span in widget.spans) {
      final spanLength = span.text.length;
      
      if (charCount + spanLength <= _currentIndex) {
        // 完整显示这个段落
        _displayedSpans.add(span);
        charCount += spanLength;
      } else if (charCount < _currentIndex) {
        // 部分显示这个段落
        final partialLength = _currentIndex - charCount;
        _displayedSpans.add(TypewriterTextSpan(
          text: span.text.substring(0, partialLength),
          style: span.style,
        ));
        break;
      } else {
        break;
      }
    }
  }

  /// 重置动画状态
  void _resetAnimation() {
    _typewriterTimer?.cancel();
    _currentIndex = 0;
    _displayedSpans.clear();
    _isComplete = false;
    
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 点击时立即完成动画
        if (!_isComplete) {
          _typewriterTimer?.cancel();
          setState(() {
            _currentIndex = _fullText.length;
            _displayedSpans = List.from(widget.spans);
            _isComplete = true;
          });
          
          if (!widget.showCursor) {
            _cursorController.stop();
          }
          
          widget.onComplete?.call();
        }
      },
      child: RichText(
        text: TextSpan(
          children: [
            ..._displayedSpans.map((span) => TextSpan(
              text: span.text,
              style: span.style,
            )),
            if (widget.showCursor && (!_isComplete || widget.showCursor))
              WidgetSpan(
                child: AnimatedBuilder(
                  animation: _cursorAnimation,
                  builder: (context, child) {
                    return Opacity(
                      opacity: _cursorAnimation.value,
                      child: Container(
                        width: 2,
                        height: 16,
                        color: widget.cursorColor,
                        margin: const EdgeInsets.only(left: 1),
                      ),
                    );
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// 打字机文本段落
class TypewriterTextSpan {
  final String text;
  final TextStyle? style;

  const TypewriterTextSpan({
    required this.text,
    this.style,
  });
}

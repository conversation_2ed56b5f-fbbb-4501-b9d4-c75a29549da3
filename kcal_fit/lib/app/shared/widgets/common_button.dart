import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';

/// 通用按钮组件
/// 提供统一的按钮样式和行为
class CommonButton extends StatelessWidget {
  /// 按钮文本
  final String text;

  /// 点击回调
  final VoidCallback? onPressed;

  /// 按钮类型
  final ButtonType type;

  /// 按钮大小
  final ButtonSize size;

  /// 是否加载中
  final bool isLoading;

  /// 是否禁用
  final bool isDisabled;

  /// 自定义宽度
  final double? width;

  /// 自定义高度
  final double? height;

  /// 图标
  final IconData? icon;

  /// 图标位置
  final IconPosition iconPosition;

  const CommonButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = ButtonType.primary,
    this.size = ButtonSize.medium,
    this.isLoading = false,
    this.isDisabled = false,
    this.width,
    this.height,
    this.icon,
    this.iconPosition = IconPosition.left,
  });

  /// 主要按钮构造函数
  const CommonButton.primary({
    super.key,
    required this.text,
    this.onPressed,
    this.size = ButtonSize.medium,
    this.isLoading = false,
    this.isDisabled = false,
    this.width,
    this.height,
    this.icon,
    this.iconPosition = IconPosition.left,
  }) : type = ButtonType.primary;

  /// 次要按钮构造函数
  const CommonButton.secondary({
    super.key,
    required this.text,
    this.onPressed,
    this.size = ButtonSize.medium,
    this.isLoading = false,
    this.isDisabled = false,
    this.width,
    this.height,
    this.icon,
    this.iconPosition = IconPosition.left,
  }) : type = ButtonType.secondary;

  /// 文本按钮构造函数
  const CommonButton.text({
    super.key,
    required this.text,
    this.onPressed,
    this.size = ButtonSize.medium,
    this.isLoading = false,
    this.isDisabled = false,
    this.width,
    this.height,
    this.icon,
    this.iconPosition = IconPosition.left,
  }) : type = ButtonType.text;

  @override
  Widget build(BuildContext context) {
    final buttonStyle = _getButtonStyle();
    final buttonSize = _getButtonSize();

    Widget button;

    switch (type) {
      case ButtonType.primary:
        button = ElevatedButton(onPressed: _getOnPressed(), style: buttonStyle, child: _buildButtonContent());
        break;
      case ButtonType.secondary:
        button = OutlinedButton(onPressed: _getOnPressed(), style: buttonStyle, child: _buildButtonContent());
        break;
      case ButtonType.text:
        button = TextButton(onPressed: _getOnPressed(), style: buttonStyle, child: _buildButtonContent());
        break;
    }

    return SizedBox(width: width ?? buttonSize.width, height: height ?? buttonSize.height, child: button);
  }

  /// 获取按钮点击回调
  VoidCallback? _getOnPressed() {
    if (isDisabled || isLoading) return null;
    return onPressed;
  }

  /// 构建按钮内容
  Widget _buildButtonContent() {
    if (isLoading) {
      return const SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(strokeWidth: 2, valueColor: AlwaysStoppedAnimation<Color>(Colors.white)),
      );
    }

    if (icon != null) {
      return _buildButtonWithIcon();
    }

    return Text(text);
  }

  /// 构建带图标的按钮内容
  Widget _buildButtonWithIcon() {
    final iconWidget = Icon(icon, size: _getIconSize());
    final textWidget = Text(text);

    if (iconPosition == IconPosition.left) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [iconWidget, const SizedBox(width: AppTheme.spacingSmall), textWidget],
      );
    } else {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [textWidget, const SizedBox(width: AppTheme.spacingSmall), iconWidget],
      );
    }
  }

  /// 获取按钮样式
  ButtonStyle _getButtonStyle() {
    switch (type) {
      case ButtonType.primary:
        return ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
          disabledBackgroundColor: AppTheme.textHintColor,
          disabledForegroundColor: Colors.white,
          elevation: 2,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(AppTheme.cardRadius)),
          textStyle: TextStyle(
            fontSize: _getFontSize(),
            fontWeight: AppTheme.fontWeightMedium,
            height: 1.2, // 设置行高，确保文字不被裁剪
          ),
        );
      case ButtonType.secondary:
        return OutlinedButton.styleFrom(
          foregroundColor: AppTheme.primaryColor,
          disabledForegroundColor: AppTheme.textHintColor,
          side: const BorderSide(color: AppTheme.primaryColor),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(AppTheme.cardRadius)),
          textStyle: TextStyle(
            fontSize: _getFontSize(),
            fontWeight: AppTheme.fontWeightMedium,
            height: 1.2, // 设置行高，确保文字不被裁剪
          ),
        );
      case ButtonType.text:
        return TextButton.styleFrom(
          foregroundColor: AppTheme.primaryColor,
          disabledForegroundColor: AppTheme.textHintColor,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          textStyle: TextStyle(
            fontSize: _getFontSize(),
            fontWeight: AppTheme.fontWeightMedium,
            height: 1.2, // 设置行高，确保文字不被裁剪
          ),
        );
    }
  }

  /// 获取按钮尺寸
  _ButtonSize _getButtonSize() {
    switch (size) {
      case ButtonSize.small:
        return const _ButtonSize(width: 80, height: 36);
      case ButtonSize.medium:
        return const _ButtonSize(width: 120, height: 44);
      case ButtonSize.large:
        return const _ButtonSize(width: 160, height: 52);
      case ButtonSize.fullWidth:
        return const _ButtonSize(width: double.infinity, height: 52);
    }
  }

  /// 获取字体大小
  double _getFontSize() {
    switch (size) {
      case ButtonSize.small:
        return AppTheme.fontSizeSmall;
      case ButtonSize.medium:
        return AppTheme.fontSizeNormal;
      case ButtonSize.large:
      case ButtonSize.fullWidth:
        return AppTheme.fontSizeMedium;
    }
  }

  /// 获取图标大小
  double _getIconSize() {
    switch (size) {
      case ButtonSize.small:
        return 16;
      case ButtonSize.medium:
        return 18;
      case ButtonSize.large:
      case ButtonSize.fullWidth:
        return 20;
    }
  }
}

/// 按钮类型枚举
enum ButtonType {
  /// 主要按钮
  primary,

  /// 次要按钮
  secondary,

  /// 文本按钮
  text,
}

/// 按钮大小枚举
enum ButtonSize {
  /// 小尺寸
  small,

  /// 中等尺寸
  medium,

  /// 大尺寸
  large,

  /// 全宽
  fullWidth,
}

/// 图标位置枚举
enum IconPosition {
  /// 左侧
  left,

  /// 右侧
  right,
}

/// 按钮尺寸数据类
class _ButtonSize {
  final double? width;
  final double height;

  const _ButtonSize({required this.width, required this.height});
}

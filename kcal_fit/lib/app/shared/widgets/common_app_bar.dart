import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/theme/app_theme.dart';
import '../../../core/services/theme_service.dart';

/// 通用应用栏组件
/// 提供统一的AppBar样式和行为，确保整个应用的一致性
class CommonAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// 标题文本
  final String title;

  /// 是否显示返回按钮
  final bool showBackButton;

  /// 自定义返回按钮回调
  final VoidCallback? onBackPressed;

  /// 右侧操作按钮列表
  final List<Widget>? actions;

  /// 是否居中显示标题
  final bool centerTitle;

  /// 背景颜色（可选，默认使用主题色）
  final Color? backgroundColor;

  /// 前景色（可选，默认使用白色）
  final Color? foregroundColor;

  /// 阴影高度
  final double elevation;

  /// 标题样式（可选）
  final TextStyle? titleStyle;

  /// 构造函数
  const CommonAppBar({
    super.key,
    required this.title,
    this.showBackButton = true,
    this.onBackPressed,
    this.actions,
    this.centerTitle = true,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation = 0,
    this.titleStyle,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style:
            titleStyle ??
            const TextStyle(
              fontSize: AppTheme.fontSizeLarge,
              fontWeight: AppTheme.fontWeightMedium,
              color: Colors.white,
            ),
      ),
      centerTitle: centerTitle,
      elevation: elevation,
      backgroundColor: backgroundColor ?? AppTheme.primaryColor,
      foregroundColor: foregroundColor ?? Colors.white,
      iconTheme: IconThemeData(color: foregroundColor ?? Colors.white),
      actionsIconTheme: IconThemeData(color: foregroundColor ?? Colors.white),
      leading: showBackButton ? _buildLeading() : null,
      actions: actions,
    );
  }

  /// 构建返回按钮
  Widget? _buildLeading() {
    if (!(Get.routing.isBack ?? false)) {
      return null;
    }

    return IconButton(
      icon: const Icon(Icons.arrow_back_ios),
      onPressed: onBackPressed ?? () => Get.back(),
      tooltip: '返回',
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// 白色背景的AppBar变体
/// 适用于需要白色背景的页面
class WhiteAppBar extends CommonAppBar {
  const WhiteAppBar({
    super.key,
    required super.title,
    super.showBackButton = true,
    super.onBackPressed,
    super.actions,
    super.centerTitle = true,
    super.elevation = 0,
    super.titleStyle,
  }) : super(backgroundColor: Colors.white, foregroundColor: AppTheme.textPrimaryColor);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style:
            titleStyle ??
            const TextStyle(
              fontSize: AppTheme.fontSizeLarge,
              fontWeight: AppTheme.fontWeightMedium,
              color: AppTheme.textPrimaryColor,
            ),
      ),
      centerTitle: centerTitle,
      elevation: elevation,
      backgroundColor: Colors.white,
      foregroundColor: AppTheme.textPrimaryColor,
      iconTheme: const IconThemeData(color: AppTheme.textPrimaryColor),
      actionsIconTheme: const IconThemeData(color: AppTheme.textPrimaryColor),
      leading: showBackButton ? _buildWhiteLeading() : null,
      actions: actions,
    );
  }

  /// 构建白色背景下的返回按钮
  Widget? _buildWhiteLeading() {
    if (!(Get.routing.isBack ?? false)) {
      return null;
    }

    return IconButton(
      icon: const Icon(Icons.arrow_back_ios),
      color: AppTheme.textPrimaryColor,
      onPressed: onBackPressed ?? () => Get.back(),
      tooltip: '返回',
    );
  }
}

/// 透明背景的AppBar变体
/// 适用于需要透明背景的页面
class TransparentAppBar extends CommonAppBar {
  const TransparentAppBar({
    super.key,
    required super.title,
    super.showBackButton = true,
    super.onBackPressed,
    super.actions,
    super.centerTitle = true,
    super.titleStyle,
  }) : super(backgroundColor: Colors.transparent, foregroundColor: Colors.white, elevation: 0);
}

/// 智能AppBar组件
/// 根据当前主题服务的设置自动适应样式
class SmartAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// 标题文本
  final String title;

  /// 是否显示返回按钮
  final bool showBackButton;

  /// 自定义返回按钮回调
  final VoidCallback? onBackPressed;

  /// 右侧操作按钮列表
  final List<Widget>? actions;

  /// 是否居中显示标题
  final bool centerTitle;

  /// 阴影高度
  final double elevation;

  /// 标题样式（可选）
  final TextStyle? titleStyle;

  /// 强制使用特定的AppBar样式（可选）
  final AppBarStyle? forceStyle;

  /// 构造函数
  const SmartAppBar({
    super.key,
    required this.title,
    this.showBackButton = true,
    this.onBackPressed,
    this.actions,
    this.centerTitle = true,
    this.elevation = 0,
    this.titleStyle,
    this.forceStyle,
  });

  @override
  Widget build(BuildContext context) {
    final themeService = Get.find<ThemeService>();

    return Obx(() {
      final appBarStyle = forceStyle ?? themeService.currentAppBarStyle;
      final primaryColor = themeService.getThemeTypeColor(themeService.currentThemeType);

      return _buildAppBarByStyle(appBarStyle, primaryColor);
    });
  }

  /// 根据样式构建AppBar
  Widget _buildAppBarByStyle(AppBarStyle style, Color primaryColor) {
    switch (style) {
      case AppBarStyle.colored:
        return _buildColoredAppBar(primaryColor);
      case AppBarStyle.white:
        return _buildWhiteAppBar();
      case AppBarStyle.transparent:
        return _buildTransparentAppBar();
    }
  }

  /// 构建彩色AppBar
  Widget _buildColoredAppBar(Color primaryColor) {
    return AppBar(
      title: Text(
        title,
        style:
            titleStyle ??
            const TextStyle(
              fontSize: AppTheme.fontSizeLarge,
              fontWeight: AppTheme.fontWeightMedium,
              color: Colors.white,
            ),
      ),
      centerTitle: centerTitle,
      elevation: elevation,
      backgroundColor: primaryColor,
      foregroundColor: Colors.white,
      iconTheme: const IconThemeData(color: Colors.white),
      actionsIconTheme: const IconThemeData(color: Colors.white),
      leading: showBackButton ? _buildLeading(Colors.white) : null,
      actions: actions,
    );
  }

  /// 构建白色AppBar
  Widget _buildWhiteAppBar() {
    return AppBar(
      title: Text(
        title,
        style:
            titleStyle ??
            const TextStyle(
              fontSize: AppTheme.fontSizeLarge,
              fontWeight: AppTheme.fontWeightMedium,
              color: AppTheme.textPrimaryColor,
            ),
      ),
      centerTitle: centerTitle,
      elevation: elevation,
      backgroundColor: Colors.white,
      foregroundColor: AppTheme.textPrimaryColor,
      iconTheme: const IconThemeData(color: AppTheme.textPrimaryColor),
      actionsIconTheme: const IconThemeData(color: AppTheme.textPrimaryColor),
      leading: showBackButton ? _buildLeading(AppTheme.textPrimaryColor) : null,
      actions: actions,
    );
  }

  /// 构建透明AppBar
  Widget _buildTransparentAppBar() {
    return AppBar(
      title: Text(
        title,
        style:
            titleStyle ??
            const TextStyle(
              fontSize: AppTheme.fontSizeLarge,
              fontWeight: AppTheme.fontWeightMedium,
              color: Colors.white,
            ),
      ),
      centerTitle: centerTitle,
      elevation: elevation,
      backgroundColor: Colors.transparent,
      foregroundColor: Colors.white,
      iconTheme: const IconThemeData(color: Colors.white),
      actionsIconTheme: const IconThemeData(color: Colors.white),
      leading: showBackButton ? _buildLeading(Colors.white) : null,
      actions: actions,
    );
  }

  /// 构建返回按钮
  Widget? _buildLeading(Color iconColor) {
    if (!(Get.routing.isBack ?? false)) {
      return null;
    }

    return IconButton(
      icon: const Icon(Icons.arrow_back_ios),
      color: iconColor,
      onPressed: onBackPressed ?? () => Get.back(),
      tooltip: '返回',
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

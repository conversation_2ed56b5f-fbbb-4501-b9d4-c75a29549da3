import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';

/// 加载状态组件
/// 提供统一的加载状态显示
class LoadingWidget extends StatelessWidget {
  /// 加载文本
  final String? message;

  /// 加载指示器大小
  final double? size;

  /// 加载指示器颜色
  final Color? color;

  /// 是否显示背景
  final bool showBackground;

  /// 背景颜色
  final Color? backgroundColor;

  const LoadingWidget({
    super.key,
    this.message,
    this.size,
    this.color,
    this.showBackground = false,
    this.backgroundColor,
  });

  /// 全屏加载构造函数
  const LoadingWidget.fullScreen({super.key, this.message, this.size, this.color, this.backgroundColor})
    : showBackground = true;

  /// 小尺寸加载构造函数
  const LoadingWidget.small({super.key, this.message, this.color, this.showBackground = false, this.backgroundColor})
    : size = 20;

  /// 中等尺寸加载构造函数
  const LoadingWidget.medium({super.key, this.message, this.color, this.showBackground = false, this.backgroundColor})
    : size = 30;

  /// 大尺寸加载构造函数
  const LoadingWidget.large({super.key, this.message, this.color, this.showBackground = false, this.backgroundColor})
    : size = 40;

  @override
  Widget build(BuildContext context) {
    Widget loadingContent = _buildLoadingContent();

    if (showBackground) {
      return Container(
        color: backgroundColor ?? Colors.black.withValues(alpha: 0.3),
        child: Center(child: loadingContent),
      );
    }

    return loadingContent;
  }

  /// 构建加载内容
  Widget _buildLoadingContent() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: size ?? 24,
          height: size ?? 24,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(color ?? AppTheme.primaryColor),
          ),
        ),
        if (message != null) ...[
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            message!,
            style: const TextStyle(fontSize: AppTheme.fontSizeNormal, color: AppTheme.textSecondaryColor),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}

/// 错误状态组件
/// 提供统一的错误状态显示
class ErrorWidget extends StatelessWidget {
  /// 错误消息
  final String message;

  /// 错误图标
  final IconData? icon;

  /// 重试按钮文本
  final String? retryText;

  /// 重试回调
  final VoidCallback? onRetry;

  /// 是否显示详细错误信息
  final bool showDetails;

  /// 详细错误信息
  final String? details;

  const ErrorWidget({
    super.key,
    required this.message,
    this.icon,
    this.retryText,
    this.onRetry,
    this.showDetails = false,
    this.details,
  });

  /// 网络错误构造函数
  const ErrorWidget.network({
    super.key,
    this.message = '网络连接失败',
    this.retryText = '重试',
    this.onRetry,
    this.showDetails = false,
    this.details,
  }) : icon = Icons.wifi_off;

  /// 服务器错误构造函数
  const ErrorWidget.server({
    super.key,
    this.message = '服务器错误',
    this.retryText = '重试',
    this.onRetry,
    this.showDetails = false,
    this.details,
  }) : icon = Icons.error_outline;

  /// 数据为空构造函数
  const ErrorWidget.empty({
    super.key,
    this.message = '暂无数据',
    this.retryText,
    this.onRetry,
    this.showDetails = false,
    this.details,
  }) : icon = Icons.inbox_outlined;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingLarge),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon ?? Icons.error_outline, size: 64, color: AppTheme.textHintColor),
            const SizedBox(height: AppTheme.spacingMedium),
            Text(
              message,
              style: const TextStyle(
                fontSize: AppTheme.fontSizeMedium,
                color: AppTheme.textSecondaryColor,
                fontWeight: AppTheme.fontWeightMedium,
              ),
              textAlign: TextAlign.center,
            ),
            if (showDetails && details != null) ...[
              const SizedBox(height: AppTheme.spacingSmall),
              Text(
                details!,
                style: const TextStyle(fontSize: AppTheme.fontSizeSmall, color: AppTheme.textHintColor),
                textAlign: TextAlign.center,
              ),
            ],
            if (onRetry != null) ...[
              const SizedBox(height: AppTheme.spacingLarge),
              ElevatedButton(onPressed: onRetry, child: Text(retryText ?? '重试')),
            ],
          ],
        ),
      ),
    );
  }
}

/// 空状态组件
/// 提供统一的空状态显示
class EmptyWidget extends StatelessWidget {
  /// 空状态消息
  final String message;

  /// 空状态图标
  final IconData? icon;

  /// 操作按钮文本
  final String? actionText;

  /// 操作回调
  final VoidCallback? onAction;

  /// 描述文本
  final String? description;

  const EmptyWidget({super.key, required this.message, this.icon, this.actionText, this.onAction, this.description});

  /// 无数据构造函数
  const EmptyWidget.noData({super.key, this.message = '暂无数据', this.actionText, this.onAction, this.description})
    : icon = Icons.inbox_outlined;

  /// 无搜索结果构造函数
  const EmptyWidget.noSearchResult({
    super.key,
    this.message = '未找到相关内容',
    this.actionText,
    this.onAction,
    this.description,
  }) : icon = Icons.search_off;

  /// 无收藏构造函数
  const EmptyWidget.noFavorites({super.key, this.message = '暂无收藏', this.actionText, this.onAction, this.description})
    : icon = Icons.favorite_border;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingLarge),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon ?? Icons.inbox_outlined, size: 64, color: AppTheme.textHintColor),
            const SizedBox(height: AppTheme.spacingMedium),
            Text(
              message,
              style: const TextStyle(
                fontSize: AppTheme.fontSizeMedium,
                color: AppTheme.textSecondaryColor,
                fontWeight: AppTheme.fontWeightMedium,
              ),
              textAlign: TextAlign.center,
            ),
            if (description != null) ...[
              const SizedBox(height: AppTheme.spacingSmall),
              Text(
                description!,
                style: const TextStyle(fontSize: AppTheme.fontSizeSmall, color: AppTheme.textHintColor),
                textAlign: TextAlign.center,
              ),
            ],
            if (onAction != null) ...[
              const SizedBox(height: AppTheme.spacingLarge),
              ElevatedButton(onPressed: onAction, child: Text(actionText ?? '添加')),
            ],
          ],
        ),
      ),
    );
  }
}

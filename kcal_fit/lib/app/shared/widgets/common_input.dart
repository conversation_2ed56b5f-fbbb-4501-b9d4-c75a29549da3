import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/theme/app_theme.dart';

/// 通用输入框组件
/// 提供统一的输入框样式和验证功能
class CommonInput extends StatefulWidget {
  /// 标签文本
  final String? label;
  
  /// 占位符文本
  final String? placeholder;
  
  /// 帮助文本
  final String? helperText;
  
  /// 错误文本
  final String? errorText;
  
  /// 初始值
  final String? initialValue;
  
  /// 文本控制器
  final TextEditingController? controller;
  
  /// 输入类型
  final TextInputType keyboardType;
  
  /// 输入格式限制
  final List<TextInputFormatter>? inputFormatters;
  
  /// 最大行数
  final int? maxLines;
  
  /// 最大长度
  final int? maxLength;
  
  /// 是否密码输入
  final bool obscureText;
  
  /// 是否只读
  final bool readOnly;
  
  /// 是否启用
  final bool enabled;
  
  /// 是否必填
  final bool required;
  
  /// 前缀图标
  final IconData? prefixIcon;
  
  /// 后缀图标
  final IconData? suffixIcon;
  
  /// 后缀图标点击回调
  final VoidCallback? onSuffixIconTap;
  
  /// 输入变化回调
  final ValueChanged<String>? onChanged;
  
  /// 提交回调
  final ValueChanged<String>? onSubmitted;
  
  /// 焦点变化回调
  final ValueChanged<bool>? onFocusChanged;
  
  /// 验证函数
  final String? Function(String?)? validator;

  const CommonInput({
    super.key,
    this.label,
    this.placeholder,
    this.helperText,
    this.errorText,
    this.initialValue,
    this.controller,
    this.keyboardType = TextInputType.text,
    this.inputFormatters,
    this.maxLines = 1,
    this.maxLength,
    this.obscureText = false,
    this.readOnly = false,
    this.enabled = true,
    this.required = false,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconTap,
    this.onChanged,
    this.onSubmitted,
    this.onFocusChanged,
    this.validator,
  });

  /// 密码输入框构造函数
  const CommonInput.password({
    super.key,
    this.label,
    this.placeholder,
    this.helperText,
    this.errorText,
    this.initialValue,
    this.controller,
    this.inputFormatters,
    this.maxLength,
    this.readOnly = false,
    this.enabled = true,
    this.required = false,
    this.onChanged,
    this.onSubmitted,
    this.onFocusChanged,
    this.validator,
  }) : keyboardType = TextInputType.visiblePassword,
       maxLines = 1,
       obscureText = true,
       prefixIcon = Icons.lock_outline,
       suffixIcon = null,
       onSuffixIconTap = null;

  /// 邮箱输入框构造函数
  const CommonInput.email({
    super.key,
    this.label,
    this.placeholder,
    this.helperText,
    this.errorText,
    this.initialValue,
    this.controller,
    this.inputFormatters,
    this.maxLength,
    this.readOnly = false,
    this.enabled = true,
    this.required = false,
    this.suffixIcon,
    this.onSuffixIconTap,
    this.onChanged,
    this.onSubmitted,
    this.onFocusChanged,
    this.validator,
  }) : keyboardType = TextInputType.emailAddress,
       maxLines = 1,
       obscureText = false,
       prefixIcon = Icons.email_outlined;

  /// 数字输入框构造函数
  const CommonInput.number({
    super.key,
    this.label,
    this.placeholder,
    this.helperText,
    this.errorText,
    this.initialValue,
    this.controller,
    this.inputFormatters,
    this.maxLength,
    this.readOnly = false,
    this.enabled = true,
    this.required = false,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconTap,
    this.onChanged,
    this.onSubmitted,
    this.onFocusChanged,
    this.validator,
  }) : keyboardType = TextInputType.number,
       maxLines = 1,
       obscureText = false;

  /// 多行文本输入框构造函数
  const CommonInput.multiline({
    super.key,
    this.label,
    this.placeholder,
    this.helperText,
    this.errorText,
    this.initialValue,
    this.controller,
    this.inputFormatters,
    this.maxLines = 3,
    this.maxLength,
    this.readOnly = false,
    this.enabled = true,
    this.required = false,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconTap,
    this.onChanged,
    this.onSubmitted,
    this.onFocusChanged,
    this.validator,
  }) : keyboardType = TextInputType.multiline,
       obscureText = false;

  @override
  State<CommonInput> createState() => _CommonInputState();
}

class _CommonInputState extends State<CommonInput> {
  late FocusNode _focusNode;
  late bool _obscureText;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _obscureText = widget.obscureText;
    _errorText = widget.errorText;
    
    _focusNode.addListener(() {
      widget.onFocusChanged?.call(_focusNode.hasFocus);
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(CommonInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.errorText != oldWidget.errorText) {
      setState(() {
        _errorText = widget.errorText;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) _buildLabel(),
        _buildTextField(),
        if (widget.helperText != null && _errorText == null) _buildHelperText(),
        if (_errorText != null) _buildErrorText(),
      ],
    );
  }

  /// 构建标签
  Widget _buildLabel() {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppTheme.spacingSmall),
      child: RichText(
        text: TextSpan(
          text: widget.label,
          style: const TextStyle(
            color: AppTheme.textPrimaryColor,
            fontSize: AppTheme.fontSizeNormal,
            fontWeight: AppTheme.fontWeightMedium,
          ),
          children: [
            if (widget.required)
              const TextSpan(
                text: ' *',
                style: TextStyle(
                  color: AppTheme.errorColor,
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// 构建输入框
  Widget _buildTextField() {
    return TextFormField(
      controller: widget.controller,
      focusNode: _focusNode,
      initialValue: widget.controller == null ? widget.initialValue : null,
      keyboardType: widget.keyboardType,
      inputFormatters: widget.inputFormatters,
      maxLines: widget.maxLines,
      maxLength: widget.maxLength,
      obscureText: _obscureText,
      readOnly: widget.readOnly,
      enabled: widget.enabled,
      decoration: InputDecoration(
        hintText: widget.placeholder,
        prefixIcon: widget.prefixIcon != null ? Icon(widget.prefixIcon) : null,
        suffixIcon: _buildSuffixIcon(),
        errorText: _errorText,
        counterText: '', // 隐藏字符计数器
      ),
      onChanged: (value) {
        // 清除错误信息
        if (_errorText != null) {
          setState(() {
            _errorText = null;
          });
        }
        
        // 实时验证
        if (widget.validator != null) {
          final error = widget.validator!(value);
          if (error != null) {
            setState(() {
              _errorText = error;
            });
          }
        }
        
        widget.onChanged?.call(value);
      },
      onFieldSubmitted: widget.onSubmitted,
      validator: widget.validator,
    );
  }

  /// 构建后缀图标
  Widget? _buildSuffixIcon() {
    if (widget.obscureText) {
      // 密码输入框的显示/隐藏切换图标
      return IconButton(
        icon: Icon(_obscureText ? Icons.visibility : Icons.visibility_off),
        onPressed: () {
          setState(() {
            _obscureText = !_obscureText;
          });
        },
      );
    }
    
    if (widget.suffixIcon != null) {
      return IconButton(
        icon: Icon(widget.suffixIcon),
        onPressed: widget.onSuffixIconTap,
      );
    }
    
    return null;
  }

  /// 构建帮助文本
  Widget _buildHelperText() {
    return Padding(
      padding: const EdgeInsets.only(top: AppTheme.spacingSmall),
      child: Text(
        widget.helperText!,
        style: const TextStyle(
          color: AppTheme.textSecondaryColor,
          fontSize: AppTheme.fontSizeSmall,
        ),
      ),
    );
  }

  /// 构建错误文本
  Widget _buildErrorText() {
    return Padding(
      padding: const EdgeInsets.only(top: AppTheme.spacingSmall),
      child: Text(
        _errorText!,
        style: const TextStyle(
          color: AppTheme.errorColor,
          fontSize: AppTheme.fontSizeSmall,
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';

/// 圆形进度条常量
// ignore: constant_identifier_names
const double CIRCULAR_PROGRESS_SIZE = 140.0; // 圆形进度条的大小
// ignore: constant_identifier_names
const double CIRCULAR_PROGRESS_INNER_SIZE = 120.0; // 圆形进度条内部的大小
// ignore: constant_identifier_names
const double CIRCULAR_PROGRESS_STROKE_WIDTH = 10.0; // 圆形进度条的宽度

/// 圆形进度条组件(非响应式版本)
class CircularProgress extends StatelessWidget {
  final double progress;
  final int consumed;
  final int target;
  final Function() onTap;

  const CircularProgress({
    super.key,
    required this.progress,
    required this.consumed,
    required this.target,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return _buildProgressWidget(context, progress, consumed, target, onTap);
  }
}

/// 构建进度条通用组件
Widget _buildProgressWidget(
  BuildContext context,
  double progress,
  int consumed,
  int target,
  Function() onTap,
) {
  return GestureDetector(
    onTap: onTap,
    behavior: HitTestBehavior.opaque,
    child: InkWell(
      onTap: onTap,
      child: SizedBox(
        width: CIRCULAR_PROGRESS_SIZE,
        height: CIRCULAR_PROGRESS_SIZE,
        child: Stack(
          alignment: Alignment.center,
          children: [
            Container(
              width: CIRCULAR_PROGRESS_INNER_SIZE,
              height: CIRCULAR_PROGRESS_INNER_SIZE,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.blue.withValues(alpha: 0.1),
                    blurRadius: 10,
                    spreadRadius: 3,
                  ),
                ],
              ),
              child: TweenAnimationBuilder<double>(
                tween: Tween<double>(begin: 0, end: progress),
                duration: Duration(milliseconds: 800),
                curve: Curves.easeOut,
                builder: (context, value, _) {
                  return CircularProgressIndicator(
                    value: value,
                    strokeWidth: CIRCULAR_PROGRESS_STROKE_WIDTH,
                    backgroundColor: Colors.grey[100],
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                  );
                },
              ),
            ),
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '$consumed',
                  style: TextStyle(
                    fontSize: 26,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[800],
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  'kcal',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
                SizedBox(height: 4),
                Text(
                  '目标 $target kcal',
                  style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                ),
              ],
            ),
          ],
        ),
      ),
    ),
  );
}

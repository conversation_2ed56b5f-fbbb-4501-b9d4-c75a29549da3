import 'package:flutter/material.dart';

/// 矩形卡片式图标+文字+数值按钮基础组件（静态版本）
class CardIconTextVal extends StatelessWidget {
  final IconData icon; // 图标
  final String label; // 文字
  final int value; // 数值
  final bool isHighlight; // 是否高亮
  final Function() onTap; // 点击事件

  /// 矩形卡片式图标+文字+数值按钮基础组件（静态版本）
  const CardIconTextVal({
    super.key,
    required this.icon,
    required this.label,
    required this.value,
    this.isHighlight = false,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: InkWell(
        onTap: null,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: BoxDecoration(
            color: isHighlight ? Colors.blue[50] : Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                icon,
                size: 16,
                color: isHighlight ? Colors.blue : Colors.grey[600],
              ),
              SizedBox(width: 8),
              Text(
                label,
                style: TextStyle(fontSize: 12, color: Colors.grey[700]),
              ),
              Spacer(),
              Text(
                '$value kcal',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: isHighlight ? FontWeight.bold : FontWeight.normal,
                  color: isHighlight ? Colors.blue : Colors.grey[800],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

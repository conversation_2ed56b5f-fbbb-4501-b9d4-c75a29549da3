import 'package:flutter/material.dart';
import 'package:get/get.dart';

class Demo extends GetWidget<DemoController> {
  final IconData icon;
  final String text;
  final Function() onTap; //将点击事件处理权交给使用组件的父组件
  /// 卡片式图标+文章按钮基础组件
  const Demo({
    super.key,
    required this.icon,
    required this.text,
    required this.onTap,
  });
  @override
  Widget build(BuildContext context) {
    return GestureDetector();
  }
}

class DemoController extends GetxController {}

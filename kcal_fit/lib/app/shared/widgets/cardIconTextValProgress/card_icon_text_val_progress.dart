import 'package:flutter/material.dart';

class CardIconTextValProgress extends StatelessWidget {
  final String label; // 标签
  final int value; // 数值
  final int progress; // 进度
  final Color color; // 颜色
  final IconData icon; // 图标
  final String unit; // 单位
  final Function() onTap; // 点击事件

  /// 卡片式图标+文字+进度按钮基础组件
  const CardIconTextValProgress({
    super.key,
    required this.label,
    required this.value,
    required this.progress,
    required this.color,
    required this.icon,
    required this.onTap,
    required this.unit,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: InkWell(
        onTap: onTap, // 避免重复定义，直接使用外部的 onTap
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(color: Colors.grey[50], borderRadius: BorderRadius.circular(8)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(icon, color: color),
                  SizedBox(width: 4),
                  Text(label, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
                  Spacer(),
                  Text(
                    '$value/$progress $unit',
                    style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600, color: Colors.grey[800]),
                  ),
                ],
              ),
              SizedBox(height: 4),
              Builder(
                builder: (context) {
                  final double ratio = progress > 0 ? value / progress : 0; // 避免除以零
                  return TweenAnimationBuilder<double>(
                    tween: Tween<double>(begin: 0, end: ratio),
                    duration: Duration(milliseconds: 800),
                    curve: Curves.easeOut,
                    builder: (context, animValue, _) {
                      return LinearProgressIndicator(
                        value: animValue,
                        backgroundColor: Colors.grey[200],
                        valueColor: AlwaysStoppedAnimation<Color>(color),
                        minHeight: 4,
                        borderRadius: BorderRadius.circular(2),
                      );
                    },
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}

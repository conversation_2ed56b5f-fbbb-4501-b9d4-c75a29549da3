import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';

/// 通用卡片组件
/// 提供统一的卡片样式和布局
class CommonCard extends StatelessWidget {
  /// 卡片内容
  final Widget child;

  /// 卡片标题
  final String? title;

  /// 标题样式
  final TextStyle? titleStyle;

  /// 卡片内边距
  final EdgeInsetsGeometry? padding;

  /// 卡片外边距
  final EdgeInsetsGeometry? margin;

  /// 卡片背景色
  final Color? backgroundColor;

  /// 卡片阴影
  final double? elevation;

  /// 卡片圆角
  final double? borderRadius;

  /// 边框
  final Border? border;

  /// 点击回调
  final VoidCallback? onTap;

  /// 长按回调
  final VoidCallback? onLongPress;

  /// 是否显示分割线
  final bool showDivider;

  /// 操作按钮列表
  final List<Widget>? actions;

  const CommonCard({
    super.key,
    required this.child,
    this.title,
    this.titleStyle,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.elevation,
    this.borderRadius,
    this.border,
    this.onTap,
    this.onLongPress,
    this.showDivider = false,
    this.actions,
  });

  /// 信息卡片构造函数
  const CommonCard.info({
    super.key,
    required this.child,
    this.title,
    this.titleStyle,
    this.padding,
    this.margin,
    this.elevation,
    this.borderRadius,
    this.border,
    this.onTap,
    this.onLongPress,
    this.showDivider = false,
    this.actions,
  }) : backgroundColor = AppTheme.infoColor;

  /// 成功卡片构造函数
  const CommonCard.success({
    super.key,
    required this.child,
    this.title,
    this.titleStyle,
    this.padding,
    this.margin,
    this.elevation,
    this.borderRadius,
    this.border,
    this.onTap,
    this.onLongPress,
    this.showDivider = false,
    this.actions,
  }) : backgroundColor = AppTheme.successColor;

  /// 警告卡片构造函数
  const CommonCard.warning({
    super.key,
    required this.child,
    this.title,
    this.titleStyle,
    this.padding,
    this.margin,
    this.elevation,
    this.borderRadius,
    this.border,
    this.onTap,
    this.onLongPress,
    this.showDivider = false,
    this.actions,
  }) : backgroundColor = AppTheme.warningColor;

  /// 错误卡片构造函数
  const CommonCard.error({
    super.key,
    required this.child,
    this.title,
    this.titleStyle,
    this.padding,
    this.margin,
    this.elevation,
    this.borderRadius,
    this.border,
    this.onTap,
    this.onLongPress,
    this.showDivider = false,
    this.actions,
  }) : backgroundColor = AppTheme.errorColor;

  @override
  Widget build(BuildContext context) {
    Widget cardContent = _buildCardContent();

    if (onTap != null || onLongPress != null) {
      cardContent = InkWell(
        onTap: onTap,
        onLongPress: onLongPress,
        borderRadius: BorderRadius.circular(borderRadius ?? AppTheme.cardRadius),
        child: cardContent,
      );
    }

    return Container(
      margin: margin ?? const EdgeInsets.all(AppTheme.cardMargin),
      decoration: BoxDecoration(
        color: backgroundColor ?? AppTheme.cardColor,
        borderRadius: BorderRadius.circular(borderRadius ?? AppTheme.cardRadius),
        border: border,
        boxShadow:
            elevation != null
                ? [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: elevation!,
                    offset: Offset(0, elevation! / 2),
                  ),
                ]
                : [BoxShadow(color: Colors.black.withValues(alpha: 0.05), blurRadius: 4, offset: const Offset(0, 2))],
      ),
      child: ClipRRect(borderRadius: BorderRadius.circular(borderRadius ?? AppTheme.cardRadius), child: cardContent),
    );
  }

  /// 构建卡片内容
  Widget _buildCardContent() {
    final contentPadding = padding ?? const EdgeInsets.all(AppTheme.cardPadding);

    if (title == null && actions == null) {
      return Padding(padding: contentPadding, child: child);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null || actions != null) _buildHeader(),
        if (showDivider && (title != null || actions != null)) const Divider(height: 1),
        Padding(padding: contentPadding, child: child),
      ],
    );
  }

  /// 构建卡片头部
  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(AppTheme.cardPadding, AppTheme.cardPadding, AppTheme.cardPadding, 0),
      child: Row(
        children: [
          if (title != null)
            Expanded(
              child: Text(
                title!,
                style:
                    titleStyle ??
                    const TextStyle(
                      fontSize: AppTheme.fontSizeMedium,
                      fontWeight: AppTheme.fontWeightMedium,
                      color: AppTheme.textPrimaryColor,
                    ),
              ),
            ),
          if (actions != null) ...[if (title != null) const SizedBox(width: AppTheme.spacingMedium), ...actions!],
        ],
      ),
    );
  }
}

/// 列表卡片组件
/// 用于显示列表项的卡片
class ListCard extends StatelessWidget {
  /// 标题
  final String title;

  /// 副标题
  final String? subtitle;

  /// 描述
  final String? description;

  /// 前导图标
  final Widget? leading;

  /// 尾随图标
  final Widget? trailing;

  /// 点击回调
  final VoidCallback? onTap;

  /// 卡片内边距
  final EdgeInsetsGeometry? padding;

  /// 卡片外边距
  final EdgeInsetsGeometry? margin;

  const ListCard({
    super.key,
    required this.title,
    this.subtitle,
    this.description,
    this.leading,
    this.trailing,
    this.onTap,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return CommonCard(
      padding: padding ?? const EdgeInsets.all(AppTheme.spacingMedium),
      margin: margin,
      onTap: onTap,
      child: Row(
        children: [
          if (leading != null) ...[leading!, const SizedBox(width: AppTheme.spacingMedium)],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: AppTheme.fontSizeNormal,
                    fontWeight: AppTheme.fontWeightMedium,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: AppTheme.spacingXSmall),
                  Text(
                    subtitle!,
                    style: const TextStyle(fontSize: AppTheme.fontSizeSmall, color: AppTheme.textSecondaryColor),
                  ),
                ],
                if (description != null) ...[
                  const SizedBox(height: AppTheme.spacingXSmall),
                  Text(
                    description!,
                    style: const TextStyle(fontSize: AppTheme.fontSizeSmall, color: AppTheme.textHintColor),
                  ),
                ],
              ],
            ),
          ),
          if (trailing != null) ...[const SizedBox(width: AppTheme.spacingMedium), trailing!],
        ],
      ),
    );
  }
}

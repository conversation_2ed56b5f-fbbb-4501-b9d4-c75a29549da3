import 'package:get/get.dart';

import '../../../core/utils/logger_util.dart';
import '../../../core/utils/message_util.dart';
import '../../../core/utils/state_manager.dart';

/// 精简的基础控制器
/// 提供核心功能，避免过度抽象
abstract class BaseController extends GetxController with StateManager {
  /// 显示成功消息
  void showSuccess(String message) => MessageUtil.success(message);

  /// 显示错误消息
  void showError(String message) => MessageUtil.error(message);

  /// 显示警告消息
  void showWarning(String message) => MessageUtil.warning(message);

  /// 显示信息消息
  void showInfo(String message) => MessageUtil.info(message);

  /// 显示确认对话框
  Future<bool> showConfirm({
    required String title,
    required String message,
    String confirmText = '确认',
    String cancelText = '取消',
  }) => MessageUtil.confirm(title: title, message: message, confirmText: confirmText, cancelText: cancelText);

  /// 处理网络错误
  void handleNetworkError(dynamic error) {
    final message = _getErrorMessage(error);
    setError(message);
    LoggerUtil.e('网络错误', error);
  }

  /// 获取错误消息
  String _getErrorMessage(dynamic error) {
    final errorStr = error.toString();
    if (errorStr.contains('timeout')) return '请求超时，请稍后重试';
    if (errorStr.contains('404')) return '请求的资源不存在';
    if (errorStr.contains('500')) return '服务器内部错误';
    if (errorStr.contains('401')) return '身份验证失败，请重新登录';
    if (errorStr.contains('403')) return '没有权限访问该资源';
    return '网络连接失败，请检查网络设置';
  }

  /// 刷新数据 - 子类重写
  @override
  Future<void> refresh() async {
    resetStates();
    await onRefresh();
  }

  /// 子类实现的刷新方法
  Future<void> onRefresh() async {}

  @override
  void onInit() {
    super.onInit();
    LoggerUtil.lifecycle(runtimeType.toString(), '初始化');
  }

  @override
  void onReady() {
    super.onReady();
    LoggerUtil.lifecycle(runtimeType.toString(), '就绪');
  }

  @override
  void onClose() {
    LoggerUtil.lifecycle(runtimeType.toString(), '销毁');
    super.onClose();
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/services/theme_service.dart';
import '../../../core/theme/app_theme.dart';
import 'base_controller.dart';

/// 主题控制器
/// 负责管理主题相关的UI状态和用户交互
class ThemeController extends BaseController {
  late final ThemeService _themeService;

  /// 当前选中的主题类型
  final Rx<ThemeType> _selectedThemeType = ThemeType.blue.obs;

  /// 当前选中的AppBar样式
  final Rx<AppBarStyle> _selectedAppBarStyle = AppBarStyle.colored.obs;

  /// 是否正在保存设置
  final RxBool _isSaving = false.obs;

  /// 获取当前选中的主题类型
  ThemeType get selectedThemeType => _selectedThemeType.value;

  /// 获取当前选中的AppBar样式
  AppBarStyle get selectedAppBarStyle => _selectedAppBarStyle.value;

  /// 是否正在保存
  bool get isSaving => _isSaving.value;

  /// 主题类型变化流
  Rx<ThemeType> get themeTypeStream => _selectedThemeType;

  /// AppBar样式变化流
  Rx<AppBarStyle> get appBarStyleStream => _selectedAppBarStyle;

  @override
  void onInit() {
    super.onInit();
    _themeService = Get.find<ThemeService>();
    _initializeThemeSettings();
  }

  /// 初始化主题设置
  void _initializeThemeSettings() {
    // 同步当前主题服务的设置
    _selectedThemeType.value = _themeService.currentThemeType;
    _selectedAppBarStyle.value = _themeService.currentAppBarStyle;

    // 监听主题服务的变化
    _themeService.themeTypeStream.listen((themeType) {
      if (_selectedThemeType.value != themeType) {
        _selectedThemeType.value = themeType;
      }
    });

    _themeService.appBarStyleStream.listen((appBarStyle) {
      if (_selectedAppBarStyle.value != appBarStyle) {
        _selectedAppBarStyle.value = appBarStyle;
      }
    });
  }

  /// 选择主题类型
  void selectThemeType(ThemeType themeType) {
    if (_selectedThemeType.value != themeType) {
      _selectedThemeType.value = themeType;
    }
  }

  /// 选择AppBar样式
  void selectAppBarStyle(AppBarStyle appBarStyle) {
    if (_selectedAppBarStyle.value != appBarStyle) {
      _selectedAppBarStyle.value = appBarStyle;
    }
  }

  /// 应用主题设置
  Future<void> applyThemeSettings() async {
    try {
      _isSaving.value = true;

      await _themeService.changeTheme(themeType: _selectedThemeType.value, appBarStyle: _selectedAppBarStyle.value);

      // 显示成功提示
      Get.snackbar('设置成功', '主题设置已保存', snackPosition: SnackPosition.BOTTOM, duration: const Duration(seconds: 2));
    } catch (e) {
      Get.snackbar('设置失败', '主题设置保存失败: $e', snackPosition: SnackPosition.BOTTOM, duration: const Duration(seconds: 2));
    } finally {
      _isSaving.value = false;
    }
  }

  /// 重置为默认主题
  Future<void> resetToDefault() async {
    try {
      _isSaving.value = true;

      await _themeService.resetToDefault();

      // 更新本地状态
      _selectedThemeType.value = ThemeType.blue;
      _selectedAppBarStyle.value = AppBarStyle.colored;

      // 显示成功提示
      Get.snackbar('重置成功', '已重置为默认主题', snackPosition: SnackPosition.BOTTOM, duration: const Duration(seconds: 2));
    } catch (e) {
      Get.snackbar('重置失败', '重置主题失败: $e', snackPosition: SnackPosition.BOTTOM, duration: const Duration(seconds: 2));
    } finally {
      _isSaving.value = false;
    }
  }

  /// 预览主题（临时应用，不保存）
  void previewTheme({ThemeType? themeType, AppBarStyle? appBarStyle}) {
    final previewThemeType = themeType ?? _selectedThemeType.value;
    final previewAppBarStyle = appBarStyle ?? _selectedAppBarStyle.value;

    final previewTheme = AppTheme.getTheme(themeType: previewThemeType, appBarStyle: previewAppBarStyle);

    Get.changeTheme(previewTheme);
  }

  /// 取消预览，恢复当前保存的主题
  void cancelPreview() {
    final currentTheme = _themeService.getCurrentTheme();
    Get.changeTheme(currentTheme);
  }

  /// 获取所有可用的主题类型
  List<ThemeType> get availableThemeTypes => _themeService.availableThemeTypes;

  /// 获取所有可用的AppBar样式
  List<AppBarStyle> get availableAppBarStyles => _themeService.availableAppBarStyles;

  /// 获取主题类型的显示名称
  String getThemeTypeName(ThemeType themeType) {
    return _themeService.getThemeTypeName(themeType);
  }

  /// 获取AppBar样式的显示名称
  String getAppBarStyleName(AppBarStyle appBarStyle) {
    return _themeService.getAppBarStyleName(appBarStyle);
  }

  /// 获取主题类型的主色调
  Color getThemeTypeColor(ThemeType themeType) {
    return _themeService.getThemeTypeColor(themeType);
  }

  /// 检查是否有未保存的更改
  bool get hasUnsavedChanges {
    return _selectedThemeType.value != _themeService.currentThemeType ||
        _selectedAppBarStyle.value != _themeService.currentAppBarStyle;
  }

  /// 撤销更改
  void discardChanges() {
    _selectedThemeType.value = _themeService.currentThemeType;
    _selectedAppBarStyle.value = _themeService.currentAppBarStyle;
    cancelPreview();
  }
}

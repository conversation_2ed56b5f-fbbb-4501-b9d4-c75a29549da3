import 'package:flutter/material.dart';
import 'package:health/health.dart';

/// 健康数据类型枚举
enum HealthDataTypeEnum {
  steps,
  distance,
  flights,
  activeCalories,
  basalCalories,
  totalCalories,
}

/// 健康数据类型配置
class HealthDataTypeConfig {
  final String title;
  final String unit;
  final IconData icon;
  final Color color;
  final String chartTitle;
  final String emptyMessage;
  final HealthDataType? healthKitType;

  const HealthDataTypeConfig({
    required this.title,
    required this.unit,
    required this.icon,
    required this.color,
    required this.chartTitle,
    required this.emptyMessage,
    this.healthKitType,
  });
}

/// 健康数据类型配置映射
class HealthDataTypeConfigs {
  static const Map<HealthDataTypeEnum, HealthDataTypeConfig> configs = {
    HealthDataTypeEnum.steps: HealthDataTypeConfig(
      title: '今日步数详情',
      unit: '步',
      icon: Icons.directions_walk,
      color: Colors.purple,
      chartTitle: '步数趋势图',
      emptyMessage: '暂无步数数据',
      healthKitType: HealthDataType.STEPS,
    ),
    HealthDataTypeEnum.distance: HealthDataTypeConfig(
      title: '步行距离详情',
      unit: '公里',
      icon: Icons.straighten,
      color: Colors.blue,
      chartTitle: '距离趋势图',
      emptyMessage: '暂无距离数据',
      healthKitType: HealthDataType.DISTANCE_WALKING_RUNNING,
    ),
    HealthDataTypeEnum.flights: HealthDataTypeConfig(
      title: '爬楼层数详情',
      unit: '层',
      icon: Icons.stairs,
      color: Colors.orange,
      chartTitle: '楼层趋势图',
      emptyMessage: '暂无楼层数据',
      healthKitType: HealthDataType.FLIGHTS_CLIMBED,
    ),
    HealthDataTypeEnum.activeCalories: HealthDataTypeConfig(
      title: '活动消耗详情',
      unit: 'kcal',
      icon: Icons.directions_run,
      color: Colors.blue,
      chartTitle: '活动消耗趋势图',
      emptyMessage: '暂无活动消耗数据',
      healthKitType: HealthDataType.ACTIVE_ENERGY_BURNED,
    ),
    HealthDataTypeEnum.basalCalories: HealthDataTypeConfig(
      title: '基础代谢详情',
      unit: 'kcal',
      icon: Icons.favorite,
      color: Colors.green,
      chartTitle: '基础代谢趋势图',
      emptyMessage: '暂无基础代谢数据',
      healthKitType: HealthDataType.BASAL_ENERGY_BURNED,
    ),
    HealthDataTypeEnum.totalCalories: HealthDataTypeConfig(
      title: '总消耗详情',
      unit: 'kcal',
      icon: Icons.local_fire_department,
      color: Colors.orange,
      chartTitle: '总消耗趋势图',
      emptyMessage: '暂无消耗数据',
      healthKitType: null, // 总消耗是计算值，不是直接的HealthKit类型
    ),
  };

  /// 获取配置
  static HealthDataTypeConfig getConfig(HealthDataTypeEnum type) {
    return configs[type]!;
  }

  /// 获取颜色
  static Color getColor(HealthDataTypeEnum type) {
    return configs[type]!.color;
  }

  /// 获取图标
  static IconData getIcon(HealthDataTypeEnum type) {
    return configs[type]!.icon;
  }

  /// 获取标题
  static String getTitle(HealthDataTypeEnum type) {
    return configs[type]!.title;
  }

  /// 获取单位
  static String getUnit(HealthDataTypeEnum type) {
    return configs[type]!.unit;
  }
}

// To parse this JSON data, do
//
//     final analysisData = analysisDataFromJson(jsonString);

import 'dart:convert';

AnalysisData analysisDataFromJson(String str) =>
    AnalysisData.fromJson(json.decode(str));

String analysisDataToJson(AnalysisData data) => json.encode(data.toJson());

class AnalysisData {
  Data data;

  AnalysisData({required this.data});

  factory AnalysisData.fromJson(Map<String, dynamic> json) =>
      AnalysisData(data: Data.fromJson(json["data"]));

  Map<String, dynamic> toJson() => {"data": data.toJson()};
}

class Data {
  List<Food> foods;
  String content;

  Data({required this.foods, required this.content});

  factory Data.fromJson(Map<String, dynamic> json) => Data(
    foods: List<Food>.from(json["foods"].map((x) => Food.fromJson(x))),
    content: json["content"],
  );

  Map<String, dynamic> toJson() => {
    "foods": List<dynamic>.from(foods.map((x) => x.toJson())),
    "content": content,
  };
}

class Food {
  String name;
  Quantity quantity;
  Macronutrients macronutrients;
  List<Micronutrient> micronutrients;

  Food({
    required this.name,
    required this.quantity,
    required this.macronutrients,
    required this.micronutrients,
  });

  factory Food.fromJson(Map<String, dynamic> json) => Food(
    name: json["name"],
    quantity: Quantity.fromJson(json["quantity"]),
    macronutrients: Macronutrients.fromJson(json["macronutrients"]),
    micronutrients: List<Micronutrient>.from(
      json["micronutrients"].map((x) => Micronutrient.fromJson(x)),
    ),
  );

  Map<String, dynamic> toJson() => {
    "name": name,
    "quantity": quantity.toJson(),
    "macronutrients": macronutrients.toJson(),
    "micronutrients": List<dynamic>.from(micronutrients.map((x) => x.toJson())),
  };
}

class Macronutrients {
  Quantity energy;
  Quantity protein;
  Quantity carbohydrates;
  Quantity fat;

  Macronutrients({
    required this.energy,
    required this.protein,
    required this.carbohydrates,
    required this.fat,
  });

  factory Macronutrients.fromJson(Map<String, dynamic> json) => Macronutrients(
    energy: Quantity.fromJson(json["energy"]),
    protein: Quantity.fromJson(json["protein"]),
    carbohydrates: Quantity.fromJson(json["carbohydrates"]),
    fat: Quantity.fromJson(json["fat"]),
  );

  Map<String, dynamic> toJson() => {
    "energy": energy.toJson(),
    "protein": protein.toJson(),
    "carbohydrates": carbohydrates.toJson(),
    "fat": fat.toJson(),
  };
}

class Quantity {
  double value;
  Unit unit;

  Quantity({required this.value, required this.unit});

  factory Quantity.fromJson(Map<String, dynamic> json) =>
  // 修改此处，将值转换为 double 类型
  Quantity(
    value: json["value"]?.toDouble() ?? 0.0,
    unit: unitValues.map[json["unit"]]!,
  );

  Map<String, dynamic> toJson() => {
    "value": value,
    "unit": unitValues.reverse[unit],
  };
}

// ignore: constant_identifier_names
enum Unit { G, KCAL, ML }

final unitValues = EnumValues({"g": Unit.G, "kcal": Unit.KCAL, "ml": Unit.ML});

class Micronutrient {
  String name;
  double value;
  String unit;
  String dailyValue;

  Micronutrient({
    required this.name,
    required this.value,
    required this.unit,
    required this.dailyValue,
  });

  factory Micronutrient.fromJson(Map<String, dynamic> json) => Micronutrient(
    name: json["name"],
    value: json["value"]?.toDouble(),
    unit: json["unit"],
    dailyValue: json["daily_value"],
  );

  Map<String, dynamic> toJson() => {
    "name": name,
    "value": value,
    "unit": unit,
    "daily_value": dailyValue,
  };
}

class EnumValues<T> {
  Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}

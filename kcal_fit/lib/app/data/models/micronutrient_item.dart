import 'dart:convert';

class MicronutrientItem {
  dynamic value;
  String unit;

  MicronutrientItem({required this.value, required this.unit});

  factory MicronutrientItem.fromRawJson(String str) =>
      MicronutrientItem.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MicronutrientItem.fromJson(Map<String, dynamic> json) =>
      MicronutrientItem(value: json["value"], unit: json["unit"]);

  Map<String, dynamic> toJson() => {"value": value, "unit": unit};
}

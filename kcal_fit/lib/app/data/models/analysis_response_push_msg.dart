import 'package:json_annotation/json_annotation.dart';

part 'analysis_response_push_msg.g.dart';

/// 最外层响应模型
@JsonSerializable()
class AnalysisResponsePushMsg {
  final String title;
  final String content;

  @JsonKey(name: 'content_type')
  final String contentType;

  @JsonKey(name: '_j_msgid')
  final int msgId;

  final Extras extras;

  AnalysisResponsePushMsg({
    required this.title, // 分析标题(分析ID)
    required this.content, // 分析内容
    required this.contentType, // 内容类型
    required this.msgId, // 消息ID
    required this.extras, // 额外信息
  });

  factory AnalysisResponsePushMsg.fromJson(Map<String, dynamic> json) =>
      _$AnalysisResponsePushMsgFromJson(json);
  Map<String, dynamic> toJson() => _$AnalysisResponsePushMsgToJson(this);
}

/// 中间层数据容器
@JsonSerializable()
class Extras {
  final String content; // 可能为空字符串
  final String error; // 根据数据示例可能为空字符串
  final List<Food> foods; // 食物列表

  @JsonKey(name: 'micronutrientTotals')
  final Map<String, Macronutrient>? micronutrientTotals; // 微量营养素总计

  @JsonKey(name: 'macronutrientTotals')
  final Macronutrients? macronutrientTotals; // 宏量营养素总计

  Extras({
    required this.content,
    required this.error,
    required this.foods,
    this.micronutrientTotals,
    this.macronutrientTotals,
  });

  factory Extras.fromJson(Map<String, dynamic> json) => _$ExtrasFromJson(json);
  Map<String, dynamic> toJson() => _$ExtrasToJson(this);
}

/// 食物主体模型
@JsonSerializable()
class Food {
  final String name; // 食物名称
  final Quantity quantity; // 食物数量
  final List<Micronutrient> micronutrients; // 微量营养素列表
  final Macronutrients macronutrients; // 宏量营养素容器

  Food({
    required this.name,
    required this.quantity,
    required this.micronutrients,
    required this.macronutrients,
  });

  factory Food.fromJson(Map<String, dynamic> json) => _$FoodFromJson(json);
  Map<String, dynamic> toJson() => _$FoodToJson(this);
}

/// 通用数值单位模型
@JsonSerializable()
class Quantity {
  final double value; // 数值
  final String unit; // 单位

  Quantity({required this.value, required this.unit});

  factory Quantity.fromJson(Map<String, dynamic> json) =>
      _$QuantityFromJson(json);
  Map<String, dynamic> toJson() => _$QuantityToJson(this);
}

/// 微量营养素模型
@JsonSerializable()
class Micronutrient {
  final String name; // 名称
  final String unit; // 单位
  final double value; // 数值

  @JsonKey(name: 'daily_value')
  final String dailyValue;

  Micronutrient({
    required this.name,
    required this.unit,
    required this.value,
    required this.dailyValue,
  });

  factory Micronutrient.fromJson(Map<String, dynamic> json) =>
      _$MicronutrientFromJson(json);
  Map<String, dynamic> toJson() => _$MicronutrientToJson(this);
}

/// 宏量营养素容器
@JsonSerializable()
class Macronutrients {
  final Macronutrient protein; // 蛋白质
  final Macronutrient fat; // 脂肪
  final Macronutrient carbohydrates; // 碳水化合物
  final Macronutrient energy; // 能量

  Macronutrients({
    required this.protein,
    required this.fat,
    required this.carbohydrates,
    required this.energy,
  });

  factory Macronutrients.fromJson(Map<String, dynamic> json) =>
      _$MacronutrientsFromJson(json);
  Map<String, dynamic> toJson() => _$MacronutrientsToJson(this);
}

/// 宏量营养素单项
@JsonSerializable()
class Macronutrient {
  final double value; // 数值
  final String unit; // 单位

  Macronutrient({required this.value, required this.unit});

  factory Macronutrient.fromJson(Map<String, dynamic> json) =>
      _$MacronutrientFromJson(json);
  Map<String, dynamic> toJson() => _$MacronutrientToJson(this);
}

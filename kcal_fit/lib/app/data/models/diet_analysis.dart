/// 饮食分析数据模型类
/// 对应数据库中的 diet_analysis 表，用于封装饮食记录和分析数据
class DietAnalysis {
  final int? id;
  final String analyzeId;
  final DateTime mealTime;
  final String? mealType;
  final String dietDescription;
  final int? calories;
  final int? protein;
  final int? carbs;
  final int? fat;
  final int? fiber;
  final int? sugar;
  final int? sodium;
  final int? cholesterol;
  final int? vitaminA;
  final int? thiamine;
  final int? riboflavin;
  final int? niacin;
  final int? pyridoxine;
  final int? cobalamin;
  final int? folate;
  final int? vitaminC;
  final int? vitaminD;
  final int? vitaminE;
  final int? vitaminK;
  final int? calcium;
  final int? iron;
  final int? magnesium;
  final int? phosphorus;
  final int? potassium;
  final int? zinc;
  final int? copper;
  final int? manganese;
  final int? selenium;
  final int? iodine;
  final int? chromium;
  final int? molybdenum;
  final int? omega3;
  final int? omega6;
  final int? transFat;
  final int? saturatedFat;
  final int? monounsaturatedFat;
  final int? polyunsaturatedFat;
  final String? tags;
  final String? notes;
  final String? photoPath;
  final DateTime createdTime;
  final DateTime updateTime;

  DietAnalysis({
    this.id,
    required this.analyzeId,
    required this.mealTime,
    this.mealType,
    required this.dietDescription,
    this.calories,
    this.protein,
    this.carbs,
    this.fat,
    this.fiber,
    this.sugar,
    this.sodium,
    this.cholesterol,
    this.vitaminA,
    this.thiamine,
    this.riboflavin,
    this.niacin,
    this.pyridoxine,
    this.cobalamin,
    this.folate,
    this.vitaminC,
    this.vitaminD,
    this.vitaminE,
    this.vitaminK,
    this.calcium,
    this.iron,
    this.magnesium,
    this.phosphorus,
    this.potassium,
    this.zinc,
    this.copper,
    this.manganese,
    this.selenium,
    this.iodine,
    this.chromium,
    this.molybdenum,
    this.omega3,
    this.omega6,
    this.transFat,
    this.saturatedFat,
    this.monounsaturatedFat,
    this.polyunsaturatedFat,
    this.tags,
    this.notes,
    this.photoPath,
    required this.createdTime,
    required this.updateTime,
  });

  factory DietAnalysis.fromMap(Map<String, dynamic> map) {
    return DietAnalysis(
      id: map['id'],
      analyzeId: map['analyze_id'],
      mealTime: DateTime.parse(map['meal_time']),
      mealType: map['meal_type'],
      dietDescription: map['diet_description'],
      calories: map['calories'] ?? 0,
      protein: map['protein'] ?? 0,
      carbs: map['carbs'] ?? 0,
      fat: map['fat'] ?? 0,
      fiber: map['fiber'] ?? 0,
      sugar: map['sugar'] ?? 0,
      sodium: map['sodium'] ?? 0,
      cholesterol: map['cholesterol'] ?? 0,
      vitaminA: map['vitamin_a'] ?? 0,
      thiamine: map['thiamine'] ?? 0,
      riboflavin: map['riboflavin'] ?? 0,
      niacin: map['niacin'] ?? 0,
      pyridoxine: map['pyridoxine'] ?? 0,
      cobalamin: map['cobalamin'] ?? 0,
      folate: map['folate'] ?? 0,
      vitaminC: map['vitamin_c'] ?? 0,
      vitaminD: map['vitamin_d'] ?? 0,
      vitaminE: map['vitamin_e'] ?? 0,
      vitaminK: map['vitamin_k'] ?? 0,
      calcium: map['calcium'],
      iron: map['iron'] ?? 0,
      magnesium: map['magnesium'] ?? 0,
      phosphorus: map['phosphorus'] ?? 0,
      potassium: map['potassium'] ?? 0,
      zinc: map['zinc'] ?? 0,
      copper: map['copper'] ?? 0,
      manganese: map['manganese'] ?? 0,
      selenium: map['selenium'] ?? 0,
      iodine: map['iodine'] ?? 0,
      chromium: map['chromium'] ?? 0,
      molybdenum: map['molybdenum'] ?? 0,
      omega3: map['omega3'] ?? 0,
      omega6: map['omega6'] ?? 0,
      transFat: map['trans_fat'] ?? 0,
      saturatedFat: map['saturated_fat'] ?? 0,
      monounsaturatedFat: map['monounsaturated_fat'] ?? 0,
      polyunsaturatedFat: map['polyunsaturated_fat'] ?? 0,
      tags: map['tags'],
      notes: map['notes'],
      photoPath: map['photo_path'],
      createdTime: DateTime.parse(map['created_time']),
      updateTime: DateTime.parse(map['update_time']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'analyze_id': analyzeId,
      'meal_time': mealTime.toIso8601String(),
      'meal_type': mealType,
      'diet_description': dietDescription,
      'calories': calories,
      'protein': protein,
      'carbs': carbs,
      'fat': fat,
      'fiber': fiber,
      'sugar': sugar,
      'sodium': sodium,
      'cholesterol': cholesterol,
      'vitamin_a': vitaminA,
      'thiamine': thiamine,
      'riboflavin': riboflavin,
      'niacin': niacin,
      'pyridoxine': pyridoxine,
      'cobalamin': cobalamin,
      'folate': folate,
      'vitamin_c': vitaminC,
      'vitamin_d': vitaminD,
      'vitamin_e': vitaminE,
      'vitamin_k': vitaminK,
      'calcium': calcium,
      'iron': iron,
      'magnesium': magnesium,
      'phosphorus': phosphorus,
      'potassium': potassium,
      'zinc': zinc,
      'copper': copper,
      'manganese': manganese,
      'selenium': selenium,
      'iodine': iodine,
      'chromium': chromium,
      'molybdenum': molybdenum,
      'omega3': omega3,
      'omega6': omega6,
      'trans_fat': transFat,
      'saturated_fat': saturatedFat,
      'monounsaturated_fat': monounsaturatedFat,
      'polyunsaturated_fat': polyunsaturatedFat,
      'tags': tags,
      'notes': notes,
      'photo_path': photoPath,
      'created_time': createdTime.toIso8601String(),
      'update_time': updateTime.toIso8601String(),
    };
  }
}

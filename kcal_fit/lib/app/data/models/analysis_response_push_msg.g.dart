// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'analysis_response_push_msg.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AnalysisResponsePushMsg _$AnalysisResponsePushMsgFromJson(Map<String, dynamic> json) => AnalysisResponsePushMsg(
  title: json['title'] as String,
  content: json['content'] as String,
  contentType: json['content_type'] as String,
  msgId: (json['_j_msgid'] as num).toInt(),
  extras: Extras.fromJson(json['extras'] as Map<String, dynamic>),
);

Map<String, dynamic> _$AnalysisResponsePushMsgToJson(AnalysisResponsePushMsg instance) => <String, dynamic>{
  'title': instance.title,
  'content': instance.content,
  'content_type': instance.contentType,
  '_j_msgid': instance.msgId,
  'extras': instance.extras,
};

Extras _$ExtrasFromJson(Map<String, dynamic> json) => Extras(
  content: json['content'] as String,
  error: json['error'] as String,
  foods: (json['foods'] as List<dynamic>).map((e) => Food.fromJson(e as Map<String, dynamic>)).toList(),
  micronutrientTotals: (json['micronutrientTotals'] as Map<String, dynamic>?)?.map(
    (k, e) => MapEntry(k, Macronutrient.fromJson(e as Map<String, dynamic>)),
  ),
  macronutrientTotals:
      json['macronutrientTotals'] == null
          ? null
          : Macronutrients.fromJson(json['macronutrientTotals'] as Map<String, dynamic>),
);

Map<String, dynamic> _$ExtrasToJson(Extras instance) => <String, dynamic>{
  'content': instance.content,
  'error': instance.error,
  'foods': instance.foods,
  'micronutrientTotals': instance.micronutrientTotals,
  'macronutrientTotals': instance.macronutrientTotals,
};

Food _$FoodFromJson(Map<String, dynamic> json) => Food(
  name: json['name'] as String,
  quantity: Quantity.fromJson(json['quantity'] as Map<String, dynamic>),
  micronutrients:
      (json['micronutrients'] as List<dynamic>).map((e) => Micronutrient.fromJson(e as Map<String, dynamic>)).toList(),
  macronutrients: Macronutrients.fromJson(json['macronutrients'] as Map<String, dynamic>),
);

Map<String, dynamic> _$FoodToJson(Food instance) => <String, dynamic>{
  'name': instance.name,
  'quantity': instance.quantity,
  'micronutrients': instance.micronutrients,
  'macronutrients': instance.macronutrients,
};

Quantity _$QuantityFromJson(Map<String, dynamic> json) =>
    Quantity(value: (json['value'] as num).toDouble(), unit: json['unit'] as String);

Map<String, dynamic> _$QuantityToJson(Quantity instance) => <String, dynamic>{
  'value': instance.value,
  'unit': instance.unit,
};

Micronutrient _$MicronutrientFromJson(Map<String, dynamic> json) => Micronutrient(
  name: json['name'] as String,
  unit: json['unit'] as String,
  value: (json['value'] as num).toDouble(),
  dailyValue: json['daily_value'] as String,
);

Map<String, dynamic> _$MicronutrientToJson(Micronutrient instance) => <String, dynamic>{
  'name': instance.name,
  'unit': instance.unit,
  'value': instance.value,
  'daily_value': instance.dailyValue,
};

Macronutrients _$MacronutrientsFromJson(Map<String, dynamic> json) => Macronutrients(
  protein: Macronutrient.fromJson(json['protein'] as Map<String, dynamic>),
  fat: Macronutrient.fromJson(json['fat'] as Map<String, dynamic>),
  carbohydrates: Macronutrient.fromJson(json['carbohydrates'] as Map<String, dynamic>),
  energy: Macronutrient.fromJson(json['energy'] as Map<String, dynamic>),
);

Map<String, dynamic> _$MacronutrientsToJson(Macronutrients instance) => <String, dynamic>{
  'protein': instance.protein,
  'fat': instance.fat,
  'carbohydrates': instance.carbohydrates,
  'energy': instance.energy,
};

Macronutrient _$MacronutrientFromJson(Map<String, dynamic> json) =>
    Macronutrient(value: (json['value'] as num).toDouble(), unit: json['unit'] as String);

Map<String, dynamic> _$MacronutrientToJson(Macronutrient instance) => <String, dynamic>{
  'value': instance.value,
  'unit': instance.unit,
};

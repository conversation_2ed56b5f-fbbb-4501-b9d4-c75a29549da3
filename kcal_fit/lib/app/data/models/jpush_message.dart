// To parse this JSON data, do
//
//     final jpushMessage = jpushMessageFromJson(jsonString);

import 'dart:convert';

JpushMessage jpushMessageFromJson(String str) => JpushMessage.fromJson(json.decode(str));

String jpushMessageToJson(JpushMessage data) => json.encode(data.toJson());

// 极光推送消息对象
class JpushMessage {
    Extras extras; // 消息扩展字段
    String title; // 消息标题
    String content; // 消息内容
    String jMsgid; // 消息ID
    String contentType; // 消息类型

    JpushMessage({
        required this.extras,
        required this.title,
        required this.content,
        required this.jMsgid,
        required this.contentType,
    });

    factory JpushMessage.fromJson(Map<String, dynamic> json) => JpushMessage(
        extras: Extras.fromJson(json["extras"]),
        title: json["title"],
        content: json["content"],
        jMsgid: json["_j_msgid"],
        contentType: json["content_type"],
    );

    Map<String, dynamic> toJson() => {
        "extras": extras.toJson(),
        "title": title,
        "content": content,
        "_j_msgid": jMsgid,
        "content_type": contentType,
    };
}

class Extras {
    Extras();

    factory Extras.fromJson(Map<String, dynamic> json) => Extras(
    );

    Map<String, dynamic> toJson() => {
    };
}

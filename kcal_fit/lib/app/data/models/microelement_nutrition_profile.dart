import 'dart:convert';

// 从JSON字符串转换为MicroelementNutritionProfile对象
MicroelementNutritionProfile microelementNutritionProfileFromJson(String str) =>
    MicroelementNutritionProfile.fromJson(json.decode(str));

// 转换为JSON字符串
String microelementNutritionProfileToJson(MicroelementNutritionProfile data) =>
    json.encode(data.toJson());

/// 食物成分 API 响应模型 [用户提交饮食信息经过AI分析后返回的食物成分信息]
/// 包含食物名称、数量、能量和 micronutrients 列表。
/// 其中，energy 是一个包含能量值和单位的对象，micronutrients 是一个 micronutrient 列表。
/// 每个 micronutrient 包含名称、值和单位。
/// 最后，total 是一个包含总能量和 micronutrients 列表的对象。
/// 其中，energy 是一个包含总能量值和单位的对象，micronutrients 是一个 micronutrient 列表。
class MicroelementNutritionProfile {
  String content;

  List<Food> foods;

  Total total;

  //只有content的构造函数
  MicroelementNutritionProfile.onlyContent({required this.content})
    : foods = [],
      total = Total(energy: Energy(value: 0, unit: ''), micronutrients: []);

  MicroelementNutritionProfile({
    this.content = '',
    this.foods = const [],
    Total? total,
  }) : total =
           total ??
           Total(energy: Energy(value: 0, unit: ''), micronutrients: const []);

  // 添加无参构造函数
  MicroelementNutritionProfile.empty()
    : content = '',
      foods = [],
      total = Total(energy: Energy(value: 0, unit: ''), micronutrients: []);

  // 从JSON字符串转换为MicroelementNutritionProfile对象
  factory MicroelementNutritionProfile.fromJson(Map<String, dynamic> json) =>
      MicroelementNutritionProfile(
        content: json["content"] ?? '',
        foods: List<Food>.from(json["foods"].map((x) => Food.fromJson(x))),
        total: Total.fromJson(json["total"]),
      );

  // 转换为JSON字符串
  Map<String, dynamic> toJson() => {
    "content": content,
    "foods": List<dynamic>.from(foods.map((x) => x.toJson())),
    "total": total.toJson(),
  };
}

class Food {
  String name;

  String quantity;

  Energy energy;

  List<Micronutrient> micronutrients;

  Food({
    required this.name,
    required this.quantity,
    required this.energy,
    required this.micronutrients,
  });

  factory Food.fromJson(Map<String, dynamic> json) => Food(
    name: json["name"],
    quantity: json["quantity"],
    energy: Energy.fromJson(json["energy"]),
    micronutrients: List<Micronutrient>.from(
      json["micronutrients"].map((x) => Micronutrient.fromJson(x)),
    ),
  );

  Map<String, dynamic> toJson() => {
    "name": name,
    "quantity": quantity,
    "energy": energy.toJson(),
    "micronutrients": List<dynamic>.from(micronutrients.map((x) => x.toJson())),
  };
}

class Energy {
  int value;

  String unit;

  Energy({required this.value, required this.unit});

  factory Energy.fromJson(Map<String, dynamic> json) =>
      Energy(value: json["value"], unit: json["unit"]);

  Map<String, dynamic> toJson() => {"value": value, "unit": unit};
}

class Micronutrient {
  String name;

  double value;

  String unit;

  // 构造函数
  Micronutrient({required this.name, required this.value, required this.unit});

  factory Micronutrient.fromJson(Map<String, dynamic> json) => Micronutrient(
    name: json["name"],
    value: json["value"]?.toDouble(),
    unit: json["unit"],
  );

  Map<String, dynamic> toJson() => {"name": name, "value": value, "unit": unit};
}

class Total {
  Energy energy;

  List<Micronutrient> micronutrients;

  Total({required this.energy, required this.micronutrients});

  factory Total.fromJson(Map<String, dynamic> json) => Total(
    energy: Energy.fromJson(json["energy"]),
    micronutrients: List<Micronutrient>.from(
      json["micronutrients"].map((x) => Micronutrient.fromJson(x)),
    ),
  );

  Map<String, dynamic> toJson() => {
    "energy": energy.toJson(),
    "micronutrients": List<dynamic>.from(micronutrients.map((x) => x.toJson())),
  };
}

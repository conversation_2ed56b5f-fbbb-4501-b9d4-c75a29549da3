/// 极光推送消息数据模型
class PushMessageModel {
  final String title;
  final String content;
  final String? contentType;
  final int? messageId;
  final PushMessageExtras extras;

  PushMessageModel({
    required this.title,
    required this.content,
    this.contentType,
    this.messageId,
    required this.extras,
  });

  /// 从Map构造PushMessageModel
  factory PushMessageModel.fromMap(Map<String, dynamic> map) {
    final dynamic extras = map['extras'];
    // ignore: avoid_print
    print("extras的数据类型: ${extras.runtimeType}");
    final Map<String, dynamic> safeExtras =
        extras is Map ? extras.cast<String, dynamic>() : <String, dynamic>{};
    // ignore: avoid_print
    print("safeExtras的数据: $safeExtras");
    // ignore: avoid_print
    print("safeExtras的数据类型: ${safeExtras.runtimeType}");
    // ignore: avoid_print
    print("title: ${map['title']}");
    // ignore: avoid_print
    print("content: ${map['content']}");
    // ignore: avoid_print
    print("contentType: ${map['content_type']}");
    // ignore: avoid_print
    print("messageId: ${map['_j_msgid']}");
    var a = extras.cast<String, dynamic>();
    // ignore: avoid_print
    print("a的数据: ${a.runtimeType}");
    return PushMessageModel(
      title: map['title'] as String,
      content: map['content'] as String,
      contentType: map['content_type'] as String?,
      messageId: map['_j_msgid'] as int?,
      extras: PushMessageExtras.fromMap(safeExtras),
    );
  }
}

/// 推送消息附加数据模型
class PushMessageExtras {
  final String? error;
  final String content;
  final List<FoodItem> foods;

  PushMessageExtras({this.error, required this.content, required this.foods});

  factory PushMessageExtras.fromMap(Map<String, dynamic> map) {
    // 安全转换foods字段
    final dynamic foods = map['foods'];
    final List<dynamic> safeFoods = foods is List ? foods : <dynamic>[];

    return PushMessageExtras(
      error: map['error'] as String?,
      content: map['content'] as String,
      foods:
          safeFoods
              .map(
                (e) =>
                    e is Map
                        ? FoodItem.fromMap(e.cast<String, dynamic>())
                        : null,
              )
              .whereType<FoodItem>()
              .toList(),
    );
  }
}

/// 食物项数据模型
class FoodItem {
  final String name;
  final FoodQuantity quantity;
  final Macronutrients macronutrients;
  final List<Micronutrient> micronutrients;

  FoodItem({
    required this.name,
    required this.quantity,
    required this.macronutrients,
    required this.micronutrients,
  });

  factory FoodItem.fromMap(Map<String, dynamic> map) {
    return FoodItem(
      name: map['name'] as String,
      quantity: FoodQuantity.fromMap(map['quantity'] as Map<String, dynamic>),
      macronutrients: Macronutrients.fromMap(
        map['macronutrients'] as Map<String, dynamic>,
      ),
      micronutrients:
          (map['micronutrients'] as List<dynamic>)
              .map((e) => Micronutrient.fromMap(e as Map<String, dynamic>))
              .toList(),
    );
  }
}

/// 食物数量模型
class FoodQuantity {
  final double value;
  final String unit;

  FoodQuantity({required this.value, required this.unit});

  factory FoodQuantity.fromMap(Map<String, dynamic> map) {
    return FoodQuantity(
      value: (map['value'] as num).toDouble(),
      unit: map['unit'] as String,
    );
  }
}

/// 宏量营养素模型
class Macronutrients {
  final Nutrient protein;
  final Nutrient fat;
  final Nutrient carbohydrates;
  final Nutrient energy;

  Macronutrients({
    required this.protein,
    required this.fat,
    required this.carbohydrates,
    required this.energy,
  });

  factory Macronutrients.fromMap(Map<String, dynamic> map) {
    return Macronutrients(
      protein: Nutrient.fromMap(map['protein'] as Map<String, dynamic>),
      fat: Nutrient.fromMap(map['fat'] as Map<String, dynamic>),
      carbohydrates: Nutrient.fromMap(
        map['carbohydrates'] as Map<String, dynamic>,
      ),
      energy: Nutrient.fromMap(map['energy'] as Map<String, dynamic>),
    );
  }
}

/// 微量营养素模型
class Micronutrient {
  final String name;
  final String unit;
  final double value;
  final String dailyValue;

  Micronutrient({
    required this.name,
    required this.unit,
    required this.value,
    required this.dailyValue,
  });

  factory Micronutrient.fromMap(Map<String, dynamic> map) {
    return Micronutrient(
      name: map['name'] as String,
      unit: map['unit'] as String,
      value: (map['value'] as num).toDouble(),
      dailyValue: map['daily_value'] as String,
    );
  }
}

/// 通用营养素模型
class Nutrient {
  final double value;
  final String unit;

  Nutrient({required this.value, required this.unit});

  factory Nutrient.fromMap(Map<String, dynamic> map) {
    return Nutrient(
      value: (map['value'] as num).toDouble(),
      unit: map['unit'] as String,
    );
  }
}

class BodyData {
  final int? id;
  final DateTime recordDate;
  final DateTime? birthDate; // 出生日期
  final String? gender; // 性别（男/女）
  final int? height;
  final double? weight;
  final double? bmi;
  final double? bodyFatPercentage;
  final int? waistCircumference;
  final int? hipCircumference;
  final int? systolicBloodPressure;
  final int? diastolicBloodPressure;
  final int? restingHeartRate;
  final DateTime? createdTime;

  BodyData({
    this.id,
    required this.recordDate,
    this.birthDate,
    this.gender,
    this.height,
    this.weight,
    this.bmi,
    this.bodyFatPercentage,
    this.waistCircumference,
    this.hipCircumference,
    this.systolicBloodPressure,
    this.diastolicBloodPressure,
    this.restingHeartRate,
    this.createdTime,
  });

  // 从Map创建BodyData对象（用于从数据库读取）
  factory BodyData.fromMap(Map<String, dynamic> map) {
    return BodyData(
      id: map['id'],
      recordDate: DateTime.parse(map['record_date']),
      birthDate: map['birth_date'] != null ? DateTime.parse(map['birth_date']) : null,
      gender: map['gender'],
      height: map['height'],
      weight: map['weight'],
      bmi: map['bmi'],
      bodyFatPercentage: map['body_fat_percentage'],
      waistCircumference: map['waist_circumference'],
      hipCircumference: map['hip_circumference'],
      systolicBloodPressure: map['systolic_blood_pressure'],
      diastolicBloodPressure: map['diastolic_blood_pressure'],
      restingHeartRate: map['resting_heart_rate'],
      createdTime: map['created_time'] != null ? DateTime.parse(map['created_time']) : null,
    );
  }

  // 将BodyData对象转换为Map（用于写入数据库）
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'record_date': recordDate.toIso8601String(),
      if (birthDate != null) 'birth_date': birthDate!.toIso8601String(),
      if (gender != null) 'gender': gender,
      if (height != null) 'height': height,
      if (weight != null) 'weight': weight,
      if (bmi != null) 'bmi': bmi,
      if (bodyFatPercentage != null) 'body_fat_percentage': bodyFatPercentage,
      if (waistCircumference != null) 'waist_circumference': waistCircumference,
      if (hipCircumference != null) 'hip_circumference': hipCircumference,
      if (systolicBloodPressure != null) 'systolic_blood_pressure': systolicBloodPressure,
      if (diastolicBloodPressure != null) 'diastolic_blood_pressure': diastolicBloodPressure,
      if (restingHeartRate != null) 'resting_heart_rate': restingHeartRate,
    };
  }

  // 复制对象并修改部分属性
  BodyData copyWith({
    int? id,
    DateTime? recordDate,
    DateTime? birthDate,
    String? gender,
    int? height,
    double? weight,
    double? bmi,
    double? bodyFatPercentage,
    int? waistCircumference,
    int? hipCircumference,
    int? systolicBloodPressure,
    int? diastolicBloodPressure,
    int? restingHeartRate,
    DateTime? createdTime,
  }) {
    return BodyData(
      id: id ?? this.id,
      recordDate: recordDate ?? this.recordDate,
      birthDate: birthDate ?? this.birthDate,
      gender: gender ?? this.gender,
      height: height ?? this.height,
      weight: weight ?? this.weight,
      bmi: bmi ?? this.bmi,
      bodyFatPercentage: bodyFatPercentage ?? this.bodyFatPercentage,
      waistCircumference: waistCircumference ?? this.waistCircumference,
      hipCircumference: hipCircumference ?? this.hipCircumference,
      systolicBloodPressure: systolicBloodPressure ?? this.systolicBloodPressure,
      diastolicBloodPressure: diastolicBloodPressure ?? this.diastolicBloodPressure,
      restingHeartRate: restingHeartRate ?? this.restingHeartRate,
      createdTime: createdTime ?? this.createdTime,
    );
  }

  @override
  String toString() {
    return 'BodyData(id: $id, recordDate: $recordDate, birthDate: $birthDate, gender: $gender, height: $height, weight: $weight, bmi: $bmi, bodyFatPercentage: $bodyFatPercentage)';
  }
}

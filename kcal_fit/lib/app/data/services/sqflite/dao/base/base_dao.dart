import 'package:logger/logger.dart';
 final Logger _logger = Logger();
/// 基础DAO接口
/// 定义通用的数据访问操作
abstract class BaseDao<T> {
  /// 插入数据
  /// [entity] 要插入的实体对象
  /// 返回插入记录的ID
  Future<int> insert(T entity);
  
  /// 根据ID删除数据
  /// [id] 要删除的记录ID
  /// 返回删除的行数
  Future<int> deleteById(int id);
  
  /// 更新数据
  /// [entity] 要更新的实体对象
  /// 返回更新的行数
  Future<int> update(T entity);
  
  /// 根据ID查询数据
  /// [id] 记录ID
  /// 返回查询到的实体对象，如果不存在则返回null
  Future<T?> findById(int id);
  
  /// 查询所有数据
  /// 返回所有实体对象列表
  Future<List<T>> findAll();
  
  /// 批量插入数据
  /// [entities] 要插入的实体对象列表
  /// 返回插入成功的记录数
  Future<int> insertBatch(List<T> entities);
  
  /// 批量删除数据
  /// [ids] 要删除的记录ID列表
  /// 返回删除的行数
  Future<int> deleteBatch(List<int> ids);
}

/// 基础DAO实现类
/// 提供通用的数据访问操作实现
abstract class BaseDaoImpl<T> implements BaseDao<T> {
  /// 获取表名
  String get tableName;
  
  /// 将实体对象转换为Map
  Map<String, dynamic> toMap(T entity);
  
  /// 将Map转换为实体对象
  T fromMap(Map<String, dynamic> map);
  
  /// 获取实体对象的ID
  int? getId(T entity);
  
  /// 设置实体对象的ID
  T setId(T entity, int id);
  
  @override
  Future<int> insertBatch(List<T> entities) async {
    if (entities.isEmpty) return 0;
    
    int successCount = 0;
    for (final entity in entities) {
      try {
        await insert(entity);
        successCount++;
      } catch (e) {
        // 记录错误但继续处理其他记录
       _logger.e('批量插入失败: $e');
      }
    }
    return successCount;
  }
  
  @override
  Future<int> deleteBatch(List<int> ids) async {
    if (ids.isEmpty) return 0;
    
    int successCount = 0;
    for (final id in ids) {
      try {
        final result = await deleteById(id);
        successCount += result;
      } catch (e) {
        // 记录错误但继续处理其他记录
        _logger.e('批量删除失败: $e');
      }
    }
    return successCount;
  }
}
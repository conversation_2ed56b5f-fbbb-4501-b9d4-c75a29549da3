import 'dart:convert';
import 'package:logger/logger.dart';

import '../../../../models/diet_analysis.dart';
import '../../config/database_config.dart';
import '../../core/database_exception.dart';
import '../../core/database_service.dart';
import '../base/base_dao.dart';

final Logger logger = Logger();

/// 饮食分析DAO
/// 负责饮食分析数据的数据库操作
class DietAnalysisDao extends BaseDaoImpl<DietAnalysis> {
  final DatabaseService _dbService = DatabaseService();

  @override
  String get tableName => DatabaseConfig.tableDietAnalysis;

  @override
  Map<String, dynamic> toMap(DietAnalysis entity) => entity.toMap();

  @override
  DietAnalysis fromMap(Map<String, dynamic> map) => DietAnalysis.fromMap(map);

  @override
  int? getId(DietAnalysis entity) => entity.id;

  @override
  DietAnalysis setId(DietAnalysis entity, int id) {
    // 这里需要根据DietAnalysis模型的实际结构来实现
    // 假设DietAnalysis有copyWith方法
    return entity; // 临时返回，需要根据实际模型调整
  }

  @override
  Future<int> insert(DietAnalysis entity) async {
    try {
      return await _dbService.insert(tableName, toMap(entity));
    } catch (e) {
      logger.e('添加饮食分析记录失败: $e');
      rethrow;
    }
  }

  /// diet_analysis表添加分析记录 (分析前首次录入)
  Future<int> addAnalysis(Map<String, dynamic> data) async {
    try {
      return await _dbService.insert(tableName, data);
    } catch (e) {
      logger.e('添加分析记录失败: $e');
      rethrow;
    }
  }

  /// 添加食物记录业务方法
  Future<int> addFoodRecord(String analysisId, Map<String, dynamic> data) async {
    try {
      // 注意：这里使用的是food_analysis表，不是food_records
      return await _dbService.insert(DatabaseConfig.tableFoodAnalysis, data);
    } catch (e) {
      logger.e('添加食物记录失败: $e');
      rethrow;
    }
  }

  @override
  Future<DietAnalysis?> findById(int id) async {
    try {
      final result = await _dbService.findById(tableName, id);
      return result != null ? fromMap(result) : null;
    } catch (e) {
      logger.e('根据ID查询饮食分析失败: $e');
      rethrow;
    }
  }

  @override
  Future<List<DietAnalysis>> findAll() async {
    try {
      final results = await _dbService.query(tableName);
      return results.map((map) => fromMap(map)).toList();
    } catch (e) {
      logger.e('获取所有饮食分析记录失败: $e');
      rethrow;
    }
  }

  /// 查询今日饮食记录
  Future<List<DietAnalysis>> getTodayAnalysis(String date) async {
    try {
      final today = date.substring(0, 10);
      logger.i('查询今日饮食记录 - 查询日期: $today');

      final results = await _dbService.query(tableName, where: 'date(meal_time) = ?', whereArgs: [today]);
      logger.i('查询结果数量: ${results.length}');

      if (results.isNotEmpty) {
        logger.i('查询到的记录示例: ${results.first}');
      }

      return results.map((map) => fromMap(map)).toList();
    } catch (e) {
      logger.e('查询今日饮食记录失败: $e');
      rethrow;
    }
  }

  @override
  Future<int> deleteById(int id) async {
    try {
      final result = await _dbService.deleteById(tableName, id);
      if (result == 0) {
        logger.w('删除失败: 未找到ID为$id的饮食分析记录');
      }
      return result;
    } catch (e) {
      logger.e('删除饮食分析记录失败: $e');
      rethrow;
    }
  }

  /// 删除食物记录 分析记录 diet_analysis（保持向后兼容）
  Future<void> deleteRecord(int id) async {
    await deleteById(id);
  }

  @override
  Future<int> update(DietAnalysis entity) async {
    try {
      final id = getId(entity);
      if (id == null) {
        throw DatabaseUpdateException('更新饮食分析记录失败: 缺少ID');
      }
      logger.i('更新饮食分析记录: ${jsonEncode(toMap(entity))}');
      return await _dbService.updateById(tableName, toMap(entity), id);
    } catch (e) {
      logger.e('更新饮食分析记录失败: $e');
      rethrow;
    }
  }

  /// 根据分析ID更新分析记录
  /// [data] 包含更新内容的diet_analysis对象
  /// 返回值：更新的行数
  Future<int> updateAnalysis(Map<String, dynamic> data) async {
    try {
      // 根据分析ID查询分析记录
      final analyzes = await _dbService.query(tableName, where: 'analyze_id = ?', whereArgs: [data['analyze_id']]);
      logger.i('查到的记录: ${jsonEncode(analyzes)}');

      if (analyzes.isEmpty) {
        logger.e('未找到ID为${data['analyze_id']}的分析记录');
        return 0;
      }

      final id = analyzes[0]['id'] as int;
      logger.i('更新分析记录: ${jsonEncode(data)}');
      return await _dbService.updateById(tableName, data, id);
    } catch (e) {
      logger.e('根据分析ID更新记录失败: $e');
      rethrow;
    }
  }

  /// 查询哪些天有记录
  Future<List<Map<String, dynamic>>> getAnalysisDays() async {
    try {
      final results = await _dbService.query(tableName, columns: ['meal_time'], groupBy: 'meal_time');
      return results;
    } catch (e) {
      logger.e('查询分析天数失败: $e');
      rethrow;
    }
  }

  /// 根据分析ID查询记录
  Future<DietAnalysis?> findByAnalyzeId(String analyzeId) async {
    try {
      final results = await _dbService.query(tableName, where: 'analyze_id = ?', whereArgs: [analyzeId], limit: 1);
      return results.isNotEmpty ? fromMap(results.first) : null;
    } catch (e) {
      logger.e('根据分析ID查询记录失败: $e');
      rethrow;
    }
  }

  /// 获取记录天数（不同日期的总数）
  Future<int> getRecordDaysCount() async {
    try {
      final results = await _dbService.query(tableName, columns: ['COUNT(DISTINCT date(meal_time)) as count']);
      return results.isNotEmpty ? (results.first['count'] as int? ?? 0) : 0;
    } catch (e) {
      logger.e('获取记录天数失败: $e');
      return 0;
    }
  }

  /// 获取连续打卡天数（从最后记录日期开始向前计算连续天数）
  Future<int> getContinuousCheckInDays() async {
    try {
      // 获取所有不同的记录日期，按日期降序排列
      final results = await _dbService.query(
        tableName,
        columns: ['DISTINCT date(meal_time) as record_date'],
        orderBy: 'date(meal_time) DESC',
      );

      if (results.isEmpty) return 0;

      // 获取最后一次记录的日期
      final lastRecordDate = DateTime.parse(results.first['record_date'] as String);
      final today = DateTime.now();

      // 如果最后记录不是今天或昨天，连续天数为0
      final daysDiff = today.difference(lastRecordDate).inDays;
      if (daysDiff > 1) return 0;

      // 计算连续天数
      int continuousDays = 0;
      DateTime expectedDate = lastRecordDate;

      for (final result in results) {
        final recordDate = DateTime.parse(result['record_date'] as String);

        // 如果当前记录日期等于期望日期，则连续
        if (recordDate.year == expectedDate.year &&
            recordDate.month == expectedDate.month &&
            recordDate.day == expectedDate.day) {
          continuousDays++;
          // 期望下一个日期是前一天
          expectedDate = expectedDate.subtract(const Duration(days: 1));
        } else {
          // 如果不连续，停止计算
          break;
        }
      }

      return continuousDays;
    } catch (e) {
      logger.e('获取连续打卡天数失败: $e');
      return 0;
    }
  }
}

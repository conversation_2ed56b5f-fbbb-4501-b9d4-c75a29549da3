import 'package:logger/logger.dart';

import '../../../../models/food_analysis.dart';
import '../../core/database_service.dart';
import '../base/base_dao.dart';
import '../../config/database_config.dart';

var logger = Logger();

/// 食物分析DAO
/// 负责食物分析数据的数据库操作
class FoodAnalysisDao extends BaseDaoImpl<FoodAnalysis> {
  final DatabaseService _dbService = DatabaseService();

  @override
  String get tableName => DatabaseConfig.tableFoodAnalysis;

  @override
  Map<String, dynamic> toMap(FoodAnalysis entity) => entity.toMap();

  @override
  FoodAnalysis fromMap(Map<String, dynamic> map) => FoodAnalysis.fromMap(map);

  @override
  int? getId(FoodAnalysis entity) => entity.id;

  @override
  FoodAnalysis setId(FoodAnalysis entity, int id) {
    // 这里需要根据FoodAnalysis模型的实际结构来实现
    // 假设FoodAnalysis有copyWith方法
    return entity; // 临时返回，需要根据实际模型调整
  }

  @override
  Future<int> insert(FoodAnalysis entity) async {
    try {
      return await _dbService.insert(tableName, toMap(entity));
    } catch (e) {
      logger.e('添加食物分析记录失败: $e');
      rethrow;
    }
  }

  @override
  Future<FoodAnalysis?> findById(int id) async {
    try {
      final result = await _dbService.findById(tableName, id);
      return result != null ? fromMap(result) : null;
    } catch (e) {
      logger.e('根据ID查询食物分析失败: $e');
      rethrow;
    }
  }

  @override
  Future<List<FoodAnalysis>> findAll() async {
    try {
      final results = await _dbService.query(tableName);
      return results.map((map) => fromMap(map)).toList();
    } catch (e) {
      logger.e('获取所有食物分析记录失败: $e');
      rethrow;
    }
  }

  /// 根据饮食分析ID查询食物记录列表
  Future<List<FoodAnalysis>> getFoodsByAnalyzeId(String analyzeId) async {
    try {
      logger.i('查询分析ID为 $analyzeId 的食物记录');

      final results = await _dbService.query(
        tableName,
        where: 'diet_analyze_id = ?',
        whereArgs: [analyzeId],
        orderBy: 'id ASC',
      );

      logger.i('查询到 ${results.length} 条食物记录');

      return results.map((map) => fromMap(map)).toList();
    } catch (e) {
      logger.e('根据分析ID查询食物记录失败: $e');
      rethrow;
    }
  }

  @override
  Future<int> deleteById(int id) async {
    try {
      final result = await _dbService.deleteById(tableName, id);
      if (result == 0) {
        logger.w('删除失败: 未找到ID为$id的食物分析记录');
      }
      return result;
    } catch (e) {
      logger.e('删除食物分析记录失败: $e');
      rethrow;
    }
  }

  @override
  Future<int> update(FoodAnalysis entity) async {
    try {
      final id = getId(entity);
      if (id == null) {
        throw Exception('更新食物分析记录失败: 缺少ID');
      }
      logger.i('更新食物分析记录: ${toMap(entity)}');
      return await _dbService.updateById(tableName, toMap(entity), id);
    } catch (e) {
      logger.e('更新食物分析记录失败: $e');
      rethrow;
    }
  }

  /// 根据分析ID删除所有相关食物记录
  Future<int> deleteByAnalyzeId(String analyzeId) async {
    try {
      logger.i('删除分析ID为 $analyzeId 的所有食物记录');

      final result = await _dbService.delete(tableName, where: 'diet_analyze_id = ?', whereArgs: [analyzeId]);

      logger.i('删除了 $result 条食物记录');
      return result;
    } catch (e) {
      logger.e('根据分析ID删除食物记录失败: $e');
      rethrow;
    }
  }
}

import 'package:logger/logger.dart';
import 'package:sqflite/sqflite.dart';

import 'database_connection.dart';
import 'database_exception.dart' as db_exception;

/// 数据库操作服务
/// 提供统一的数据库CRUD操作接口
class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  static final Logger _logger = Logger();
  
  final DatabaseConnection _connection = DatabaseConnection();
  
  factory DatabaseService() => _instance;
  
  DatabaseService._internal();
  
  /// 获取数据库实例
  Future<Database> get _database async => await _connection.database;
  
  /// 插入数据
  /// [table] 表名
  /// [data] 要插入的数据
  /// 返回插入记录的ID
  Future<int> insert(String table, Map<String, dynamic> data) async {
    try {
      final db = await _database;
      final result = await db.insert(table, data);
      _logger.d('插入数据成功: 表=$table, ID=$result');
      return result;
    } catch (e) {
      _logger.e('插入数据失败: 表=$table, 错误=$e');
      throw db_exception.DatabaseInsertException('插入数据失败: $e', originalException: e);
    }
  }
  
  /// 批量插入数据
  /// [table] 表名
  /// [dataList] 要插入的数据列表
  /// 返回插入成功的记录数
  Future<int> insertBatch(String table, List<Map<String, dynamic>> dataList) async {
    if (dataList.isEmpty) return 0;
    
    try {
      final db = await _database;
      int successCount = 0;
      
      await db.transaction((txn) async {
        for (final data in dataList) {
          await txn.insert(table, data);
          successCount++;
        }
      });
      
      _logger.d('批量插入数据成功: 表=$table, 成功数量=$successCount');
      return successCount;
    } catch (e) {
      _logger.e('批量插入数据失败: 表=$table, 错误=$e');
      throw db_exception.DatabaseInsertException('批量插入数据失败: $e', originalException: e);
    }
  }
  
  /// 根据ID删除数据
  /// [table] 表名
  /// [id] 要删除的记录ID
  /// 返回删除的行数
  Future<int> deleteById(String table, int id) async {
    try {
      final db = await _database;
      final result = await db.delete(table, where: 'id = ?', whereArgs: [id]);
      
      if (result == 0) {
        _logger.w('删除数据失败: 表=$table, ID=$id (记录不存在)');
      } else {
        _logger.d('删除数据成功: 表=$table, ID=$id');
      }
      
      return result;
    } catch (e) {
      _logger.e('删除数据失败: 表=$table, ID=$id, 错误=$e');
      throw db_exception.DatabaseDeleteException('删除数据失败: $e', originalException: e);
    }
  }
  
  /// 根据条件删除数据
  /// [table] 表名
  /// [where] WHERE条件
  /// [whereArgs] WHERE参数
  /// 返回删除的行数
  Future<int> delete(String table, {String? where, List<Object?>? whereArgs}) async {
    try {
      final db = await _database;
      final result = await db.delete(table, where: where, whereArgs: whereArgs);
      _logger.d('删除数据成功: 表=$table, 删除行数=$result');
      return result;
    } catch (e) {
      _logger.e('删除数据失败: 表=$table, 错误=$e');
      throw db_exception.DatabaseDeleteException('删除数据失败: $e', originalException: e);
    }
  }
  
  /// 更新数据
  /// [table] 表名
  /// [data] 要更新的数据
  /// [id] 记录ID
  /// 返回更新的行数
  Future<int> updateById(String table, Map<String, dynamic> data, int id) async {
    try {
      final db = await _database;
      
      // 移除ID字段避免冲突
      final updateData = Map<String, dynamic>.from(data);
      updateData.remove('id');
      
      final result = await db.update(
        table,
        updateData,
        where: 'id = ?',
        whereArgs: [id],
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      
      if (result == 0) {
        _logger.w('更新数据失败: 表=$table, ID=$id (记录不存在)');
      } else {
        _logger.d('更新数据成功: 表=$table, ID=$id');
      }
      
      return result;
    } catch (e) {
      _logger.e('更新数据失败: 表=$table, ID=$id, 错误=$e');
      throw db_exception.DatabaseUpdateException('更新数据失败: $e', originalException: e);
    }
  }
  
  /// 根据条件更新数据
  /// [table] 表名
  /// [data] 要更新的数据
  /// [where] WHERE条件
  /// [whereArgs] WHERE参数
  /// 返回更新的行数
  Future<int> update(String table, Map<String, dynamic> data, {String? where, List<Object?>? whereArgs}) async {
    try {
      final db = await _database;
      final result = await db.update(
        table,
        data,
        where: where,
        whereArgs: whereArgs,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      _logger.d('更新数据成功: 表=$table, 更新行数=$result');
      return result;
    } catch (e) {
      _logger.e('更新数据失败: 表=$table, 错误=$e');
      throw db_exception.DatabaseUpdateException('更新数据失败: $e', originalException: e);
    }
  }
  
  /// 查询数据
  /// [table] 表名
  /// [distinct] 是否去重
  /// [columns] 查询的列
  /// [where] WHERE条件
  /// [whereArgs] WHERE参数
  /// [groupBy] GROUP BY条件
  /// [having] HAVING条件
  /// [orderBy] ORDER BY条件
  /// [limit] LIMIT条件
  /// [offset] OFFSET条件
  /// 返回查询结果
  Future<List<Map<String, dynamic>>> query(
    String table, {
    bool? distinct,
    List<String>? columns,
    String? where,
    List<Object?>? whereArgs,
    String? groupBy,
    String? having,
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    try {
      final db = await _database;
      final result = await db.query(
        table,
        distinct: distinct,
        columns: columns,
        where: where,
        whereArgs: whereArgs,
        groupBy: groupBy,
        having: having,
        orderBy: orderBy,
        limit: limit,
        offset: offset,
      );
      _logger.d('查询数据成功: 表=$table, 结果数量=${result.length}');
      return result;
    } catch (e) {
      _logger.e('查询数据失败: 表=$table, 错误=$e');
      throw db_exception.DatabaseQueryException('查询数据失败: $e', originalException: e);
    }
  }
  
  /// 根据ID查询单条数据
  /// [table] 表名
  /// [id] 记录ID
  /// 返回查询结果，如果不存在则返回null
  Future<Map<String, dynamic>?> findById(String table, int id) async {
    try {
      final results = await query(table, where: 'id = ?', whereArgs: [id], limit: 1);
      return results.isNotEmpty ? results.first : null;
    } catch (e) {
      _logger.e('根据ID查询数据失败: 表=$table, ID=$id, 错误=$e');
      throw db_exception.DatabaseQueryException('根据ID查询数据失败: $e', originalException: e);
    }
  }
  
  /// 执行原生SQL查询
  /// [sql] SQL语句
  /// [arguments] SQL参数
  /// 返回查询结果
  Future<List<Map<String, dynamic>>> rawQuery(String sql, [List<Object?>? arguments]) async {
    try {
      final db = await _database;
      final result = await db.rawQuery(sql, arguments);
      _logger.d('执行原生查询成功: SQL=$sql, 结果数量=${result.length}');
      return result;
    } catch (e) {
      _logger.e('执行原生查询失败: SQL=$sql, 错误=$e');
      throw db_exception.DatabaseQueryException('执行原生查询失败: $e', originalException: e);
    }
  }
  
  /// 执行原生SQL语句
  /// [sql] SQL语句
  /// [arguments] SQL参数
  /// 返回影响的行数
  Future<int> rawExecute(String sql, [List<Object?>? arguments]) async {
    try {
      final db = await _database;
      final result = await db.rawUpdate(sql, arguments);
      _logger.d('执行原生SQL成功: SQL=$sql, 影响行数=$result');
      return result;
    } catch (e) {
      _logger.e('执行原生SQL失败: SQL=$sql, 错误=$e');
      throw db_exception.DatabaseException('执行原生SQL失败: $e', originalException: e);
    }
  }
  
  /// 执行事务
  /// [action] 事务操作
  /// 返回事务结果
  Future<T> transaction<T>(Future<T> Function(Transaction txn) action) async {
    try {
      return await _connection.transaction(action);
    } catch (e) {
      _logger.e('执行事务失败: $e');
      rethrow;
    }
  }
  
  /// 关闭数据库连接
  Future<void> close() async {
    await _connection.close();
  }
}
/// 数据库异常类
class DatabaseException implements Exception {
  final String message;
  final String? code;
  final dynamic originalException;
  
  const DatabaseException(this.message, {this.code, this.originalException});
  
  @override
  String toString() {
    return 'DatabaseException: $message${code != null ? ' (Code: $code)' : ''}';
  }
}

/// 数据库连接异常
class DatabaseConnectionException extends DatabaseException {
  const DatabaseConnectionException(super.message, {super.code, super.originalException});
}

/// 数据库查询异常
class DatabaseQueryException extends DatabaseException {
  const DatabaseQueryException(super.message, {super.code, super.originalException});
}

/// 数据库插入异常
class DatabaseInsertException extends DatabaseException {
  const DatabaseInsertException(super.message, {super.code, super.originalException});
}

/// 数据库更新异常
class DatabaseUpdateException extends DatabaseException {
  const DatabaseUpdateException(super.message, {super.code, super.originalException});
}

/// 数据库删除异常
class DatabaseDeleteException extends DatabaseException {
  const DatabaseDeleteException(super.message, {super.code, super.originalException});
}
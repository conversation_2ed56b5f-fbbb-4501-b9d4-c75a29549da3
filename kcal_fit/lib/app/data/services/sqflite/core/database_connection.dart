import 'package:logger/logger.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';


import '../config/database_config.dart';
import '../config/database_schema.dart';
import 'database_exception.dart' as db_exception;


/// 数据库连接管理器
/// 负责数据库连接的创建、管理和生命周期
class DatabaseConnection {
  static final DatabaseConnection _instance = DatabaseConnection._internal();
  static Database? _database;
  static final Logger _logger = Logger();
  
  factory DatabaseConnection() => _instance;
  
  DatabaseConnection._internal();
  
  /// 获取数据库实例
  Future<Database> get database async {
    try {
      if (_database != null && _database!.isOpen) {
        return _database!;
      }
      _database = await _initDatabase();
      return _database!;
    } catch (e) {
      _logger.e('获取数据库实例失败: $e');
      throw db_exception.DatabaseConnectionException('无法获取数据库连接', originalException: e);
    }
  }
  
  /// 初始化数据库
  Future<Database> _initDatabase() async {
    try {
      final databasesPath = await getDatabasesPath();
      final path = join(databasesPath, DatabaseConfig.databaseName);
      
      _logger.i('初始化数据库: $path');
      
      return await openDatabase(
        path,
        version: DatabaseConfig.databaseVersion,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
        onOpen: _onOpen,
      );
    } catch (e) {
      _logger.e('数据库初始化失败: $e');
      throw db_exception.DatabaseConnectionException('数据库初始化失败', originalException: e);
    }
  }
  
  /// 数据库创建回调
  Future<void> _onCreate(Database db, int version) async {
    try {
      _logger.i('创建数据库表结构，版本: $version');
      await DatabaseSchema.createTables(db);
      _logger.i('数据库表创建成功');
    } catch (e) {
      _logger.e('创建数据库表失败: $e');
      throw db_exception.DatabaseConnectionException('创建数据库表失败', originalException: e);
    }
  }
  
  /// 数据库升级回调
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    try {
      _logger.i('数据库升级: $oldVersion -> $newVersion');
      await DatabaseSchema.upgradeTables(db, oldVersion, newVersion);
      _logger.i('数据库升级成功');
    } catch (e) {
      _logger.e('数据库升级失败: $e');
      throw db_exception.DatabaseConnectionException('数据库升级失败', originalException: e);
    }
  }
  
  /// 数据库打开回调
  Future<void> _onOpen(Database db) async {
    _logger.i('数据库已打开');
    // 启用外键约束
    await db.execute('PRAGMA foreign_keys = ON');
  }
  
  /// 关闭数据库连接
  Future<void> close() async {
    try {
      if (_database != null && _database!.isOpen) {
        await _database!.close();
        _database = null;
        _logger.i('数据库连接已关闭');
      }
    } catch (e) {
      _logger.e('关闭数据库连接失败: $e');
      throw db_exception.DatabaseConnectionException('关闭数据库连接失败', originalException: e);
    }
  }
  
  /// 检查数据库连接状态
  bool get isOpen => _database != null && _database!.isOpen;
  
  /// 执行事务
  Future<T> transaction<T>(Future<T> Function(Transaction txn) action) async {
    try {
      final db = await database;
      return await db.transaction(action);
    } catch (e) {
      _logger.e('执行事务失败: $e');
      throw db_exception.DatabaseException('事务执行失败', originalException: e);
    }
  }
}
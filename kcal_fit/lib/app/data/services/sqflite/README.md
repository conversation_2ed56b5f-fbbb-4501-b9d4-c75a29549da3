# SQLite 数据库工具重构说明

## 重构概述

本次重构遵循软件开发最佳实践，实现了高内聚低耦合的架构设计，提高了代码的可维护性和可扩展性。

## 架构设计

### 核心组件

1. **DatabaseConfig** - 数据库配置管理
   - 统一管理数据库名称、版本、表名等配置
   - 便于维护和修改配置信息

2. **DatabaseException** - 异常处理
   - 定义了专门的数据库异常类型
   - 提供更精确的错误信息和处理

3. **DatabaseConnection** - 连接管理
   - 负责数据库连接的生命周期管理
   - 提供连接池和事务支持

4. **DatabaseSchema** - 表结构管理
   - 负责数据库表的创建和升级
   - 统一管理索引创建

5. **DatabaseService** - 数据库服务
   - 提供统一的CRUD操作接口
   - 支持事务操作和批量操作

6. **BaseDao** - DAO基类
   - 定义标准的数据访问接口
   - 提供通用的CRUD操作实现

### DAO层重构

- **BodyDataDao** - 身体数据访问对象
- **DietAnalysisDao** - 饮食分析数据访问对象

每个DAO都继承自`BaseDaoImpl`，实现了标准的数据访问模式。

## 设计原则

### 高内聚
- 每个类都有明确的单一职责
- 相关功能聚合在同一个模块中
- 减少了类之间的依赖关系

### 低耦合
- 通过接口定义契约，降低具体实现的依赖
- 使用依赖注入模式
- 便于单元测试和模块替换

### 可扩展性
- 新增DAO只需继承`BaseDaoImpl`
- 数据库表结构变更集中在`DatabaseSchema`
- 配置变更集中在`DatabaseConfig`

## 使用方式

### 1. 基本CRUD操作

```dart
// 使用DatabaseService进行基本操作
final dbService = DatabaseService();

// 插入数据
final id = await dbService.insert('table_name', data);

// 查询数据
final results = await dbService.query('table_name');

// 更新数据
final count = await dbService.updateById('table_name', data, id);

// 删除数据
final count = await dbService.deleteById('table_name', id);
```

### 2. 使用DAO进行业务操作

#### BodyDataDao - 身体数据操作

```dart
// 创建DAO实例
final bodyDataDao = BodyDataDao();

// 1. 添加身体数据
final bodyData = BodyData(
  userId: 'user123',
  height: 175.0,
  weight: 70.5,
  bodyFat: 15.2,
  muscleMass: 55.0,
  createdTime: DateTime.now(),
);
final id = await bodyDataDao.insert(bodyData);
// 或使用向后兼容方法
final id2 = await bodyDataDao.addBodyData(bodyData);

// 2. 查询操作
// 根据ID查询
final bodyData = await bodyDataDao.findById(1);

// 获取最新身体数据
final latest = await bodyDataDao.getLatestBodyData();

// 获取用户所有身体数据
final userDataList = await bodyDataDao.getBodyDataByUserId('user123');

// 获取指定日期范围的数据
final rangeData = await bodyDataDao.getBodyDataByDateRange(
  'user123',
  DateTime(2024, 1, 1),
  DateTime(2024, 12, 31),
);

// 3. 更新和删除
// 更新数据
final updatedBodyData = bodyData.copyWith(weight: 71.0);
final updateCount = await bodyDataDao.update(updatedBodyData);

// 删除数据
final deleteCount = await bodyDataDao.deleteById(1);

// 4. 批量操作
// 批量插入
final bodyDataList = [bodyData1, bodyData2, bodyData3];
final insertCount = await bodyDataDao.insertBatch(bodyDataList);

// 批量删除
final deleteCount = await bodyDataDao.deleteBatch([1, 2, 3]);
```

#### DietAnalysisDao - 饮食分析操作

```dart
// 创建DAO实例
final dietDao = DietAnalysisDao();

// 1. 添加饮食分析记录
final dietAnalysis = DietAnalysis(
  userId: 'user123',
  analysisDate: DateTime.now(),
  totalCalories: 2000.0,
  protein: 120.0,
  carbs: 250.0,
  fat: 80.0,
  fiber: 25.0,
);
final id = await dietDao.insert(dietAnalysis);

// 使用Map数据添加（向后兼容）
final analysisData = {
  'user_id': 'user123',
  'analysis_date': DateTime.now().toIso8601String(),
  'total_calories': 2000.0,
  'protein': 120.0,
  'carbs': 250.0,
  'fat': 80.0,
};
final id2 = await dietDao.addAnalysis(analysisData);

// 2. 查询操作
// 获取今日分析
final today = DateTime.now();
final todayAnalysis = await dietDao.getTodayAnalysis(
  DateFormat('yyyy-MM-dd').format(today)
);

// 获取用户指定日期的分析
final userAnalysis = await dietDao.getAnalysisByUserAndDate(
  'user123',
  '2024-01-15'
);

// 获取用户所有分析记录
final allAnalysis = await dietDao.getAnalysisByUserId('user123');

// 获取指定日期范围的分析
final rangeAnalysis = await dietDao.getAnalysisByDateRange(
  'user123',
  DateTime(2024, 1, 1),
  DateTime(2024, 1, 31),
);

// 3. 更新分析记录
final updateData = {
  'total_calories': 2100.0,
  'protein': 125.0,
  'updated_time': DateTime.now().toIso8601String(),
};
final updateCount = await dietDao.updateAnalysis(1, updateData);

// 4. 删除操作
// 删除指定记录
final deleteCount = await dietDao.deleteById(1);

// 删除用户指定日期的分析
final deleteCount2 = await dietDao.deleteAnalysisByUserAndDate(
  'user123',
  '2024-01-15'
);
```

#### 在控制器中使用DAO

```dart
class MyController extends GetxController {
  // 使用新架构创建DAO实例
  final bodyDataDao = BodyDataDao();
  final dietAnalysisDao = DietAnalysisDao();
  
  final bodyData = Rx<BodyData?>(null);
  final todayAnalysis = Rx<DietAnalysis?>(null);

  @override
  void onInit() {
    super.onInit();
    loadLatestBodyData();
    loadTodayAnalysis();
  }

  /// 加载最新身体数据
  Future<void> loadLatestBodyData() async {
    try {
      final latest = await bodyDataDao.getLatestBodyData();
      bodyData.value = latest;
    } catch (e) {
      logger.e('加载身体数据失败: $e');
    }
  }

  /// 加载今日饮食分析
  Future<void> loadTodayAnalysis() async {
    try {
      final today = DateFormat('yyyy-MM-dd').format(DateTime.now());
      final analysis = await dietAnalysisDao.getTodayAnalysis(today);
      todayAnalysis.value = analysis;
    } catch (e) {
      logger.e('加载饮食分析失败: $e');
    }
  }

  /// 保存身体数据
  Future<bool> saveBodyData(BodyData data) async {
    try {
      await bodyDataDao.insert(data);
      await loadLatestBodyData(); // 刷新数据
      return true;
    } catch (e) {
      logger.e('保存身体数据失败: $e');
      return false;
    }
  }
}
```

### 3. 事务操作

```dart
final dbService = DatabaseService();

await dbService.transaction((txn) async {
  // 在事务中执行多个操作
  await txn.insert('table1', data1);
  await txn.insert('table2', data2);
  // 如果任何操作失败，整个事务会回滚
});
```

## 最佳实践

### 1. 依赖注入模式

```dart
// 在应用初始化时注册DAO
class AppInit {
  static void initDaos() {
    Get.put<BodyDataDao>(BodyDataDao());
    Get.put<DietAnalysisDao>(DietAnalysisDao());
  }
}

// 在控制器中使用依赖注入
class MyController extends GetxController {
  final BodyDataDao bodyDataDao = Get.find<BodyDataDao>();
  final DietAnalysisDao dietAnalysisDao = Get.find<DietAnalysisDao>();
}
```

### 2. 错误处理最佳实践

```dart
class DataService {
  final BodyDataDao _bodyDataDao = BodyDataDao();
  
  Future<Result<BodyData>> saveBodyData(BodyData data) async {
    try {
      final id = await _bodyDataDao.insert(data);
      final savedData = await _bodyDataDao.findById(id);
      return Result.success(savedData!);
    } on DatabaseInsertException catch (e) {
      logger.e('保存身体数据失败: ${e.message}');
      return Result.error('保存失败，请重试');
    } on DatabaseException catch (e) {
      logger.e('数据库操作失败: ${e.message}');
      return Result.error('数据库错误');
    } catch (e) {
      logger.e('未知错误: $e');
      return Result.error('操作失败');
    }
  }
}
```

### 3. 数据验证

```dart
class BodyDataDao extends BaseDaoImpl<BodyData> {
  @override
  Future<int> insert(BodyData entity) async {
    // 数据验证
    if (entity.userId.isEmpty) {
      throw DatabaseValidationException('用户ID不能为空');
    }
    if (entity.weight <= 0 || entity.height <= 0) {
      throw DatabaseValidationException('身高体重必须大于0');
    }
    
    return super.insert(entity);
  }
}
```

### 4. 分页查询

```dart
class BodyDataDao extends BaseDaoImpl<BodyData> {
  /// 分页查询用户身体数据
  Future<List<BodyData>> getBodyDataByPage(
    String userId, {
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      final offset = (page - 1) * pageSize;
      final results = await _dbService.query(
        tableName,
        where: 'user_id = ?',
        whereArgs: [userId],
        orderBy: 'created_time DESC',
        limit: pageSize,
        offset: offset,
      );
      return results.map((map) => fromMap(map)).toList();
    } catch (e) {
      logger.e('分页查询身体数据失败: $e');
      rethrow;
    }
  }
}
```

## 向后兼容性

`DatabaseHelper` 已被完全移除，所有功能已迁移到新架构：

```dart
// 旧方式（已移除）
// final helper = DatabaseHelper();
// final result = await helper.queryData('table_name');

// 新方式（当前推荐）
final service = DatabaseService();
final result = await service.query('table_name');

// 或使用专门的DAO
final bodyDataDao = BodyDataDao();
final bodyData = await bodyDataDao.findById(1);
```

## 错误处理

新架构提供了完善的错误处理机制：

```dart
try {
  final result = await dbService.insert('table_name', data);
} on DatabaseInsertException catch (e) {
  // 处理插入异常
  logger.e('插入失败: ${e.message}');
} on DatabaseException catch (e) {
  // 处理通用数据库异常
  logger.e('数据库操作失败: ${e.message}');
}
```

## 日志记录

所有数据库操作都包含详细的日志记录，便于调试和监控：

- 操作成功/失败的日志
- 执行时间统计
- 错误详情记录

## 性能优化

- 连接池管理，避免频繁创建连接
- 批量操作支持，提高大量数据处理效率
- 索引优化，提高查询性能
- 事务支持，保证数据一致性

## 测试支持

新架构更容易进行单元测试：

- 接口抽象便于Mock
- 依赖注入支持测试替身
- 每个组件职责单一，测试覆盖更全面

## 迁移建议

1. **立即可做**：新功能使用新架构开发
2. **逐步迁移**：现有功能在维护时逐步重构
3. **最终目标**：完全移除`DatabaseHelper`的使用

## 文件结构

```
sqflite/
├── README.md                 # 本说明文件
├── config/                       # 配置层
│   ├── database_config.dart      # 数据库配置
│   └── database_schema.dart      # 表结构管理
├── core/                         # 核心层
│   ├── database_connection.dart  # 连接管理
│   ├── database_service.dart     # 数据库服务
│   └── database_exception.dart   # 异常定义
└── dao/                          # 数据访问层
    ├── base/
    │   └── base_dao.dart            # DAO基类
    └── impl/
        ├── body_data_dao.dart       # 身体数据DAO
        └── diet_analysis_dao.dart   # 饮食分析DAO
```

## 架构优势总结

### 1. 高内聚低耦合
- 每个类职责单一，功能内聚
- 模块间依赖关系清晰
- 便于单独测试和维护

### 2. 可扩展性强
- 新增DAO只需继承BaseDaoImpl
- 数据库结构变更集中管理
- 支持插件化扩展

### 3. 错误处理完善
- 分层异常处理机制
- 详细的错误日志记录
- 用户友好的错误提示

### 4. 性能优化
- 连接池管理
- 批量操作支持
- 索引优化
- 事务保证数据一致性

### 5. 开发效率
- 统一的操作接口
- 丰富的使用示例
- 完善的文档说明
- 向后兼容的迁移策略

这种架构设计确保了代码的可维护性、可扩展性和可测试性，为未来的功能迭代奠定了坚实的基础。
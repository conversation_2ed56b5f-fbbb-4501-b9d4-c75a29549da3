import 'package:sqflite/sqflite.dart';
import 'package:logger/logger.dart';

import 'database_config.dart';

/// 数据库表结构管理器
/// 负责数据库表的创建、升级和索引管理
class DatabaseSchema {
  static final Logger _logger = Logger();

  /// 创建所有数据库表
  static Future<void> createTables(Database db) async {
    await _createDietAnalysisTable(db);
    await _createFoodAnalysisTable(db);
    await _createBodyDataTable(db);
    await _createIndexes(db);
  }

  /// 升级数据库表（开发阶段暂不需要，直接删除重装即可）
  static Future<void> upgradeTables(Database db, int oldVersion, int newVersion) async {
    _logger.i('数据库升级: $oldVersion -> $newVersion');
    _logger.i('开发阶段建议删除应用重新安装以获得最新数据库结构');
  }

  /// 创建饮食分析主表
  static Future<void> _createDietAnalysisTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConfig.tableDietAnalysis} (
          -- 核心信息
          id INTEGER PRIMARY KEY AUTOINCREMENT, -- 主键（自增）
          analyze_id VARCHAR(32) UNIQUE NOT NULL, -- 唯一分析批次ID
          meal_time DATETIME NOT NULL, -- 实际饮食时间（如：2023-10-05 08:30:00）
          meal_type VARCHAR(20), -- 餐次类型（早餐/午餐/晚餐/加餐等）
          diet_description TEXT, -- 饮食描述（如：这顿饭主要包含……）
          
          -- 宏量营养素
          calories INT, -- 能量（千卡）
          protein INT, -- 蛋白质（克）
          carbs INT, -- 碳水化合物（克）
          fat INT, -- 脂肪（克）
          fiber INT, -- 膳食纤维（克）
          sugar INT, -- 糖分（克）
          
          -- 关键风险营养素
          sodium INT, -- 钠（毫克）
          cholesterol INT, -- 胆固醇（毫克）
          
          -- 维生素
          vitamin_a INT, -- 维生素A（视黄醇当量，微克）
          vitamin_b1 INT, -- 维生素B1/硫胺素（毫克）
          vitamin_b2 INT, -- 维生素B2/核黄素（毫克）
          vitamin_b3 INT, -- 维生素B3/烟酸（毫克）
          vitamin_b6 INT, -- 维生素B6/吡哆醇（毫克）
          vitamin_b12 INT, -- 维生素B12/钴胺素（微克）
          folate INT, -- 叶酸（微克）
          vitamin_c INT, -- 维生素C（毫克）
          vitamin_d INT, -- 维生素D（微克）
          vitamin_e INT, -- 维生素E（α-生育酚当量，毫克）
          vitamin_k INT, -- 维生素K（微克）
          
          -- 矿物质
          calcium INT, -- 钙（毫克）
          iron INT, -- 铁（毫克）
          magnesium INT, -- 镁（毫克）
          phosphorus INT, -- 磷（毫克）
          potassium INT, -- 钾（毫克）
          zinc INT, -- 锌（毫克）
          copper INT, -- 铜（毫克）
          manganese INT, -- 锰（毫克）
          selenium INT, -- 硒（微克）
          iodine INT, -- 碘（微克）
          chromium INT, -- 铬（微克）
          molybdenum INT, -- 钼（微克）
          
          -- 脂肪酸
          omega3 INT, -- ω-3脂肪酸（克）
          omega6 INT, -- ω-6脂肪酸（克）
          trans_fat INT, -- 反式脂肪（克）
          saturated_fat INT, -- 饱和脂肪酸（克）
          monounsaturated_fat INT, -- 单不饱和脂肪酸（MUFA，克）
          polyunsaturated_fat INT, -- 多不饱和脂肪酸（PUFA，不含Omega3/6，克）
          
          -- 其他信息
          tags TEXT,  -- 标签（如：健康/低热量/素食等）
          notes TEXT, -- 备注（如用餐地点、特殊说明）
          photo_path VARCHAR(255), -- 餐食照片路径（URL或本地路径）
          created_time DATETIME DEFAULT CURRENT_TIMESTAMP, -- 创建时间
          update_time DATETIME DEFAULT CURRENT_TIMESTAMP -- 最后更新时间
      )
    ''');
  }

  /// 创建食物分析表
  static Future<void> _createFoodAnalysisTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConfig.tableFoodAnalysis} (
          -- 核心信息
          id INTEGER PRIMARY KEY AUTOINCREMENT, -- 主键（自增）
          diet_analyze_id VARCHAR(32) NOT NULL, -- 关联主表analyze_id
          food_name VARCHAR(100) NOT NULL, -- 食物名称（如：全脂牛奶）
          quantity INT, -- 食用量（克/毫升/个）
          
          -- 宏量营养素
          calories INT, -- 能量（千卡）
          protein INT, -- 蛋白质（克）
          carbs INT, -- 碳水化合物（克）
          fat INT, -- 脂肪（克）
          fiber INT, -- 膳食纤维（克）
          sugar INT, -- 糖分（克）

          -- 关键风险营养素
          sodium INT, -- 钠（毫克）
          cholesterol INT, -- 胆固醇（毫克）

          -- 维生素
          vitamin_a INT, -- 维生素A（微克视黄醇当量）
          vitamin_b1 INT, -- 维生素B1/硫胺素（毫克）
          vitamin_b2 INT, -- 维生素B2/核黄素（毫克）
          vitamin_b3 INT, -- 维生素B3/烟酸（毫克）
          vitamin_b6 INT, -- 维生素B6/吡哆醇（毫克）
          vitamin_b12 INT, -- 维生素B12/钴胺素（微克）
          folate INT, -- 叶酸（微克）
          vitamin_c INT, -- 维生素C（毫克）
          vitamin_d INT, -- 维生素D（微克）
          vitamin_e INT, -- 维生素E（α-生育酚当量，毫克）
          vitamin_k INT, -- 维生素K（微克）
          
          -- 矿物质
          calcium INT, -- 钙（毫克）
          iron INT, -- 铁（毫克）
          magnesium INT, -- 镁（毫克）
          phosphorus INT, -- 磷（毫克）
          potassium INT, -- 钾（毫克）
          zinc INT, -- 锌（毫克）
          copper INT, -- 铜（毫克）
          manganese INT, -- 锰（毫克）
          selenium INT, -- 硒（微克）
          iodine INT, -- 碘（微克）
          chromium INT, -- 铬（微克）
          molybdenum INT, -- 钼（微克）
          
          -- 脂肪酸
          omega3 INT, -- ω-3脂肪酸（克）
          omega6 INT, -- ω-6脂肪酸（克）
          trans_fat INT, -- 反式脂肪（克）
          saturated_fat INT, -- 饱和脂肪酸（克）
          monounsaturated_fat INT, -- 单不饱和脂肪酸（MUFA，克）
          polyunsaturated_fat INT, -- 多不饱和脂肪酸（PUFA，不含Omega3/6，克）
          
          -- 外键约束
          FOREIGN KEY (diet_analyze_id) REFERENCES ${DatabaseConfig.tableDietAnalysis}(analyze_id) ON DELETE CASCADE
      );
    ''');
  }

  /// 创建身体数据表
  static Future<void> _createBodyDataTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConfig.tableBodyData} (
          -- 核心信息
          id INTEGER PRIMARY KEY AUTOINCREMENT, -- 主键（自增）
          record_date DATETIME NOT NULL, -- 记录日期（如：2023-10-05 08:30:00）
          
          -- 基础身体指标
          birth_date DATETIME, -- 出生日期
          gender VARCHAR(10), -- 性别（男/女）
          height INT, -- 身高（厘米）
          weight REAL, -- 体重（千克，支持小数）
          bmi FLOAT, -- 身体质量指数
          body_fat_percentage FLOAT, -- 体脂率（百分比）
          
          -- 围度测量
          waist_circumference INT, -- 腰围（厘米）
          hip_circumference INT, -- 臀围（厘米）
          
          -- 健康指标
          systolic_blood_pressure INT, -- 收缩压（mmHg）
          diastolic_blood_pressure INT, -- 舒张压（mmHg）
          resting_heart_rate INT, -- 静息心率（次/分钟）
          
          created_time DATETIME DEFAULT CURRENT_TIMESTAMP -- 创建时间
      );
    ''');
  }

  /// 创建索引
  static Future<void> _createIndexes(Database db) async {
    // 食物分析表索引
    await db.execute('CREATE INDEX ${DatabaseConfig.idxFoodName} ON ${DatabaseConfig.tableFoodAnalysis} (food_name);');
    await db.execute(
      'CREATE INDEX ${DatabaseConfig.idxDietAnalyzeId} ON ${DatabaseConfig.tableFoodAnalysis} (diet_analyze_id);',
    );

    // 身体数据表索引
    await db.execute('CREATE INDEX ${DatabaseConfig.idxRecordDate} ON ${DatabaseConfig.tableBodyData} (record_date);');
  }
}

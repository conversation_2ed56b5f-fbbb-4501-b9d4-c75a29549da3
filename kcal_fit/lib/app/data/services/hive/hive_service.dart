import 'package:hive_flutter/hive_flutter.dart';
import 'dart:io';

class HiveService {
  static bool _initialized = false;
  static Future<void> _ensureInitialized() async {
    if (!_initialized) {
      await Hive.initFlutter();
      if (!Hive.isAdapterRegistered(0)) {}
      _initialized = true;
    }
  }

  static Future<void> saveData<T>(String boxName, String key, T value) async {
    await _ensureInitialized();
    final box = await Hive.openBox(boxName);
    await box.put(key, value);
  }

  static Future<T?> getData<T>(String boxName, String key) async {
    await _ensureInitialized();
    final box = await Hive.openBox(boxName);
    return box.get(key) as T?;
  }

  static Future<void> deleteData(String boxName, String key) async {
    await _ensureInitialized();
    final box = await Hive.openBox(boxName);
    await box.delete(key);
  }

  static Future<void> clearBox(String boxName) async {
    await _ensureInitialized();
    final box = await Hive.openBox(boxName);
    await box.clear();
  }

  static Future<void> closeBox(String boxName) async {
    await _ensureInitialized();
    final box = Hive.box(boxName);
    await box.close();
  }

  static Future<void> closeAllBoxes() async {
    await _ensureInitialized();
    await Hive.close();
  }

  static Future<Map<String, T>> getAllData<T>(String boxName) async {
    final box = await Hive.openBox(boxName);
    return Map.fromEntries(
      box.toMap().entries.map((e) => MapEntry(e.key.toString(), e.value as T)),
    );
  }

  // 获取指定box的大小
  static Future<int> getBoxSize(String boxName) async {
    await _ensureInitialized();
    final box = await Hive.openBox(boxName);
    final boxPath = box.path;
    if (boxPath == null) return 0;
    final file = File(boxPath);
    if (await file.exists()) {
      return await file.length();
    }
    return 0;
  }
}

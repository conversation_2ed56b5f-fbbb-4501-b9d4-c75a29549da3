import 'package:get/get.dart';

import '../modules/add_diet_record/bindings/add_diet_record_binding.dart';
import '../modules/add_diet_record/views/add_diet_record_view.dart';
import '../modules/body_data_form/bindings/body_data_form_binding.dart';
import '../modules/body_data_form/views/body_data_form_view.dart';
import '../modules/home/<USER>/home_controller.dart';
import '../modules/home/<USER>/home_view.dart';
import '../modules/index/bindings/index_binding.dart';
import '../modules/index/controllers/index_controller.dart';
import '../modules/index/views/index_view.dart';
import '../modules/my/bindings/my_binding.dart';
import '../modules/my/controllers/my_controller.dart';
import '../modules/my/views/my_view.dart';
import '../modules/personal_info/bindings/personal_info_binding.dart';
import '../modules/personal_info/views/personal_info_view.dart';
import '../modules/plan/bindings/plan_binding.dart';
import '../modules/plan/views/plan_view.dart';
import '../modules/record/bindings/record_binding.dart';
import '../modules/record/views/record_view.dart';
import '../modules/help_feedback/bindings/help_feedback_binding.dart';
import '../modules/help_feedback/views/help_feedback_view.dart';
import '../modules/about_us/bindings/about_us_binding.dart';
import '../modules/about_us/views/about_us_view.dart';
import '../modules/goal_setting/bindings/goal_setting_binding.dart';
import '../modules/goal_setting/views/goal_setting_view.dart';
import '../modules/reminder_setting/bindings/reminder_setting_binding.dart';
import '../modules/reminder_setting/views/reminder_setting_view.dart';
import '../modules/privacy_setting/bindings/privacy_setting_binding.dart';
import '../modules/privacy_setting/views/privacy_setting_view.dart';
import '../modules/theme_setting/bindings/theme_setting_binding.dart';
import '../modules/theme_setting/views/theme_setting_view.dart';
import '../modules/ai_assistant/bindings/ai_assistant_binding.dart';
import '../modules/ai_assistant/views/ai_assistant_view.dart';
import '../modules/health_detail/bindings/health_detail_binding.dart';
import '../modules/health_detail/views/health_detail_view.dart';

part 'app_routes.dart';

/// 路由配置类，用于定义应用程序的路由。
/// 它包含了应用程序的初始路由和所有的路由。
class AppPages {
  AppPages._();

  /// 应用程序的初始路由。
  static const initial = Routes.HOME;

  // 在原有路由配置中添加
  static final routes = [
    GetPage(
      name: _Paths.HOME,
      page: () => HomeView(),
      //路由打开home页面的时候就会注入这些控制器
      bindings: [
        // BindingsBuilder.put(() => RecordController()),
        BindingsBuilder.put(() => HomeController()),
        // BindingsBuilder.put(() => PlanController()),
        BindingsBuilder.put(() => IndexController()),
        BindingsBuilder.put(() => MyController()),
      ],
    ),
    GetPage(name: _Paths.MY, page: () => const MyView(), binding: BindingsBuilder.put(() => MyBinding())),
    GetPage(name: _Paths.INDEX, page: () => IndexView(), binding: IndexBinding()),
    GetPage(name: _Paths.PLAN, page: () => const PlanView(), binding: PlanBinding()),
    GetPage(name: _Paths.RECORD, page: () => const RecordView(), binding: RecordBinding()),
    GetPage(name: Routes.ADD_DIET_RECORD, page: () => AddDietRecordView(), binding: AddDietRecordBinding()),
    GetPage(name: _Paths.BODY_DATA_FORM, page: () => BodyDataFormView(), binding: BodyDataFormBinding()),
    GetPage(name: _Paths.PERSONAL_INFO, page: () => PersonalInfoView(), binding: PersonalInfoBinding()),
    GetPage(name: _Paths.HELP_FEEDBACK, page: () => const HelpFeedbackView(), binding: HelpFeedbackBinding()),
    GetPage(name: _Paths.ABOUT_US, page: () => const AboutUsView(), binding: AboutUsBinding()),
    GetPage(name: _Paths.GOAL_SETTING, page: () => const GoalSettingView(), binding: GoalSettingBinding()),
    GetPage(name: _Paths.REMINDER_SETTING, page: () => const ReminderSettingView(), binding: ReminderSettingBinding()),
    GetPage(name: _Paths.PRIVACY_SETTING, page: () => const PrivacySettingView(), binding: PrivacySettingBinding()),
    GetPage(name: _Paths.THEME_SETTING, page: () => const ThemeSettingView(), binding: ThemeSettingBinding()),
    GetPage(name: _Paths.AI_ASSISTANT, page: () => const AiAssistantView(), binding: AiAssistantBinding()),
    GetPage(name: _Paths.HEALTH_DETAIL, page: () => const HealthDetailView(), binding: HealthDetailBinding()),
  ];
}

// ignore_for_file: constant_identifier_names

part of 'app_pages.dart';
// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

///应用程序的所有路由。
abstract class Routes {
  Routes._();
  static const HOME = _Paths.HOME;
  static const MY = _Paths.MY;
  static const INDEX = _Paths.INDEX;
  static const PLAN = _Paths.PLAN;
  static const RECORD = _Paths.RECORD;
  static const ADD_DIET_RECORD = _Paths.ADD_DIET_RECORD;
  static const BODY_DATA_FORM = _Paths.BODY_DATA_FORM;
  static const PERSONAL_INFO = _Paths.PERSONAL_INFO;
  static const HELP_FEEDBACK = _Paths.HELP_FEEDBACK;
  static const ABOUT_US = _Paths.ABOUT_US;
  static const GOAL_SETTING = _Paths.GOAL_SETTING;
  static const REMINDER_SETTING = _Paths.REMINDER_SETTING;
  static const PRIVACY_SETTING = _Paths.PRIVACY_SETTING;
  static const THEME_SETTING = _Paths.THEME_SETTING;
  static const AI_ASSISTANT = _Paths.AI_ASSISTANT;
}

///应用程序的所有路径。
abstract class _Paths {
  _Paths._();
  static const HOME = '/home';
  static const MY = '/my'; // 个人中心页面
  static const INDEX = '/index'; // 首页页面
  static const PLAN = '/plan'; // 计划页面
  static const RECORD = '/record'; // 记录页面
  static const ADD_DIET_RECORD = '/add-diet-record';
  static const BODY_DATA_FORM = '/body-data-form';
  static const PERSONAL_INFO = '/personal-info';
  static const HELP_FEEDBACK = '/help-feedback';
  static const ABOUT_US = '/about-us';
  static const GOAL_SETTING = '/goal-setting';
  static const REMINDER_SETTING = '/reminder-setting';
  static const PRIVACY_SETTING = '/privacy-setting';
  static const THEME_SETTING = '/theme-setting';
  static const AI_ASSISTANT = '/ai-assistant';
}

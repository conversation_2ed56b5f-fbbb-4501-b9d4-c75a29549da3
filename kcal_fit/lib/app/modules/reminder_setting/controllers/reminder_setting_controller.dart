import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/services/hive/hive_service.dart';

/// 提醒设置控制器
class ReminderSettingController extends GetxController {
  // 提醒开关
  final RxBool mealReminderEnabled = true.obs;
  final RxBool waterReminderEnabled = true.obs;
  final RxBool exerciseReminderEnabled = false.obs;
  final RxBool weightRecordReminderEnabled = true.obs;
  final RxBool sleepReminderEnabled = false.obs;

  // 餐食提醒时间
  final RxString breakfastTime = '08:00'.obs;
  final RxString lunchTime = '12:00'.obs;
  final RxString dinnerTime = '18:00'.obs;

  // 喝水提醒间隔（分钟）
  final RxInt waterReminderInterval = 120.obs;
  final List<int> waterIntervalOptions = [30, 60, 90, 120, 180, 240];

  // 运动提醒时间
  final RxString exerciseTime = '19:00'.obs;

  // 体重记录提醒
  final RxString weightRecordTime = '07:00'.obs;
  final RxList<String> weightRecordDays = <String>['周一', '周三', '周五'].obs;
  final List<String> weekDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];

  // 睡眠提醒时间
  final RxString sleepTime = '22:00'.obs;

  // 加载状态
  final RxBool isLoading = true.obs;
  final RxBool isSaving = false.obs;

  @override
  void onInit() {
    super.onInit();
    _loadReminderSettings();
  }

  /// 加载提醒设置
  Future<void> _loadReminderSettings() async {
    try {
      isLoading.value = true;

      // 从Hive加载设置
      final mealEnabled = await HiveService.getData("reminders", "mealReminderEnabled");
      final waterEnabled = await HiveService.getData("reminders", "waterReminderEnabled");
      final exerciseEnabled = await HiveService.getData("reminders", "exerciseReminderEnabled");
      final weightEnabled = await HiveService.getData("reminders", "weightRecordReminderEnabled");
      final sleepEnabled = await HiveService.getData("reminders", "sleepReminderEnabled");

      if (mealEnabled != null) mealReminderEnabled.value = mealEnabled;
      if (waterEnabled != null) waterReminderEnabled.value = waterEnabled;
      if (exerciseEnabled != null) exerciseReminderEnabled.value = exerciseEnabled;
      if (weightEnabled != null) weightRecordReminderEnabled.value = weightEnabled;
      if (sleepEnabled != null) sleepReminderEnabled.value = sleepEnabled;

      // 加载时间设置
      final breakfast = await HiveService.getData("reminders", "breakfastTime");
      final lunch = await HiveService.getData("reminders", "lunchTime");
      final dinner = await HiveService.getData("reminders", "dinnerTime");
      final exercise = await HiveService.getData("reminders", "exerciseTime");
      final weightRecord = await HiveService.getData("reminders", "weightRecordTime");
      final sleep = await HiveService.getData("reminders", "sleepTime");

      if (breakfast != null) breakfastTime.value = breakfast;
      if (lunch != null) lunchTime.value = lunch;
      if (dinner != null) dinnerTime.value = dinner;
      if (exercise != null) exerciseTime.value = exercise;
      if (weightRecord != null) weightRecordTime.value = weightRecord;
      if (sleep != null) sleepTime.value = sleep;

      // 加载其他设置
      final waterInterval = await HiveService.getData("reminders", "waterReminderInterval");
      if (waterInterval != null) {
        waterReminderInterval.value = int.tryParse(waterInterval.toString()) ?? 120;
      }

      final weightDays = await HiveService.getData("reminders", "weightRecordDays");
      if (weightDays != null && weightDays is List) {
        weightRecordDays.value = List<String>.from(weightDays);
      }
    } catch (e) {
      // ignore: avoid_print
      print('加载提醒设置失败: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// 切换餐食提醒
  void toggleMealReminder(bool value) {
    mealReminderEnabled.value = value;
    _saveSettings();
  }

  /// 切换喝水提醒
  void toggleWaterReminder(bool value) {
    waterReminderEnabled.value = value;
    _saveSettings();
  }

  /// 切换运动提醒
  void toggleExerciseReminder(bool value) {
    exerciseReminderEnabled.value = value;
    _saveSettings();
  }

  /// 切换体重记录提醒
  void toggleWeightRecordReminder(bool value) {
    weightRecordReminderEnabled.value = value;
    _saveSettings();
  }

  /// 切换睡眠提醒
  void toggleSleepReminder(bool value) {
    sleepReminderEnabled.value = value;
    _saveSettings();
  }

  /// 设置餐食时间
  Future<void> setMealTime(String mealType) async {
    final TimeOfDay? picked = await showTimePicker(
      context: Get.context!,
      initialTime: _parseTimeString(_getCurrentMealTime(mealType)),
      builder: (context, child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: ColorScheme.light(primary: Colors.blue.shade600, onPrimary: Colors.white),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      final timeString = '${picked.hour.toString().padLeft(2, '0')}:${picked.minute.toString().padLeft(2, '0')}';

      switch (mealType) {
        case 'breakfast':
          breakfastTime.value = timeString;
          break;
        case 'lunch':
          lunchTime.value = timeString;
          break;
        case 'dinner':
          dinnerTime.value = timeString;
          break;
      }

      _saveSettings();
    }
  }

  /// 设置运动时间
  Future<void> setExerciseTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: Get.context!,
      initialTime: _parseTimeString(exerciseTime.value),
      builder: (context, child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: ColorScheme.light(primary: Colors.blue.shade600, onPrimary: Colors.white),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      exerciseTime.value = '${picked.hour.toString().padLeft(2, '0')}:${picked.minute.toString().padLeft(2, '0')}';
      _saveSettings();
    }
  }

  /// 设置体重记录时间
  Future<void> setWeightRecordTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: Get.context!,
      initialTime: _parseTimeString(weightRecordTime.value),
      builder: (context, child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: ColorScheme.light(primary: Colors.blue.shade600, onPrimary: Colors.white),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      weightRecordTime.value = '${picked.hour.toString().padLeft(2, '0')}:${picked.minute.toString().padLeft(2, '0')}';
      _saveSettings();
    }
  }

  /// 设置睡眠时间
  Future<void> setSleepTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: Get.context!,
      initialTime: _parseTimeString(sleepTime.value),
      builder: (context, child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: ColorScheme.light(primary: Colors.blue.shade600, onPrimary: Colors.white),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      sleepTime.value = '${picked.hour.toString().padLeft(2, '0')}:${picked.minute.toString().padLeft(2, '0')}';
      _saveSettings();
    }
  }

  /// 设置喝水提醒间隔
  void setWaterReminderInterval(int interval) {
    waterReminderInterval.value = interval;
    _saveSettings();
  }

  /// 切换体重记录日期
  void toggleWeightRecordDay(String day) {
    if (weightRecordDays.contains(day)) {
      weightRecordDays.remove(day);
    } else {
      weightRecordDays.add(day);
    }
    _saveSettings();
  }

  /// 获取当前餐食时间
  String _getCurrentMealTime(String mealType) {
    switch (mealType) {
      case 'breakfast':
        return breakfastTime.value;
      case 'lunch':
        return lunchTime.value;
      case 'dinner':
        return dinnerTime.value;
      default:
        return '08:00';
    }
  }

  /// 解析时间字符串
  TimeOfDay _parseTimeString(String timeString) {
    final parts = timeString.split(':');
    return TimeOfDay(hour: int.parse(parts[0]), minute: int.parse(parts[1]));
  }

  /// 保存设置
  Future<void> _saveSettings() async {
    try {
      // 保存开关状态
      await HiveService.saveData("reminders", "mealReminderEnabled", mealReminderEnabled.value);
      await HiveService.saveData("reminders", "waterReminderEnabled", waterReminderEnabled.value);
      await HiveService.saveData("reminders", "exerciseReminderEnabled", exerciseReminderEnabled.value);
      await HiveService.saveData("reminders", "weightRecordReminderEnabled", weightRecordReminderEnabled.value);
      await HiveService.saveData("reminders", "sleepReminderEnabled", sleepReminderEnabled.value);

      // 保存时间设置
      await HiveService.saveData("reminders", "breakfastTime", breakfastTime.value);
      await HiveService.saveData("reminders", "lunchTime", lunchTime.value);
      await HiveService.saveData("reminders", "dinnerTime", dinnerTime.value);
      await HiveService.saveData("reminders", "exerciseTime", exerciseTime.value);
      await HiveService.saveData("reminders", "weightRecordTime", weightRecordTime.value);
      await HiveService.saveData("reminders", "sleepTime", sleepTime.value);

      // 保存其他设置
      await HiveService.saveData("reminders", "waterReminderInterval", waterReminderInterval.value);
      await HiveService.saveData("reminders", "weightRecordDays", weightRecordDays.toList());
    } catch (e) {
      // ignore: avoid_print
      print('保存提醒设置失败: $e');
    }
  }

  /// 手动保存所有设置
  Future<void> saveAllSettings() async {
    try {
      isSaving.value = true;
      await _saveSettings();

      Get.snackbar(
        '保存成功',
        '提醒设置已保存',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      Get.snackbar(
        '保存失败',
        '保存提醒设置失败: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    } finally {
      isSaving.value = false;
    }
  }

  /// 重置所有设置
  void resetAllSettings() {
    mealReminderEnabled.value = true;
    waterReminderEnabled.value = true;
    exerciseReminderEnabled.value = false;
    weightRecordReminderEnabled.value = true;
    sleepReminderEnabled.value = false;

    breakfastTime.value = '08:00';
    lunchTime.value = '12:00';
    dinnerTime.value = '18:00';
    exerciseTime.value = '19:00';
    weightRecordTime.value = '07:00';
    sleepTime.value = '22:00';

    waterReminderInterval.value = 120;
    weightRecordDays.value = ['周一', '周三', '周五'];

    _saveSettings();
  }

  /// 获取喝水间隔描述
  String getWaterIntervalDescription(int interval) {
    if (interval < 60) {
      // ignore: unnecessary_brace_in_string_interps
      return '${interval}分钟';
    } else {
      final hours = interval / 60;
      if (hours == hours.toInt()) {
        return '${hours.toInt()}小时';
      } else {
        return '${hours.toStringAsFixed(1)}小时';
      }
    }
  }
}

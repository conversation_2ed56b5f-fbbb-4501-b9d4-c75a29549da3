import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../shared/widgets/common_app_bar.dart';
import '../controllers/reminder_setting_controller.dart';

class ReminderSettingView extends GetView<ReminderSettingController> {
  const ReminderSettingView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: SmartAppBar(
        title: '提醒设置',
        actions: [
          TextButton(
            onPressed: controller.resetAllSettings,
            child: const Text('重置', style: TextStyle(color: Colors.white, fontSize: 14)),
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              _buildMealReminderSection(),
              const SizedBox(height: 16),
              _buildWaterReminderSection(),
              const SizedBox(height: 16),
              _buildExerciseReminderSection(),
              const SizedBox(height: 16),
              _buildWeightRecordReminderSection(),
              const SizedBox(height: 16),
              _buildSleepReminderSection(),
              const SizedBox(height: 24),
              _buildSaveButton(),
              const SizedBox(height: 20),
            ],
          ),
        );
      }),
    );
  }

  /// 构建餐食提醒设置
  Widget _buildMealReminderSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.restaurant, color: Colors.orange.shade600, size: 24),
              const SizedBox(width: 8),
              const Text('餐食提醒', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
              const Spacer(),
              Obx(
                () => Switch(
                  value: controller.mealReminderEnabled.value,
                  onChanged: controller.toggleMealReminder,
                  activeColor: Colors.orange.shade600,
                ),
              ),
            ],
          ),

          Obx(
            () =>
                controller.mealReminderEnabled.value
                    ? Column(
                      children: [
                        const SizedBox(height: 16),
                        _buildMealTimeItem('早餐', controller.breakfastTime.value, 'breakfast'),
                        _buildMealTimeItem('午餐', controller.lunchTime.value, 'lunch'),
                        _buildMealTimeItem('晚餐', controller.dinnerTime.value, 'dinner'),
                      ],
                    )
                    : const SizedBox(),
          ),
        ],
      ),
    );
  }

  /// 构建餐食时间项目
  Widget _buildMealTimeItem(String mealName, String time, String mealType) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange.shade100),
      ),
      child: Row(
        children: [
          Text(mealName, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
          const Spacer(),
          GestureDetector(
            onTap: () => controller.setMealTime(mealType),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(color: Colors.orange.shade600, borderRadius: BorderRadius.circular(8)),
              child: Text(time, style: const TextStyle(color: Colors.white, fontWeight: FontWeight.w500)),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建喝水提醒设置
  Widget _buildWaterReminderSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.water_drop, color: Colors.blue.shade600, size: 24),
              const SizedBox(width: 8),
              const Text('喝水提醒', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
              const Spacer(),
              Obx(
                () => Switch(
                  value: controller.waterReminderEnabled.value,
                  onChanged: controller.toggleWaterReminder,
                  activeColor: Colors.blue.shade600,
                ),
              ),
            ],
          ),

          Obx(
            () =>
                controller.waterReminderEnabled.value
                    ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 16),
                        const Text('提醒间隔', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
                        const SizedBox(height: 12),
                        Wrap(
                          spacing: 12,
                          runSpacing: 8,
                          children:
                              controller.waterIntervalOptions.map((interval) {
                                final isSelected = controller.waterReminderInterval.value == interval;
                                return GestureDetector(
                                  onTap: () => controller.setWaterReminderInterval(interval),
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                    decoration: BoxDecoration(
                                      color: isSelected ? Colors.blue.shade600 : Colors.blue.shade50,
                                      borderRadius: BorderRadius.circular(20),
                                      border: Border.all(
                                        color: isSelected ? Colors.blue.shade600 : Colors.blue.shade200,
                                      ),
                                    ),
                                    child: Text(
                                      controller.getWaterIntervalDescription(interval),
                                      style: TextStyle(
                                        color: isSelected ? Colors.white : Colors.blue.shade700,
                                        fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                                      ),
                                    ),
                                  ),
                                );
                              }).toList(),
                        ),
                      ],
                    )
                    : const SizedBox(),
          ),
        ],
      ),
    );
  }

  /// 构建运动提醒设置
  Widget _buildExerciseReminderSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.fitness_center, color: Colors.green.shade600, size: 24),
              const SizedBox(width: 8),
              const Text('运动提醒', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
              const Spacer(),
              Obx(
                () => Switch(
                  value: controller.exerciseReminderEnabled.value,
                  onChanged: controller.toggleExerciseReminder,
                  activeColor: Colors.green.shade600,
                ),
              ),
            ],
          ),

          Obx(
            () =>
                controller.exerciseReminderEnabled.value
                    ? Column(
                      children: [
                        const SizedBox(height: 16),
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.green.shade50,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: Colors.green.shade100),
                          ),
                          child: Row(
                            children: [
                              const Text('运动时间', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
                              const Spacer(),
                              GestureDetector(
                                onTap: controller.setExerciseTime,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                  decoration: BoxDecoration(
                                    color: Colors.green.shade600,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Obx(
                                    () => Text(
                                      controller.exerciseTime.value,
                                      style: const TextStyle(color: Colors.white, fontWeight: FontWeight.w500),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    )
                    : const SizedBox(),
          ),
        ],
      ),
    );
  }

  /// 构建体重记录提醒设置
  Widget _buildWeightRecordReminderSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.scale, color: Colors.purple.shade600, size: 24),
              const SizedBox(width: 8),
              const Text('体重记录提醒', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
              const Spacer(),
              Obx(
                () => Switch(
                  value: controller.weightRecordReminderEnabled.value,
                  onChanged: controller.toggleWeightRecordReminder,
                  activeColor: Colors.purple.shade600,
                ),
              ),
            ],
          ),

          Obx(
            () =>
                controller.weightRecordReminderEnabled.value
                    ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 16),

                        // 时间设置
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.purple.shade50,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: Colors.purple.shade100),
                          ),
                          child: Row(
                            children: [
                              const Text('提醒时间', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
                              const Spacer(),
                              GestureDetector(
                                onTap: controller.setWeightRecordTime,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                  decoration: BoxDecoration(
                                    color: Colors.purple.shade600,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Obx(
                                    () => Text(
                                      controller.weightRecordTime.value,
                                      style: const TextStyle(color: Colors.white, fontWeight: FontWeight.w500),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 16),

                        // 日期选择
                        const Text('提醒日期', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
                        const SizedBox(height: 12),
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children:
                              controller.weekDays.map((day) {
                                return Obx(() {
                                  final isSelected = controller.weightRecordDays.contains(day);
                                  return GestureDetector(
                                    onTap: () => controller.toggleWeightRecordDay(day),
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                      decoration: BoxDecoration(
                                        color: isSelected ? Colors.purple.shade600 : Colors.purple.shade50,
                                        borderRadius: BorderRadius.circular(16),
                                        border: Border.all(
                                          color: isSelected ? Colors.purple.shade600 : Colors.purple.shade200,
                                        ),
                                      ),
                                      child: Text(
                                        day,
                                        style: TextStyle(
                                          color: isSelected ? Colors.white : Colors.purple.shade700,
                                          fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ),
                                  );
                                });
                              }).toList(),
                        ),
                      ],
                    )
                    : const SizedBox(),
          ),
        ],
      ),
    );
  }

  /// 构建睡眠提醒设置
  Widget _buildSleepReminderSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.bedtime, color: Colors.indigo.shade600, size: 24),
              const SizedBox(width: 8),
              const Text('睡眠提醒', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
              const Spacer(),
              Obx(
                () => Switch(
                  value: controller.sleepReminderEnabled.value,
                  onChanged: controller.toggleSleepReminder,
                  activeColor: Colors.indigo.shade600,
                ),
              ),
            ],
          ),

          Obx(
            () =>
                controller.sleepReminderEnabled.value
                    ? Column(
                      children: [
                        const SizedBox(height: 16),
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.indigo.shade50,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: Colors.indigo.shade100),
                          ),
                          child: Row(
                            children: [
                              const Text('睡眠时间', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
                              const Spacer(),
                              GestureDetector(
                                onTap: controller.setSleepTime,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                  decoration: BoxDecoration(
                                    color: Colors.indigo.shade600,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Obx(
                                    () => Text(
                                      controller.sleepTime.value,
                                      style: const TextStyle(color: Colors.white, fontWeight: FontWeight.w500),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    )
                    : const SizedBox(),
          ),
        ],
      ),
    );
  }

  /// 构建保存按钮
  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: Obx(
        () => ElevatedButton(
          onPressed: controller.isSaving.value ? null : controller.saveAllSettings,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue.shade600,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
          child:
              controller.isSaving.value
                  ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                  : const Text('保存设置', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/services/user_guide_service.dart';
import '../../../data/services/hive/hive_service.dart';

/// 隐私设置控制器
class PrivacySettingController extends GetxController {
  // 隐私设置开关
  final RxBool dataAnalyticsEnabled = true.obs;
  final RxBool crashReportingEnabled = true.obs;
  final RxBool personalizedAdsEnabled = false.obs;
  final RxBool locationTrackingEnabled = false.obs;
  final RxBool biometricAuthEnabled = false.obs;
  final RxBool autoBackupEnabled = true.obs;
  final RxBool shareDataWithPartnersEnabled = false.obs;

  // 数据保留设置
  final RxString dataRetentionPeriod = '1年'.obs;
  final List<String> retentionOptions = ['3个月', '6个月', '1年', '2年', '永久保留'];

  // 账户安全设置
  final RxBool twoFactorAuthEnabled = false.obs;
  final RxBool loginNotificationsEnabled = true.obs;
  final RxBool deviceManagementEnabled = true.obs;

  // 加载状态
  final RxBool isLoading = true.obs;
  final RxBool isSaving = false.obs;
  final RxBool isDeleting = false.obs;

  @override
  void onInit() {
    super.onInit();
    _loadPrivacySettings();
  }

  /// 加载隐私设置
  Future<void> _loadPrivacySettings() async {
    try {
      isLoading.value = true;

      // 从Hive加载隐私设置
      final analytics = await HiveService.getData("privacy", "dataAnalyticsEnabled");
      final crashReporting = await HiveService.getData("privacy", "crashReportingEnabled");
      final personalizedAds = await HiveService.getData("privacy", "personalizedAdsEnabled");
      final locationTracking = await HiveService.getData("privacy", "locationTrackingEnabled");
      final biometricAuth = await HiveService.getData("privacy", "biometricAuthEnabled");
      final autoBackup = await HiveService.getData("privacy", "autoBackupEnabled");
      final shareData = await HiveService.getData("privacy", "shareDataWithPartnersEnabled");

      if (analytics != null) dataAnalyticsEnabled.value = analytics;
      if (crashReporting != null) crashReportingEnabled.value = crashReporting;
      if (personalizedAds != null) personalizedAdsEnabled.value = personalizedAds;
      if (locationTracking != null) locationTrackingEnabled.value = locationTracking;
      if (biometricAuth != null) biometricAuthEnabled.value = biometricAuth;
      if (autoBackup != null) autoBackupEnabled.value = autoBackup;
      if (shareData != null) shareDataWithPartnersEnabled.value = shareData;

      // 加载其他设置
      final retention = await HiveService.getData("privacy", "dataRetentionPeriod");
      if (retention != null) dataRetentionPeriod.value = retention;

      final twoFactor = await HiveService.getData("privacy", "twoFactorAuthEnabled");
      final loginNotifications = await HiveService.getData("privacy", "loginNotificationsEnabled");
      final deviceManagement = await HiveService.getData("privacy", "deviceManagementEnabled");

      if (twoFactor != null) twoFactorAuthEnabled.value = twoFactor;
      if (loginNotifications != null) loginNotificationsEnabled.value = loginNotifications;
      if (deviceManagement != null) deviceManagementEnabled.value = deviceManagement;
    } catch (e) {
      // ignore: avoid_print
      print('加载隐私设置失败: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// 切换数据分析
  void toggleDataAnalytics(bool value) {
    dataAnalyticsEnabled.value = value;
    _saveSettings();
  }

  /// 切换崩溃报告
  void toggleCrashReporting(bool value) {
    crashReportingEnabled.value = value;
    _saveSettings();
  }

  /// 切换个性化广告
  void togglePersonalizedAds(bool value) {
    personalizedAdsEnabled.value = value;
    _saveSettings();
  }

  /// 切换位置跟踪
  void toggleLocationTracking(bool value) {
    locationTrackingEnabled.value = value;
    _saveSettings();
  }

  /// 切换生物识别认证
  void toggleBiometricAuth(bool value) {
    biometricAuthEnabled.value = value;
    _saveSettings();
  }

  /// 切换自动备份
  void toggleAutoBackup(bool value) {
    autoBackupEnabled.value = value;
    _saveSettings();
  }

  /// 切换与合作伙伴共享数据
  void toggleShareDataWithPartners(bool value) {
    shareDataWithPartnersEnabled.value = value;
    _saveSettings();
  }

  /// 切换双因素认证
  void toggleTwoFactorAuth(bool value) {
    twoFactorAuthEnabled.value = value;
    _saveSettings();
  }

  /// 切换登录通知
  void toggleLoginNotifications(bool value) {
    loginNotificationsEnabled.value = value;
    _saveSettings();
  }

  /// 切换设备管理
  void toggleDeviceManagement(bool value) {
    deviceManagementEnabled.value = value;
    _saveSettings();
  }

  /// 设置数据保留期限
  void setDataRetentionPeriod(String period) {
    dataRetentionPeriod.value = period;
    _saveSettings();
  }

  /// 查看隐私政策
  void viewPrivacyPolicy() {
    Get.dialog(
      AlertDialog(
        title: const Text('隐私政策'),
        content: const SingleChildScrollView(
          child: Text(
            'KcalFit 隐私政策\n\n'
            '1. 信息收集\n'
            '我们收集您主动提供的信息，包括个人资料、健康数据和使用偏好。\n\n'
            '2. 信息使用\n'
            '• 提供个性化的健康建议\n'
            '• 改进应用功能和用户体验\n'
            '• 发送重要通知和更新\n\n'
            '3. 信息保护\n'
            '• 采用行业标准的加密技术\n'
            '• 定期进行安全审计\n'
            '• 严格限制数据访问权限\n\n'
            '4. 信息共享\n'
            '我们不会向第三方出售您的个人信息。仅在法律要求或您明确同意的情况下才会共享数据。\n\n'
            '5. 您的权利\n'
            '• 访问和更新个人信息\n'
            '• 删除账户和数据\n'
            '• 控制数据使用方式\n\n'
            '如有疑问，请联系我们的隐私团队。',
            style: TextStyle(height: 1.5),
          ),
        ),
        actions: [TextButton(onPressed: () => Get.back(), child: const Text('我知道了'))],
      ),
    );
  }

  /// 查看数据使用详情
  void viewDataUsageDetails() {
    Get.dialog(
      AlertDialog(
        title: const Text('数据使用详情'),
        content: const SingleChildScrollView(
          child: Text(
            '数据收集和使用说明\n\n'
            '必要数据：\n'
            '• 账户信息（用户名、邮箱）\n'
            '• 基本健康数据（身高、体重、年龄）\n'
            '• 饮食记录数据\n\n'
            '可选数据：\n'
            '• 位置信息（用于附近餐厅推荐）\n'
            '• 设备信息（用于性能优化）\n'
            '• 使用统计（用于功能改进）\n\n'
            '数据处理：\n'
            '• 本地存储：敏感数据优先本地存储\n'
            '• 云端同步：仅在您同意时进行\n'
            '• 匿名化：统计数据会去除个人标识\n\n'
            '数据安全：\n'
            '• 端到端加密传输\n'
            '• 定期安全备份\n'
            '• 访问日志记录',
            style: TextStyle(height: 1.5),
          ),
        ),
        actions: [TextButton(onPressed: () => Get.back(), child: const Text('我知道了'))],
      ),
    );
  }

  /// 导出个人数据
  void exportPersonalData() {
    Get.dialog(
      AlertDialog(
        title: const Text('导出个人数据'),
        content: const Text(
          '我们将为您准备一份包含所有个人数据的文件。\n\n'
          '导出内容包括：\n'
          '• 个人资料信息\n'
          '• 饮食记录数据\n'
          '• 健康目标设置\n'
          '• 应用使用记录\n\n'
          '数据将以JSON格式提供，预计需要1-3个工作日准备完成。',
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('取消')),
          ElevatedButton(
            onPressed: () {
              Get.back();
              Get.snackbar(
                '请求已提交',
                '我们将在1-3个工作日内将数据发送到您的邮箱',
                snackPosition: SnackPosition.TOP,
                backgroundColor: Colors.green.shade100,
                colorText: Colors.green.shade800,
              );
            },
            child: const Text('确认导出'),
          ),
        ],
      ),
    );
  }

  /// 删除所有数据
  void deleteAllData() {
    Get.dialog(
      AlertDialog(
        title: const Text('删除所有数据'),
        content: const Text(
          '⚠️ 警告：此操作不可撤销！\n\n'
          '删除后将清除：\n'
          '• 所有个人资料\n'
          '• 所有饮食记录\n'
          '• 所有健康数据\n'
          '• 应用设置和偏好\n\n'
          '您确定要继续吗？',
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('取消')),
          ElevatedButton(
            onPressed: _confirmDeleteAllData,
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red, foregroundColor: Colors.white),
            child: const Text('确认删除'),
          ),
        ],
      ),
    );
  }

  /// 确认删除所有数据
  Future<void> _confirmDeleteAllData() async {
    Get.back(); // 关闭对话框

    try {
      isDeleting.value = true;

      // 模拟删除过程
      await Future.delayed(const Duration(seconds: 2));

      // 清除所有本地数据
      await HiveService.clearBox("user");
      await HiveService.clearBox("bodyData");
      await HiveService.clearBox("goals");
      await HiveService.clearBox("reminders");
      await HiveService.clearBox("privacy");
      await HiveService.clearBox("jpush");

      Get.snackbar(
        '删除完成',
        '所有数据已成功删除',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
      );

      // 返回到登录页面或首页
      Get.offAllNamed('/home');
    } catch (e) {
      Get.snackbar(
        '删除失败',
        '删除数据时发生错误: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    } finally {
      isDeleting.value = false;
    }
  }

  /// 保存设置
  Future<void> _saveSettings() async {
    try {
      // 保存隐私设置
      await HiveService.saveData("privacy", "dataAnalyticsEnabled", dataAnalyticsEnabled.value);
      await HiveService.saveData("privacy", "crashReportingEnabled", crashReportingEnabled.value);
      await HiveService.saveData("privacy", "personalizedAdsEnabled", personalizedAdsEnabled.value);
      await HiveService.saveData("privacy", "locationTrackingEnabled", locationTrackingEnabled.value);
      await HiveService.saveData("privacy", "biometricAuthEnabled", biometricAuthEnabled.value);
      await HiveService.saveData("privacy", "autoBackupEnabled", autoBackupEnabled.value);
      await HiveService.saveData("privacy", "shareDataWithPartnersEnabled", shareDataWithPartnersEnabled.value);

      // 保存其他设置
      await HiveService.saveData("privacy", "dataRetentionPeriod", dataRetentionPeriod.value);
      await HiveService.saveData("privacy", "twoFactorAuthEnabled", twoFactorAuthEnabled.value);
      await HiveService.saveData("privacy", "loginNotificationsEnabled", loginNotificationsEnabled.value);
      await HiveService.saveData("privacy", "deviceManagementEnabled", deviceManagementEnabled.value);
    } catch (e) {
      // ignore: avoid_print
      print('保存隐私设置失败: $e');
    }
  }

  /// 手动保存所有设置
  Future<void> saveAllSettings() async {
    try {
      isSaving.value = true;
      await _saveSettings();

      Get.snackbar(
        '保存成功',
        '隐私设置已保存',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      Get.snackbar(
        '保存失败',
        '保存隐私设置失败: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    } finally {
      isSaving.value = false;
    }
  }

  /// 重置所有设置
  void resetAllSettings() {
    dataAnalyticsEnabled.value = true;
    crashReportingEnabled.value = true;
    personalizedAdsEnabled.value = false;
    locationTrackingEnabled.value = false;
    biometricAuthEnabled.value = false;
    autoBackupEnabled.value = true;
    shareDataWithPartnersEnabled.value = false;

    dataRetentionPeriod.value = '1年';
    twoFactorAuthEnabled.value = false;
    loginNotificationsEnabled.value = true;
    deviceManagementEnabled.value = true;

    _saveSettings();
  }

  /// 重置用户引导
  void resetUserGuide() {
    Get.dialog(
      AlertDialog(
        title: const Text('重置用户引导'),
        content: const Text(
          '重置后，您将重新看到首次使用时的引导流程。\n\n'
          '这包括：\n'
          '• 身体数据录入引导\n'
          '• 功能介绍提示\n'
          '• 使用帮助信息\n\n'
          '确定要重置吗？',
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('取消')),
          ElevatedButton(
            onPressed: () async {
              Get.back();
              await _performResetUserGuide();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange.shade600, foregroundColor: Colors.white),
            child: const Text('确认重置'),
          ),
        ],
      ),
    );
  }

  /// 执行重置用户引导
  Future<void> _performResetUserGuide() async {
    try {
      if (Get.isRegistered<UserGuideService>()) {
        final userGuideService = Get.find<UserGuideService>();
        await userGuideService.resetGuideStatus();

        Get.snackbar(
          '重置成功',
          '用户引导已重置，重启应用后生效',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
          duration: const Duration(seconds: 3),
        );
      } else {
        Get.snackbar(
          '重置失败',
          '用户引导服务未初始化',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    } catch (e) {
      Get.snackbar(
        '重置失败',
        '重置用户引导时发生错误: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../shared/widgets/common_app_bar.dart';
import '../controllers/privacy_setting_controller.dart';

class PrivacySettingView extends GetView<PrivacySettingController> {
  const PrivacySettingView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: SmartAppBar(
        title: '隐私设置',
        actions: [
          TextButton(
            onPressed: controller.resetAllSettings,
            child: const Text('重置', style: TextStyle(color: Colors.white, fontSize: 14)),
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              _buildDataPrivacySection(),
              const SizedBox(height: 16),
              _buildSecuritySection(),
              const SizedBox(height: 16),
              _buildDataManagementSection(),
              const SizedBox(height: 16),
              _buildLegalSection(),
              const SizedBox(height: 24),
              _buildSaveButton(),
              const SizedBox(height: 20),
            ],
          ),
        );
      }),
    );
  }

  /// 构建数据隐私设置
  Widget _buildDataPrivacySection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.privacy_tip, color: Colors.blue.shade600, size: 24),
              const SizedBox(width: 8),
              const Text('数据隐私', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
            ],
          ),
          const SizedBox(height: 16),

          _buildPrivacyToggleItem(
            '数据分析',
            '允许收集匿名使用数据以改进应用',
            controller.dataAnalyticsEnabled,
            controller.toggleDataAnalytics,
            Icons.analytics,
          ),

          _buildPrivacyToggleItem(
            '崩溃报告',
            '自动发送崩溃报告帮助修复问题',
            controller.crashReportingEnabled,
            controller.toggleCrashReporting,
            Icons.bug_report,
          ),

          _buildPrivacyToggleItem(
            '个性化广告',
            '基于使用习惯显示相关广告',
            controller.personalizedAdsEnabled,
            controller.togglePersonalizedAds,
            Icons.ads_click,
          ),

          _buildPrivacyToggleItem(
            '位置跟踪',
            '使用位置信息提供本地化服务',
            controller.locationTrackingEnabled,
            controller.toggleLocationTracking,
            Icons.location_on,
          ),

          _buildPrivacyToggleItem(
            '与合作伙伴共享',
            '与可信合作伙伴共享匿名数据',
            controller.shareDataWithPartnersEnabled,
            controller.toggleShareDataWithPartners,
            Icons.share,
            isLast: true,
          ),
        ],
      ),
    );
  }

  /// 构建安全设置
  Widget _buildSecuritySection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.security, color: Colors.green.shade600, size: 24),
              const SizedBox(width: 8),
              const Text('账户安全', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
            ],
          ),
          const SizedBox(height: 16),

          _buildPrivacyToggleItem(
            '生物识别认证',
            '使用指纹或面部识别登录',
            controller.biometricAuthEnabled,
            controller.toggleBiometricAuth,
            Icons.fingerprint,
          ),

          _buildPrivacyToggleItem(
            '双因素认证',
            '登录时需要额外验证步骤',
            controller.twoFactorAuthEnabled,
            controller.toggleTwoFactorAuth,
            Icons.verified_user,
          ),

          _buildPrivacyToggleItem(
            '登录通知',
            '新设备登录时发送通知',
            controller.loginNotificationsEnabled,
            controller.toggleLoginNotifications,
            Icons.notifications_active,
          ),

          _buildPrivacyToggleItem(
            '设备管理',
            '管理已登录的设备列表',
            controller.deviceManagementEnabled,
            controller.toggleDeviceManagement,
            Icons.devices,
          ),

          _buildPrivacyToggleItem(
            '自动备份',
            '定期备份数据到云端',
            controller.autoBackupEnabled,
            controller.toggleAutoBackup,
            Icons.backup,
            isLast: true,
          ),
        ],
      ),
    );
  }

  /// 构建数据管理设置
  Widget _buildDataManagementSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.storage, color: Colors.orange.shade600, size: 24),
              const SizedBox(width: 8),
              const Text('数据管理', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
            ],
          ),
          const SizedBox(height: 16),

          // 数据保留期限
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.orange.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.orange.shade100),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.schedule, color: Colors.orange.shade600, size: 20),
                    const SizedBox(width: 8),
                    const Text('数据保留期限', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
                  ],
                ),
                const SizedBox(height: 12),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children:
                      controller.retentionOptions.map((option) {
                        return Obx(() {
                          final isSelected = controller.dataRetentionPeriod.value == option;
                          return GestureDetector(
                            onTap: () => controller.setDataRetentionPeriod(option),
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                              decoration: BoxDecoration(
                                color: isSelected ? Colors.orange.shade600 : Colors.white,
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(color: isSelected ? Colors.orange.shade600 : Colors.orange.shade300),
                              ),
                              child: Text(
                                option,
                                style: TextStyle(
                                  color: isSelected ? Colors.white : Colors.orange.shade700,
                                  fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          );
                        });
                      }).toList(),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // 数据操作按钮
          _buildDataActionButton(
            '查看数据使用详情',
            '了解我们如何收集和使用您的数据',
            Icons.info_outline,
            Colors.blue.shade600,
            controller.viewDataUsageDetails,
          ),

          const SizedBox(height: 12),

          _buildDataActionButton(
            '导出个人数据',
            '下载您的所有个人数据副本',
            Icons.download,
            Colors.green.shade600,
            controller.exportPersonalData,
          ),

          const SizedBox(height: 12),

          _buildDataActionButton(
            '重置用户引导',
            '重新显示首次使用引导流程',
            Icons.refresh,
            Colors.orange.shade600,
            controller.resetUserGuide,
          ),

          const SizedBox(height: 12),

          _buildDataActionButton(
            '删除所有数据',
            '永久删除您的账户和所有数据',
            Icons.delete_forever,
            Colors.red.shade600,
            controller.deleteAllData,
          ),
        ],
      ),
    );
  }

  /// 构建法律信息设置
  Widget _buildLegalSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.gavel, color: Colors.purple.shade600, size: 24),
              const SizedBox(width: 8),
              const Text('法律信息', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
            ],
          ),
          const SizedBox(height: 16),

          _buildLegalItem('隐私政策', '查看完整的隐私政策文档', Icons.privacy_tip, controller.viewPrivacyPolicy),
        ],
      ),
    );
  }

  /// 构建隐私开关项目
  Widget _buildPrivacyToggleItem(
    String title,
    String description,
    RxBool value,
    Function(bool) onChanged,
    IconData icon, {
    bool isLast = false,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: isLast ? 0 : 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(color: Colors.grey[50], borderRadius: BorderRadius.circular(12)),
      child: Row(
        children: [
          Icon(icon, color: Colors.grey[600], size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
                const SizedBox(height: 4),
                Text(description, style: TextStyle(fontSize: 14, color: Colors.grey[600])),
              ],
            ),
          ),
          const SizedBox(width: 12),
          Obx(() => Switch(value: value.value, onChanged: onChanged, activeColor: Colors.blue.shade600)),
        ],
      ),
    );
  }

  /// 构建数据操作按钮
  Widget _buildDataActionButton(String title, String description, IconData icon, Color color, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(title, style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: color)),
                  const SizedBox(height: 4),
                  Text(description, style: TextStyle(fontSize: 14, color: Colors.grey[600])),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey[400]),
          ],
        ),
      ),
    );
  }

  /// 构建法律信息项目
  Widget _buildLegalItem(String title, String description, IconData icon, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.purple.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.purple.shade100),
        ),
        child: Row(
          children: [
            Icon(icon, color: Colors.purple.shade600, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: Colors.purple.shade700),
                  ),
                  const SizedBox(height: 4),
                  Text(description, style: TextStyle(fontSize: 14, color: Colors.grey[600])),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey[400]),
          ],
        ),
      ),
    );
  }

  /// 构建保存按钮
  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: Obx(
        () => ElevatedButton(
          onPressed: controller.isSaving.value ? null : controller.saveAllSettings,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue.shade600,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
          child:
              controller.isSaving.value
                  ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                  : const Text('保存设置', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
        ),
      ),
    );
  }
}

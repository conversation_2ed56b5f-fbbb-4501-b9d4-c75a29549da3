import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../shared/widgets/common_app_bar.dart';
import '../controllers/health_detail_controller.dart';
import '../widgets/health_chart_widget.dart';
import '../widgets/health_list_widget.dart';

/// 健康数据详情页面
class HealthDetailView extends GetView<HealthDetailController> {
  const HealthDetailView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: SmartAppBar(
        title: controller.config.title,
        actions: [IconButton(icon: const Icon(Icons.refresh), onPressed: () => controller.refreshData())],
      ),
      body: Obx(() {
        if (controller.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (!controller.hasData) {
          return _buildEmptyState();
        }

        return _buildContent();
      }),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(controller.config.icon, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(controller.config.emptyMessage, style: TextStyle(fontSize: 16, color: Colors.grey[600])),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => controller.refreshData(),
            icon: const Icon(Icons.refresh),
            label: const Text('刷新数据'),
            style: ElevatedButton.styleFrom(backgroundColor: controller.config.color, foregroundColor: Colors.white),
          ),
        ],
      ),
    );
  }

  /// 构建内容
  Widget _buildContent() {
    return RefreshIndicator(
      onRefresh: () => controller.refreshData(),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSummaryCard(),
            const SizedBox(height: 20),
            _buildChartSection(),
            const SizedBox(height: 20),
            _buildListSection(),
          ],
        ),
      ),
    );
  }

  /// 构建汇总卡片
  Widget _buildSummaryCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [controller.config.color.withValues(alpha: 0.1), controller.config.color.withValues(alpha: 0.05)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: controller.config.color.withValues(alpha: 0.2), width: 1),
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: controller.config.color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(controller.config.icon, color: controller.config.color, size: 30),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('今日总计', style: TextStyle(fontSize: 14, color: Colors.grey[600], fontWeight: FontWeight.w500)),
                const SizedBox(height: 4),
                Obx(
                  () => Text(
                    controller.getFormattedValue(),
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: controller.config.color),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建图表部分
  Widget _buildChartSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(controller.config.chartTitle, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
        const SizedBox(height: 12),
        Container(
          height: 250,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(color: Colors.black.withValues(alpha: 0.05), blurRadius: 10, offset: const Offset(0, 2)),
            ],
          ),
          child: HealthChartWidget(
            data: controller.getChartData(),
            dataType: controller.dataType,
            color: controller.config.color,
          ),
        ),
      ],
    );
  }

  /// 构建列表部分
  Widget _buildListSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('详细记录', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
        const SizedBox(height: 12),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(color: Colors.black.withValues(alpha: 0.05), blurRadius: 10, offset: const Offset(0, 2)),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: HealthListWidget(
              data: controller.getListData(),
              dataType: controller.dataType,
              color: controller.config.color,
            ),
          ),
        ),
      ],
    );
  }
}

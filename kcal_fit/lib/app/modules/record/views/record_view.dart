import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../shared/widgets/common_app_bar.dart';
import '../controllers/record_controller.dart';

class RecordView extends GetView<RecordController> {
  const RecordView({super.key});

  Color _getMealColor(String tag) {
    switch (tag) {
      case '早餐':
        return const Color(0xFFFF9800); // 橙色
      case '午餐':
        return const Color(0xFF4CAF50); // 绿色
      case '晚餐':
        return const Color(0xFF2196F3); // 蓝色
      default:
        return const Color(0xFF9C27B0); // 紫色
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: SmartAppBar(title: controller.date),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: RefreshIndicator(
                onRefresh: () async {
                  controller.refreshData();
                },
                child: Obx(() {
                  if (controller.isLoading.value) {
                    return _buildLoadingState();
                  }

                  return controller.todayAnalysis.isEmpty
                      ? _buildEmptyState()
                      : ListView.separated(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        physics: const AlwaysScrollableScrollPhysics(),
                        shrinkWrap: false, // 允许列表收缩以适应内容

                        itemCount: controller.todayAnalysis.length,
                        separatorBuilder: (context, index) => const SizedBox(height: 12),
                        itemBuilder: (context, index) {
                          final item = controller.todayAnalysis[index];
                          return Dismissible(
                            key: Key(item.dietDescription),
                            direction: DismissDirection.endToStart,
                            background: Container(
                              margin: const EdgeInsets.all(0),
                              decoration: BoxDecoration(
                                color: Colors.red.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              alignment: Alignment.centerRight,
                              padding: const EdgeInsets.only(right: 20),
                              child: Icon(Icons.delete, color: Colors.red[600]),
                            ),
                            // 确认删除对话框
                            confirmDismiss: (direction) async {
                              return await showDialog(
                                context: context,
                                builder: (BuildContext context) {
                                  return AlertDialog(
                                    title: const Text("确认删除"),
                                    content: const Text("确定要删除这条记录吗？"),
                                    actions: <Widget>[
                                      TextButton(
                                        onPressed: () => Navigator.of(context).pop(false),
                                        child: const Text("取消"),
                                      ),
                                      TextButton(
                                        onPressed: () => Navigator.of(context).pop(true),
                                        child: const Text("删除"),
                                      ),
                                    ],
                                  );
                                },
                              );
                            },
                            // 滑动删除回调
                            onDismissed: (direction) {
                              controller.removeRecord(index);
                            },
                            child: _buildMealCard(context, item),
                          );
                        },
                      );
                }),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建餐次卡片
  Widget _buildMealCard(BuildContext context, item) {
    final foods = controller.getFoodsByAnalyzeId(item.analyzeId);

    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.white, Colors.grey[50]!],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 6,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            controller.showRecordDetails(item);
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 卡片头部：餐次类型和总热量
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: _getMealColor(item.mealType ?? '').withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        controller.getTagIcon(item.mealType ?? ''),
                        color: _getMealColor(item.mealType ?? ''),
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            item.mealType ?? '未分类',
                            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Color(0xFF2D3748)),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            _formatMealTime(item.mealTime),
                            style: TextStyle(fontSize: 13, color: Colors.grey[600], fontWeight: FontWeight.w500),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            _getMealColor(item.mealType ?? ''),
                            _getMealColor(item.mealType ?? '').withValues(alpha: 0.8),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: _getMealColor(item.mealType ?? '').withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Text(
                        '${item.calories ?? 0} kcal',
                        style: const TextStyle(fontSize: 14, color: Colors.white, fontWeight: FontWeight.bold),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),

                // 食物列表
                const SizedBox(height: 12),
                if (foods.isNotEmpty) ...[
                  // 食物列表标题
                  Row(
                    children: [
                      Icon(Icons.restaurant_menu, size: 16, color: Colors.grey[600]),
                      const SizedBox(width: 6),
                      Text(
                        '包含食物 (${foods.length})',
                        style: TextStyle(fontSize: 13, fontWeight: FontWeight.w600, color: Colors.grey[700]),
                      ),
                      const Spacer(),
                      Text('点击查看营养详情', style: TextStyle(fontSize: 11, color: Colors.grey[500])),
                    ],
                  ),
                  const SizedBox(height: 8),
                  // 食物列表
                  ...foods.map((food) => _buildFoodItemInCard(context, food)),
                ] else ...[
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue[100]!, width: 1),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.blue[600], size: 16),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            item.dietDescription,
                            style: TextStyle(fontSize: 13, color: Colors.blue[800], height: 1.3),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 格式化用餐时间
  String _formatMealTime(DateTime mealTime) {
    return '${mealTime.hour.toString().padLeft(2, '0')}:${mealTime.minute.toString().padLeft(2, '0')}';
  }

  /// 构建卡片中的食物项
  Widget _buildFoodItemInCard(BuildContext context, food) {
    return Container(
      margin: const EdgeInsets.only(bottom: 6),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Row(
        children: [
          // 食物图标
          Container(
            width: 4,
            height: 4,
            decoration: BoxDecoration(color: Theme.of(context).primaryColor, shape: BoxShape.circle),
          ),
          const SizedBox(width: 10),
          // 食物名称
          Expanded(
            child: Text(
              food.foodName,
              style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500, color: Color(0xFF2D3748)),
            ),
          ),
          // 数量
          if (food.quantity != null && food.quantity! > 0) ...[
            Text(
              '${food.quantity}g',
              style: TextStyle(fontSize: 11, color: Colors.grey[600], fontWeight: FontWeight.w400),
            ),
            const SizedBox(width: 8),
          ],
          // 热量
          Text(
            '${food.calories ?? 0} kcal',
            style: TextStyle(fontSize: 11, color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  /// 构建加载状态显示
  Widget _buildLoadingState() {
    return CustomScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      slivers: [
        SliverFillRemaining(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text('正在加载饮食记录...', style: TextStyle(fontSize: 16, color: Colors.grey[600])),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 构建空状态显示
  Widget _buildEmptyState() {
    return CustomScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      slivers: [
        SliverFillRemaining(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.fastfood, size: 48, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text('暂无今日饮食记录', style: TextStyle(fontSize: 18, color: Colors.grey[600])),
                const SizedBox(height: 16),
                Text('下拉可刷新数据', style: TextStyle(fontSize: 14, color: Colors.grey[500])),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

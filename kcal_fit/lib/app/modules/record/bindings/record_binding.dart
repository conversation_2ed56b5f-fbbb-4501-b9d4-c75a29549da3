import 'package:get/get.dart';

import '../controllers/record_controller.dart';

class RecordBinding extends Bindings {
  @override
  void dependencies() {
    // 在这里注册依赖关系，例如控制器或服务
    Get.lazyPut<RecordController>(() {
      // 从路由参数获取日期，如果没有则使用今天
      final args = Get.arguments as Map<String, dynamic>?;
      final date = args?['date'] ?? DateTime.now().toString().split(" ")[0];
      return RecordController(date: date);
    });
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';

import '../../../data/models/diet_analysis.dart';
import '../../../data/models/food_analysis.dart';
import '../../../data/services/sqflite/dao/impl/diet_analysis_dao.dart';
import '../../../data/services/sqflite/dao/impl/food_analysis_dao.dart';
import '../../my/controllers/my_controller.dart';

var logger = Logger();

class RecordController extends GetxController {
  String date;
  RecordController({required this.date});

  /// 饮食分析数据访问对象
  late final DietAnalysisDao _dietAnalysisDao;

  /// 食物分析数据访问对象
  late final FoodAnalysisDao _foodAnalysisDao;

  /// 今天的饮食记录
  final todayAnalysis = <DietAnalysis>[].obs;

  /// 食物记录映射表 (analyze_id -> 食物列表)
  final foodRecordsMap = <String, List<FoodAnalysis>>{}.obs;

  /// 数据加载状态
  final isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();

    // 初始化DAO
    _dietAnalysisDao = Get.find<DietAnalysisDao>();
    _foodAnalysisDao = Get.find<FoodAnalysisDao>();

    // 获取路由参数
    final args = Get.arguments as Map<String, dynamic>?;
    if (args != null && args.containsKey('date')) {
      date = args['date'];
    }

    // 延迟加载数据，确保UI已经构建完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      getTodayAnalysis();
    });
  }

  @override
  void onReady() {
    super.onReady();
    // 备用数据加载机制，确保数据一定会被加载
    if (todayAnalysis.isEmpty) {
      logger.i('onReady: 检测到数据为空，重新加载数据');
      getTodayAnalysis();
    }
  }

  /// 获取今日饮食分析记录
  void getTodayAnalysis() async {
    try {
      isLoading.value = true;
      logger.i('开始获取日期 $date 的饮食记录');

      // 确保日期格式正确
      if (date.isEmpty) {
        logger.w('日期为空，使用今天的日期');
        date = DateTime.now().toString().substring(0, 10);
      }

      List<DietAnalysis> list = await _dietAnalysisDao.getTodayAnalysis(date);

      logger.i('获取到 ${list.length} 条饮食记录');
      if (list.isNotEmpty) {
        for (var item in list) {
          logger.d('记录详情: ${item.toMap()}');
        }
      } else {
        logger.i('当前日期 $date 没有饮食记录');
      }

      // 更新数据，即使是空列表也要更新，这样UI会显示空状态
      todayAnalysis.value = list;

      // 获取每条记录对应的食物列表
      if (list.isNotEmpty) {
        await _loadFoodRecords(list);
      } else {
        // 清空食物记录映射
        foodRecordsMap.clear();
      }

      logger.i('饮食记录数据加载完成');
    } catch (e) {
      logger.e('获取今日饮食记录失败: $e');
      // 确保即使出错也要更新UI状态
      todayAnalysis.value = [];
      foodRecordsMap.clear();
      Get.snackbar('错误', '获取饮食记录失败: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// 加载食物记录
  Future<void> _loadFoodRecords(List<DietAnalysis> analysisList) async {
    try {
      foodRecordsMap.clear();

      for (var analysis in analysisList) {
        final foods = await _foodAnalysisDao.getFoodsByAnalyzeId(analysis.analyzeId);
        foodRecordsMap[analysis.analyzeId] = foods;
        logger.d('分析ID ${analysis.analyzeId} 对应 ${foods.length} 条食物记录');
      }
    } catch (e) {
      logger.e('加载食物记录失败: $e');
    }
  }

  /// 获取特定分析记录的食物列表
  List<FoodAnalysis> getFoodsByAnalyzeId(String analyzeId) {
    return foodRecordsMap[analyzeId] ?? [];
  }

  /// 获取餐次图标
  IconData getTagIcon(String tag) {
    switch (tag) {
      case '早餐':
        return Icons.breakfast_dining;
      case '午餐':
        return Icons.lunch_dining;
      case '晚餐':
        return Icons.dinner_dining;
      default:
        return Icons.restaurant;
    }
  }

  /// 获取餐次颜色
  Color _getMealColor(String tag) {
    switch (tag) {
      case '早餐':
        return const Color(0xFFFF9800); // 橙色
      case '午餐':
        return const Color(0xFF4CAF50); // 绿色
      case '晚餐':
        return const Color(0xFF2196F3); // 蓝色
      default:
        return const Color(0xFF9C27B0); // 紫色
    }
  }

  /// 删除指定索引的记录
  void removeRecord(int index) async {
    try {
      if (index < 0 || index >= todayAnalysis.length) {
        logger.w('删除记录失败: 索引超出范围 $index');
        return;
      }

      final record = todayAnalysis[index];
      if (record.id == null) {
        logger.w('删除记录失败: 记录ID为空');
        return;
      }

      logger.i('开始删除记录: ID=${record.id}, 描述=${record.dietDescription}');

      // 从数据库删除
      await _dietAnalysisDao.deleteRecord(record.id!);

      // 从列表中移除
      todayAnalysis.removeAt(index);

      // 刷新我的页面的统计数据
      if (Get.isRegistered<MyController>()) {
        Get.find<MyController>().loadStatisticsData();
      }

      logger.i('记录删除成功');
      Get.snackbar('成功', '记录已删除');
    } catch (e) {
      logger.e('删除记录失败: $e');
      Get.snackbar('错误', '删除记录失败: $e');
    }
  }

  /// 显示记录详情对话框
  void showRecordDetails(DietAnalysis item) async {
    try {
      logger.i('显示记录详情: ${item.toMap()}');

      // 获取该记录的食物列表
      final foods = getFoodsByAnalyzeId(item.analyzeId);

      Get.dialog(
        AlertDialog(
          title: Row(
            children: [
              Icon(getTagIcon(item.mealType ?? ''), color: _getMealColor(item.mealType ?? ''), size: 24),
              const SizedBox(width: 8),
              Expanded(child: Text('${item.mealType ?? '未分类'} 详情', style: const TextStyle(fontSize: 18))),
            ],
          ),
          content: ConstrainedBox(
            constraints: const BoxConstraints(
              maxWidth: 400,
              maxHeight: 500, // 限制最大高度
            ),
            child: SizedBox(
              width: double.maxFinite,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 基本信息
                    _buildDetailRow('用餐时间', _formatDateTime(item.mealTime)),
                    _buildDetailRow('总热量', '${item.calories ?? 0} kcal'),

                    // 食物列表
                    if (foods.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      const Text('食物清单', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
                      const SizedBox(height: 8),
                      ...foods.map((food) => _buildFoodDetailItem(food)),
                    ] else ...[
                      const SizedBox(height: 8),
                      _buildDetailRow('食物描述', item.dietDescription),
                    ],

                    const SizedBox(height: 16),
                    const Divider(),

                    // 营养信息
                    const Text('营养成分', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
                    const SizedBox(height: 8),
                    _buildNutritionGrid(item),

                    if (item.notes != null && item.notes!.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      const Divider(),
                      _buildDetailRow('备注', item.notes!),
                    ],
                  ],
                ),
              ),
            ),
          ),
          actions: [TextButton(onPressed: () => Get.back(), child: const Text('关闭'))],
        ),
      );
    } catch (e) {
      logger.e('显示记录详情失败: $e');
      Get.snackbar('错误', '显示详情失败: $e');
    }
  }

  /// 构建详情行组件
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(width: 80, child: Text('$label:', style: const TextStyle(fontWeight: FontWeight.w500))),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  /// 构建食物详情项
  Widget _buildFoodDetailItem(FoodAnalysis food) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Row(
        children: [
          Container(width: 6, height: 6, decoration: const BoxDecoration(color: Colors.blue, shape: BoxShape.circle)),
          const SizedBox(width: 12),
          Expanded(child: Text(food.foodName, style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500))),
          Text('${food.quantity ?? 0}g', style: TextStyle(fontSize: 12, color: Colors.grey[600])),
          const SizedBox(width: 12),
          Text(
            '${food.calories ?? 0} kcal',
            style: const TextStyle(fontSize: 12, color: Colors.orange, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  /// 构建营养成分展示
  Widget _buildNutritionGrid(DietAnalysis item) {
    return Column(
      children: [
        // 主要营养素 - 大卡片展示
        _buildMacroNutrients(item),
        const SizedBox(height: 16),
        // 微量营养素 - 列表展示
        _buildMicroNutrients(item),
      ],
    );
  }

  /// 构建主要营养素（蛋白质、碳水、脂肪）
  Widget _buildMacroNutrients(DietAnalysis item) {
    final macroData = [
      {
        'label': '蛋白质',
        'value': '${item.protein ?? 0}',
        'unit': 'g',
        'color': Colors.red[400],
        'icon': Icons.fitness_center,
      },
      {'label': '碳水', 'value': '${item.carbs ?? 0}', 'unit': 'g', 'color': Colors.blue[400], 'icon': Icons.grain},
      {'label': '脂肪', 'value': '${item.fat ?? 0}', 'unit': 'g', 'color': Colors.orange[400], 'icon': Icons.opacity},
    ];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.grey[50]!, Colors.white],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Row(
        children:
            macroData.map((macro) {
              return Expanded(
                child: Column(
                  children: [
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: (macro['color'] as Color).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(24),
                      ),
                      child: Icon(macro['icon'] as IconData, color: macro['color'] as Color, size: 24),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      macro['label'] as String,
                      style: TextStyle(fontSize: 12, color: Colors.grey[600], fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(height: 4),
                    RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: macro['value'] as String,
                            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: macro['color'] as Color),
                          ),
                          TextSpan(text: ' ${macro['unit']}', style: TextStyle(fontSize: 12, color: Colors.grey[500])),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
      ),
    );
  }

  /// 构建微量营养素
  Widget _buildMicroNutrients(DietAnalysis item) {
    final microData = [
      {'label': '膳食纤维', 'value': '${item.fiber ?? 0} g', 'icon': Icons.eco},
      {'label': '钠', 'value': '${item.sodium ?? 0} mg', 'icon': Icons.grain},
      {'label': '钙', 'value': '${item.calcium ?? 0} mg', 'icon': Icons.local_drink},
      {'label': '铁', 'value': '${item.iron ?? 0} mg', 'icon': Icons.fitness_center},
      {'label': '维生素A', 'value': '${item.vitaminA ?? 0} μg', 'icon': Icons.visibility},
      {'label': '维生素D', 'value': '${item.vitaminD ?? 0} μg', 'icon': Icons.wb_sunny},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('其他营养成分', style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: Colors.grey[700])),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[200]!, width: 1),
          ),
          child: Column(
            children:
                microData.asMap().entries.map((entry) {
                  final index = entry.key;
                  final micro = entry.value;
                  final isLast = index == microData.length - 1;

                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      border: isLast ? null : Border(bottom: BorderSide(color: Colors.grey[200]!, width: 0.5)),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(color: Colors.blue[50], borderRadius: BorderRadius.circular(16)),
                          child: Icon(micro['icon'] as IconData, color: Colors.blue[400], size: 16),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            micro['label'] as String,
                            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                          ),
                        ),
                        Text(
                          micro['value'] as String,
                          style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: Colors.blue[600]),
                        ),
                      ],
                    ),
                  );
                }).toList(),
          ),
        ),
      ],
    );
  }

  /// 格式化日期时间显示
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 刷新记录数据
  /// 提供给外部调用的刷新方法
  void refreshData() {
    logger.i('刷新记录页面数据');
    getTodayAnalysis();
  }

  /// 设置查看日期并刷新数据
  /// [newDate] 新的日期字符串
  void setDate(String newDate) {
    if (date != newDate) {
      logger.i('切换查看日期: $date -> $newDate');
      date = newDate;
      getTodayAnalysis();
    }
  }

  /// 添加测试数据（仅用于开发测试）
  Future<void> addTestData() async {
    try {
      logger.i('开始添加测试数据');

      final testData = [
        {
          'analyze_id': 'test_001',
          'meal_time': DateTime.now().toString(),
          'meal_type': '早餐',
          'diet_description': '全麦面包2片，煎蛋1个，牛奶1杯',
          'calories': 350,
          'protein': 18,
          'carbs': 45,
          'fat': 12,
          'fiber': 6,
          'sodium': 420,
          'tags': '早餐',
          'notes': '健康早餐搭配',
          'created_time': DateTime.now().toString(),
          'update_time': DateTime.now().toString(),
        },
        {
          'analyze_id': 'test_002',
          'meal_time': DateTime.now().subtract(const Duration(hours: 4)).toString(),
          'meal_type': '午餐',
          'diet_description': '米饭1碗，红烧肉100g，青菜炒蘑菇',
          'calories': 680,
          'protein': 25,
          'carbs': 78,
          'fat': 22,
          'fiber': 4,
          'sodium': 890,
          'tags': '午餐',
          'notes': '家常菜搭配',
          'created_time': DateTime.now().toString(),
          'update_time': DateTime.now().toString(),
        },
        {
          'analyze_id': 'test_003',
          'meal_time': DateTime.now().subtract(const Duration(hours: 8)).toString(),
          'meal_type': '晚餐',
          'diet_description': '蒸蛋羹，小米粥，凉拌黄瓜',
          'calories': 280,
          'protein': 12,
          'carbs': 42,
          'fat': 8,
          'fiber': 3,
          'sodium': 320,
          'tags': '晚餐',
          'notes': '清淡晚餐',
          'created_time': DateTime.now().toString(),
          'update_time': DateTime.now().toString(),
        },
      ];

      for (final data in testData) {
        await _dietAnalysisDao.addAnalysis(data);
      }

      // 添加对应的食物记录
      await _addTestFoodRecords();

      logger.i('测试数据添加成功，共添加 ${testData.length} 条记录');
      Get.snackbar('成功', '测试数据添加成功');

      // 刷新数据显示
      getTodayAnalysis();
    } catch (e) {
      logger.e('添加测试数据失败: $e');
      Get.snackbar('错误', '添加测试数据失败: $e');
    }
  }

  /// 添加测试食物记录
  Future<void> _addTestFoodRecords() async {
    try {
      final testFoodData = [
        // 早餐食物记录
        {
          'diet_analyze_id': 'test_001',
          'food_name': '全麦面包',
          'quantity': 100,
          'calories': 250,
          'protein': 8,
          'carbs': 45,
          'fat': 4,
        },
        {
          'diet_analyze_id': 'test_001',
          'food_name': '煎蛋',
          'quantity': 60,
          'calories': 90,
          'protein': 8,
          'carbs': 1,
          'fat': 6,
        },
        {
          'diet_analyze_id': 'test_001',
          'food_name': '牛奶',
          'quantity': 200,
          'calories': 120,
          'protein': 6,
          'carbs': 9,
          'fat': 6,
        },

        // 午餐食物记录
        {
          'diet_analyze_id': 'test_002',
          'food_name': '米饭',
          'quantity': 150,
          'calories': 200,
          'protein': 4,
          'carbs': 45,
          'fat': 1,
        },
        {
          'diet_analyze_id': 'test_002',
          'food_name': '红烧肉',
          'quantity': 100,
          'calories': 350,
          'protein': 15,
          'carbs': 8,
          'fat': 28,
        },
        {
          'diet_analyze_id': 'test_002',
          'food_name': '青菜炒蘑菇',
          'quantity': 120,
          'calories': 80,
          'protein': 4,
          'carbs': 12,
          'fat': 3,
        },

        // 晚餐食物记录
        {
          'diet_analyze_id': 'test_003',
          'food_name': '蒸蛋羹',
          'quantity': 100,
          'calories': 120,
          'protein': 8,
          'carbs': 2,
          'fat': 8,
        },
        {
          'diet_analyze_id': 'test_003',
          'food_name': '小米粥',
          'quantity': 200,
          'calories': 100,
          'protein': 3,
          'carbs': 22,
          'fat': 1,
        },
        {
          'diet_analyze_id': 'test_003',
          'food_name': '凉拌黄瓜',
          'quantity': 80,
          'calories': 25,
          'protein': 1,
          'carbs': 5,
          'fat': 0,
        },
      ];

      for (final foodData in testFoodData) {
        await _dietAnalysisDao.addFoodRecord(foodData['diet_analyze_id'] as String, foodData);
      }

      logger.i('测试食物记录添加成功，共添加 ${testFoodData.length} 条食物记录');
    } catch (e) {
      logger.e('添加测试食物记录失败: $e');
    }
  }
}

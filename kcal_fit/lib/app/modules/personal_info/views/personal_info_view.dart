import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../shared/widgets/common_app_bar.dart';
import '../controllers/personal_info_controller.dart';
import '../../../../core/widgets/user_data_input_widgets.dart';
import '../../../../core/utils/logger_util.dart';

class PersonalInfoView extends GetView<PersonalInfoController> {
  const PersonalInfoView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: SmartAppBar(
        title: '个人信息',
        actions: [
          Obx(
            () => TextButton(
              onPressed: controller.isSaving.value ? null : controller.savePersonalInfo,
              child:
                  controller.isSaving.value
                      ? const SizedBox(
                        width: 18,
                        height: 18,
                        child: CircularProgressIndicator(
                          strokeWidth: 2.0,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                      : const Text(
                        '保存',
                        style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.w500),
                      ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // 加载状态指示器
          Obx(() => controller.isLoading.value ? const LinearProgressIndicator() : const SizedBox.shrink()),
          // 主要内容
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }

              return SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(children: [_buildBasicInfoSection(), const SizedBox(height: 20)]),
              );
            }),
          ),
        ],
      ),
    );
  }

  /// 构建基本信息区域
  Widget _buildBasicInfoSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.person, color: Colors.blue.shade600, size: 24),
              const SizedBox(width: 8),
              const Text('基本信息', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
            ],
          ),
          const SizedBox(height: 16),
          _buildNicknameField(),
          const SizedBox(height: 16),
          _buildGenderField(),
          const SizedBox(height: 16),
          _buildDateField(),
        ],
      ),
    );
  }

  /// 构建昵称字段
  Widget _buildNicknameField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('昵称', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
        const SizedBox(height: 12),
        Obx(
          () => TextFormField(
            controller: controller.nicknameController,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            decoration: InputDecoration(
              hintText: '请输入您的昵称',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.blue.shade600),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.red.shade400),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.red.shade400),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              errorText: controller.nicknameError.value.isNotEmpty ? controller.nicknameError.value : null,
            ),
            onChanged: controller.updateNickname,
          ),
        ),
      ],
    );
  }

  /// 构建性别字段
  Widget _buildGenderField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        UserDataInputWidgets.buildGenderSelection(controller.gender),
        Obx(
          () =>
              controller.genderError.value.isNotEmpty
                  ? Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Text(
                      controller.genderError.value,
                      style: TextStyle(fontSize: 14, color: Colors.red.shade600, fontWeight: FontWeight.w500),
                    ),
                  )
                  : const SizedBox.shrink(),
        ),
      ],
    );
  }

  /// 构建日期字段
  Widget _buildDateField() {
    LoggerUtil.d('🔄 _buildDateField 被调用');
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 日期选择组件内部已经使用Obx，无需外层包装
        UserDataInputWidgets.buildAgeSelection(controller.birthDate),
        // 错误信息显示
        Obx(
          () =>
              controller.birthDateError.value.isNotEmpty
                  ? Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Text(
                      controller.birthDateError.value,
                      style: TextStyle(fontSize: 14, color: Colors.red.shade600, fontWeight: FontWeight.w500),
                    ),
                  )
                  : const SizedBox.shrink(),
        ),
      ],
    );
  }
}

import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../shared/widgets/calendarCard/calendar_card.dart';
import '../controllers/plan_controller.dart';

// 在类顶部添加样式常量
const _cardPadding = EdgeInsets.all(16);
final _cardDecoration = BoxDecoration(
  color: Colors.white,
  borderRadius: BorderRadius.circular(12),
  boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 8, offset: Offset(0, 2))],
);

class PlanView extends GetView<PlanController> {
  const PlanView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50], // 添加背景色
      body: SafeArea(
        child: Column(
          children: [
            // 主要内容区域
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    // 计划页头部
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('饮食计划', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
                          TextButton.icon(
                            icon: const Icon(Icons.add, size: 16),
                            label: const Text('新建计划'),
                            style: TextButton.styleFrom(foregroundColor: Colors.blue[600]),
                            onPressed: () {},
                          ),
                        ],
                      ),
                    ),

                    // 日历视图
                    CalendarCard(controller: controller.calendarController),

                    // 当天计划
                    // 在当天计划部分的营养目标卡片下方添加膳食计划数据
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Container(
                        decoration: _cardDecoration,
                        padding: _cardPadding,
                        child: Column(
                          children: [
                            // 日期头部
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  '4月20日 星期四',
                                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.grey),
                                ),
                                IconButton(icon: const Icon(Icons.add), color: Colors.blue[600], onPressed: () {}),
                              ],
                            ),

                            // 替换原有的营养目标横向滚动部分
                            // 营养目标网格布局
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 16),
                              child: GridView.count(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                crossAxisCount: 2,
                                childAspectRatio: 3,
                                crossAxisSpacing: 8,
                                mainAxisSpacing: 8,
                                children: [
                                  _buildNutritionItem('蛋白质', '65/100g', 0.65, Colors.blue),
                                  _buildNutritionItem('碳水', '135/300g', 0.45, Colors.orange),
                                  _buildNutritionItem('脂肪', '30/100g', 0.3, Colors.red),
                                  _buildNutritionItem('维生素', '75%', 0.75, Colors.green),
                                  _buildNutritionItem('钠', '1.2/2g', 0.6, Colors.purple),
                                  _buildNutritionItem('膳食纤维', '25/50g', 0.5, Colors.teal),
                                ],
                              ),
                            ),

                            // 添加膳食计划部分
                            Column(
                              children: [
                                _buildMealSection(Icons.breakfast_dining, '早餐', '08:00', '450 kcal', [
                                  ['全麦面包', '2片'],
                                  ['鸡蛋', '1个'],
                                  ['牛奶', '250ml'],
                                ]),
                                _buildMealSection(Icons.lunch_dining, '午餐', '12:30', '650 kcal', [
                                  ['糙米饭', '150g'],
                                  ['鸡胸肉', '120g'],
                                  ['西兰花', '200g'],
                                ]),
                                _buildMealSection(Icons.dinner_dining, '晚餐', '18:30', '500 kcal', [
                                  ['三文鱼', '100g'],
                                  ['藜麦', '80g'],
                                  ['蔬菜沙拉', '150g'],
                                ]),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 修改膳食计划卡片组件
  Widget _buildMealSection(IconData icon, String title, String time, String calories, List<List<String>> items) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
        boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.05), blurRadius: 4, offset: const Offset(0, 2))],
      ),
      child: Column(
        children: [
          // 餐食头部
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(topLeft: Radius.circular(12), topRight: Radius.circular(12)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(color: Colors.white, shape: BoxShape.circle),
                      child: Icon(icon, size: 18, color: Colors.blue[600]),
                    ),
                    const SizedBox(width: 12),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(title, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                        Text(time, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
                      ],
                    ),
                  ],
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(color: Colors.blue[50], borderRadius: BorderRadius.circular(8)),
                  child: Text(
                    calories,
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: Colors.blue[800]),
                  ),
                ),
              ],
            ),
          ),

          // 餐食项目
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Column(
              children:
                  items
                      .map(
                        (item) => Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(item[0], style: const TextStyle(fontSize: 14)),
                              Text(item[1], style: TextStyle(fontSize: 14, color: Colors.grey[600])),
                            ],
                          ),
                        ),
                      )
                      .toList(),
            ),
          ),
        ],
      ),
    );
  }
}

// 新增营养项组件方法
Widget _buildNutritionItem(String label, String value, double progress, Color color) {
  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
    decoration: BoxDecoration(color: Colors.grey[50], borderRadius: BorderRadius.circular(8)),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            // 添加图标
            _getNutritionIcon(label, color),
            const SizedBox(width: 4),
            Text(label, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
            const Spacer(),
            Text(value, style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600, color: Colors.grey[800])),
          ],
        ),
        const SizedBox(height: 4),
        TweenAnimationBuilder<double>(
          tween: Tween<double>(begin: 0, end: progress),
          duration: const Duration(milliseconds: 800),
          curve: Curves.easeOut,
          builder: (context, value, _) {
            return LinearProgressIndicator(
              value: value,
              backgroundColor: Colors.grey[200],
              valueColor: AlwaysStoppedAnimation<Color>(color),
              minHeight: 4,
              borderRadius: BorderRadius.circular(2),
            );
          },
        ),
      ],
    ),
  );
}

// 新增：获取营养指标对应图标
Widget _getNutritionIcon(String label, Color color) {
  switch (label) {
    case '蛋白质':
      return Icon(Icons.fitness_center, size: 16, color: color);
    case '碳水':
      return Icon(Icons.breakfast_dining, size: 16, color: color);
    case '脂肪':
      return Icon(Icons.water_drop, size: 16, color: color);
    case '维生素':
      return Icon(Icons.medical_services, size: 16, color: color);
    case '钠':
      return Icon(Icons.science, size: 16, color: color);
    case '膳食纤维':
      return Icon(Icons.grass, size: 16, color: color);
    default:
      return Icon(Icons.restaurant, size: 16, color: color);
  }
}

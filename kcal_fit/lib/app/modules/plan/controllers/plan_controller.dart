import 'package:get/get.dart';
import 'package:logger/logger.dart';

import '../../../shared/widgets/calendarCard/calendar_card.dart';

var logger = Logger();

class PlanController extends GetxController {
  final calendarController = CalendarController();
  final selectedDate = DateTime.now().obs;

  @override
  void onInit() {
    super.onInit();
    // 监听日历控制器的日期变化
    ever(calendarController.selectedDates, (dates) {
      if (dates.isNotEmpty) {
        selectedDate.value = dates.last;
        // 这里可以添加处理选中日期的逻辑
        logger.i('选中日期: ${selectedDate.value}');
      }
    });
  }
}

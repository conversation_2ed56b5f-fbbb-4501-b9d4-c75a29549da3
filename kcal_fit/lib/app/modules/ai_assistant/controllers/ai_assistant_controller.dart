import 'dart:math';
import 'package:flutter_chat_core/flutter_chat_core.dart';
import 'package:get/get.dart';

import '../../../../core/utils/logger_util.dart';
import '../../../../core/services/stt/speech_to_text.dart';
import '../../../shared/controllers/base_controller.dart';

/// 智能助手控制器
/// 负责管理聊天消息和AI交互
class AiAssistantController extends BaseController {
  /// 聊天控制器
  late final InMemoryChatController chatController;

  /// 消息列表（可观察）
  final RxList<Message> messages = <Message>[].obs;

  /// 语音识别工具
  SpeechToTextUtil? _speechToTextUtil;

  /// 语音输入状态
  final RxBool isListening = false.obs;

  /// AI正在输入状态
  final RxBool isAiTyping = false.obs;

  /// 当前用户ID
  static const String currentUserId = 'user';

  /// AI助手用户ID
  static const String aiUserId = 'ai_assistant';

  @override
  void onInit() {
    super.onInit();
    LoggerUtil.d('智能助手控制器初始化');

    // 初始化聊天控制器
    chatController = InMemoryChatController();

    // 初始化语音识别（延迟初始化，避免启动时错误）
    try {
      _speechToTextUtil = Get.find<SpeechToTextUtil>();
    } catch (e) {
      LoggerUtil.w('语音识别服务初始化失败: $e');
    }

    // 添加欢迎消息
    _addWelcomeMessage();
  }

  @override
  void onClose() {
    chatController.dispose();
    super.onClose();
  }

  /// 添加欢迎消息
  void _addWelcomeMessage() {
    final welcomeMessage = TextMessage(
      id: 'welcome_${Random().nextInt(10000)}',
      authorId: aiUserId,
      createdAt: DateTime.now().toUtc(),
      text: '你好！我是你的智能健康助手 🤖\n\n我可以帮助你：\n• 解答营养健康问题\n• 提供饮食建议\n• 分析食物营养成分\n• 制定健康计划\n\n有什么问题尽管问我吧！',
    );

    chatController.insertMessage(welcomeMessage);
    messages.insert(0, welcomeMessage);
  }

  /// 发送消息
  void onMessageSend(String text) {
    LoggerUtil.d('用户发送消息: $text');

    // 添加用户消息
    final userMessage = TextMessage(
      id: 'user_${Random().nextInt(10000)}_${DateTime.now().millisecondsSinceEpoch}',
      authorId: currentUserId,
      createdAt: DateTime.now().toUtc(),
      text: text,
    );

    chatController.insertMessage(userMessage);
    messages.insert(0, userMessage);

    // 模拟AI回复（延迟1-2秒）
    _simulateAiResponse(text);
  }

  /// 模拟AI回复
  void _simulateAiResponse(String userMessage) {
    // 显示AI正在输入状态
    isAiTyping.value = true;

    final delay = Duration(milliseconds: 1000 + Random().nextInt(1000));

    Future.delayed(delay, () {
      final aiResponse = _generateAiResponse(userMessage);
      final cardData = _generateCardData(userMessage);

      // 创建自定义卡片消息
      final aiMessage = CustomMessage(
        id: 'ai_${Random().nextInt(10000)}_${DateTime.now().millisecondsSinceEpoch}',
        authorId: aiUserId,
        createdAt: DateTime.now().toUtc(),
        metadata: {
          'type': 'ai_card',
          'title': cardData['title'],
          'content': aiResponse,
          'icon': cardData['icon'],
          'color': cardData['color'],
          'suggestions': cardData['suggestions'],
          'useTypewriter': true, // 启用打字机效果
        },
      );

      // 隐藏AI正在输入状态
      isAiTyping.value = false;

      chatController.insertMessage(aiMessage);
      messages.insert(0, aiMessage);
      LoggerUtil.d('AI回复卡片消息: $aiResponse');
    });
  }

  /// 生成AI回复内容
  String _generateAiResponse(String userMessage) {
    final message = userMessage.toLowerCase();

    // 简单的关键词匹配回复
    if (message.contains('减肥') || message.contains('瘦身')) {
      return '关于减肥，我建议：\n\n1. 控制热量摄入，创造热量缺口\n2. 增加蛋白质摄入，保持肌肉量\n3. 多吃蔬菜水果，增加饱腹感\n4. 规律运动，结合有氧和力量训练\n5. 保证充足睡眠，避免熬夜\n\n记住，健康减肥需要循序渐进，建议每周减重0.5-1公斤。';
    }

    if (message.contains('营养') || message.contains('维生素')) {
      return '营养均衡很重要！建议每天摄入：\n\n🥬 蔬菜：300-500克\n🍎 水果：200-350克\n🍖 蛋白质：1.2-1.6g/kg体重\n🍚 碳水化合物：占总热量45-65%\n🥑 脂肪：占总热量20-35%\n\n多样化饮食，确保各种营养素的摄入。';
    }

    if (message.contains('运动') || message.contains('锻炼')) {
      return '运动建议：\n\n💪 力量训练：每周2-3次\n🏃 有氧运动：每周150分钟中等强度\n🧘 柔韧性训练：每周2-3次\n\n运动前后记得热身和拉伸，循序渐进，避免运动伤害。配合合理饮食效果更佳！';
    }

    if (message.contains('水') || message.contains('喝水')) {
      return '水分摄入很重要！💧\n\n建议每天饮水：\n• 成年男性：2.5-3.7升\n• 成年女性：2.0-2.7升\n\n运动时需要额外补充水分。可以通过尿液颜色判断水分状态：淡黄色表示水分充足。';
    }

    if (message.contains('睡眠') || message.contains('失眠')) {
      return '良好的睡眠对健康很重要！😴\n\n建议：\n• 每晚7-9小时睡眠\n• 规律作息时间\n• 睡前避免电子设备\n• 保持卧室安静、黑暗、凉爽\n• 避免睡前大量进食\n\n充足睡眠有助于新陈代谢和体重管理。';
    }

    // 默认回复
    return '感谢你的问题！作为你的健康助手，我会尽力帮助你。\n\n你可以问我关于：\n• 营养搭配\n• 减肥建议\n• 运动指导\n• 健康生活方式\n\n如果你有具体的健康问题，建议咨询专业医生哦！';
  }

  /// 生成卡片数据
  Map<String, dynamic> _generateCardData(String userMessage) {
    final message = userMessage.toLowerCase();

    if (message.contains('减肥') || message.contains('瘦身')) {
      return {
        'title': '减肥指导',
        'icon': '🏃‍♀️',
        'color': 0xFF4CAF50,
        'suggestions': ['制定减肥计划', '查看运动建议', '营养搭配指导'],
      };
    }

    if (message.contains('营养') || message.contains('维生素')) {
      return {
        'title': '营养建议',
        'icon': '🥗',
        'color': 0xFF2196F3,
        'suggestions': ['查看营养成分', '饮食搭配建议', '维生素补充'],
      };
    }

    if (message.contains('运动') || message.contains('锻炼')) {
      return {
        'title': '运动指导',
        'icon': '💪',
        'color': 0xFFFF9800,
        'suggestions': ['制定运动计划', '运动技巧', '运动安全'],
      };
    }

    if (message.contains('水') || message.contains('喝水')) {
      return {
        'title': '水分补充',
        'icon': '💧',
        'color': 0xFF00BCD4,
        'suggestions': ['喝水提醒', '水分计算', '健康饮水'],
      };
    }

    if (message.contains('睡眠') || message.contains('失眠')) {
      return {
        'title': '睡眠健康',
        'icon': '😴',
        'color': 0xFF9C27B0,
        'suggestions': ['睡眠建议', '作息调整', '睡眠质量'],
      };
    }

    // 默认卡片
    return {
      'title': '健康助手',
      'icon': '🤖',
      'color': 0xFF607D8B,
      'suggestions': ['营养咨询', '运动指导', '健康建议'],
    };
  }

  /// 开始语音输入
  Future<void> startVoiceInput() async {
    if (_speechToTextUtil == null) {
      Get.snackbar('提示', '语音识别服务不可用');
      return;
    }

    try {
      LoggerUtil.d('开始语音输入');
      isListening.value = true;

      // 监听语音识别结果
      ever(_speechToTextUtil!.finalRecognizedWords, (String result) {
        if (result.isNotEmpty) {
          LoggerUtil.d('语音识别结果: $result');
          onMessageSend(result);
          // 清空识别结果
          _speechToTextUtil!.finalRecognizedWords.value = '';
          isListening.value = false;
        }
      });

      // 开始语音识别
      await _speechToTextUtil!.handleVoiceInput();
    } catch (e) {
      LoggerUtil.e('语音输入失败: $e');
      Get.snackbar('错误', '语音输入失败: $e');
      isListening.value = false;
    }
  }

  /// 解析用户信息
  Future<User> resolveUser(UserID id) async {
    if (id == currentUserId) {
      return User(id: id, name: '我');
    } else if (id == aiUserId) {
      return User(id: id, name: 'AI助手');
    }

    // 默认用户
    return User(id: id, name: '用户');
  }
}

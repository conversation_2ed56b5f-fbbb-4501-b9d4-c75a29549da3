import 'package:flutter/material.dart';
import 'package:flutter_chat_core/flutter_chat_core.dart';
import 'package:get/get.dart';

import '../../../shared/widgets/common_app_bar.dart';
import '../../../shared/widgets/typewriter_text.dart';
import '../controllers/ai_assistant_controller.dart';

/// 智能助手视图
/// 提供聊天界面与AI助手交互
class AiAssistantView extends GetView<AiAssistantController> {
  const AiAssistantView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(appBar: _buildAppBar(), body: _buildChatInterface());
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar() {
    return SmartAppBar(
      title: '智能助手',
      actions: [IconButton(icon: const Icon(Icons.info_outline), onPressed: _showHelpDialog, tooltip: '帮助')],
    );
  }

  /// 构建聊天界面
  Widget _buildChatInterface() {
    return Column(
      children: [
        // 聊天消息区域
        Expanded(
          child: Container(
            color: const Color(0xFFF5F5F5),
            child: Obx(
              () => ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: controller.messages.length + (controller.isAiTyping.value ? 1 : 0),
                reverse: true,
                itemBuilder: (context, index) {
                  // 如果AI正在输入，在第一个位置显示输入指示器
                  if (index == 0 && controller.isAiTyping.value) {
                    return _buildTypingIndicator();
                  }

                  // 调整消息索引
                  final messageIndex = controller.isAiTyping.value ? index - 1 : index;
                  final message = controller.messages[messageIndex];
                  return _buildMessageItem(message);
                },
              ),
            ),
          ),
        ),
        // 自定义输入区域
        _buildCustomInput(onSendPressed: controller.onMessageSend, onAttachmentPressed: () {}),
      ],
    );
  }

  /// 构建消息项
  Widget _buildMessageItem(Message message) {
    final isUser = message.authorId == AiAssistantController.currentUserId;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) ...[
            // AI头像
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(color: Colors.blue.withValues(alpha: 0.1), shape: BoxShape.circle),
              child: const Icon(Icons.smart_toy, size: 20, color: Colors.blue),
            ),
            const SizedBox(width: 8),
          ],
          // 消息内容
          Flexible(
            child:
                message is CustomMessage
                    ? _buildAiCardMessage(message.metadata ?? {})
                    : _buildTextMessage(message as TextMessage, isUser),
          ),
          if (isUser) ...[
            const SizedBox(width: 8),
            // 用户头像
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(color: Colors.green.withValues(alpha: 0.1), shape: BoxShape.circle),
              child: const Icon(Icons.person, size: 20, color: Colors.green),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建文本消息
  Widget _buildTextMessage(TextMessage message, bool isUser) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isUser ? Colors.blue : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.black.withValues(alpha: 0.1), blurRadius: 4, offset: const Offset(0, 2))],
      ),
      child: Text(
        message.text,
        style: TextStyle(color: isUser ? Colors.white : Colors.black87, fontSize: 14, height: 1.4),
      ),
    );
  }

  /// 构建AI卡片消息
  Widget _buildAiCardMessage(Map<String, dynamic> metadata) {
    final title = metadata['title'] as String? ?? '智能助手';
    final content = metadata['content'] as String? ?? '';
    final icon = metadata['icon'] as String? ?? '🤖';
    final color = Color(metadata['color'] as int? ?? 0xFF607D8B);
    final suggestions = metadata['suggestions'] as List<dynamic>? ?? [];
    final useTypewriter = metadata['useTypewriter'] as bool? ?? false;

    return Container(
      constraints: const BoxConstraints(maxWidth: 280),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.black.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 卡片头部
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(topLeft: Radius.circular(16), topRight: Radius.circular(16)),
            ),
            child: Row(
              children: [
                Text(icon, style: const TextStyle(fontSize: 24)),
                const SizedBox(width: 12),
                Expanded(child: Text(title, style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color))),
              ],
            ),
          ),
          // 卡片内容
          Padding(
            padding: const EdgeInsets.all(16),
            child:
                useTypewriter
                    ? TypewriterText(
                      text: content,
                      speed: 30, // 较快的打字速度
                      style: const TextStyle(fontSize: 14, height: 1.5, color: Colors.black87),
                      showCursor: true,
                      cursorColor: color,
                    )
                    : Text(content, style: const TextStyle(fontSize: 14, height: 1.5, color: Colors.black87)),
          ),
          // 建议按钮
          if (suggestions.isNotEmpty)
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Wrap(
                spacing: 8,
                runSpacing: 8,
                children:
                    suggestions.map((suggestion) {
                      return _buildSuggestionButton(suggestion.toString(), color);
                    }).toList(),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建建议按钮
  Widget _buildSuggestionButton(String text, Color color) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => controller.onMessageSend(text),
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: color.withValues(alpha: 0.3)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.touch_app, size: 14, color: color),
              const SizedBox(width: 4),
              Text(text, style: TextStyle(fontSize: 12, color: color, fontWeight: FontWeight.w500)),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建自定义输入框
  Widget _buildCustomInput({
    required void Function(String) onSendPressed,
    required void Function() onAttachmentPressed,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Color(0xFFE0E0E0), width: 1)),
      ),
      child: Row(
        children: [
          // 语音输入按钮
          Obx(
            () => GestureDetector(
              onTap: controller.startVoiceInput,
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color:
                      controller.isListening.value
                          ? Colors.red.withValues(alpha: 0.1)
                          : Colors.blue.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  controller.isListening.value ? Icons.mic : Icons.mic_none,
                  color: controller.isListening.value ? Colors.red : Colors.blue,
                  size: 20,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          // 文本输入框
          Expanded(
            child: TextField(
              decoration: InputDecoration(
                hintText: '输入消息或点击语音按钮...',
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(20), borderSide: BorderSide.none),
                filled: true,
                fillColor: const Color(0xFFF5F5F5),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              onSubmitted: (text) {
                if (text.trim().isNotEmpty) {
                  onSendPressed(text.trim());
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建AI正在输入指示器
  Widget _buildTypingIndicator() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // AI头像
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(color: Colors.blue.withValues(alpha: 0.1), shape: BoxShape.circle),
            child: const Icon(Icons.smart_toy, size: 20, color: Colors.blue),
          ),
          const SizedBox(width: 8),
          // 输入指示器
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(color: Colors.black.withValues(alpha: 0.1), blurRadius: 4, offset: const Offset(0, 2)),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('AI正在思考', style: TextStyle(color: Colors.black54, fontSize: 14)),
                  const SizedBox(width: 8),
                  _buildTypingDots(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建打字动画点
  Widget _buildTypingDots() {
    return SizedBox(
      width: 24,
      height: 16,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: List.generate(3, (index) {
          return _TypingDot(delay: index * 200);
        }),
      ),
    );
  }

  /// 显示帮助对话框
  void _showHelpDialog() {
    Get.dialog(
      AlertDialog(
        title: const Row(children: [Icon(Icons.help_outline, color: Colors.blue), SizedBox(width: 8), Text('使用帮助')]),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('智能助手可以帮助你：', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 12),
              _HelpItem(icon: Icons.restaurant, title: '营养咨询', description: '询问食物营养成分、饮食搭配建议'),
              _HelpItem(icon: Icons.fitness_center, title: '运动指导', description: '获取运动建议和健身计划'),
              _HelpItem(icon: Icons.monitor_weight, title: '减肥建议', description: '制定健康的减肥计划和方法'),
              _HelpItem(icon: Icons.bedtime, title: '生活习惯', description: '改善睡眠、作息等生活方式'),
              SizedBox(height: 12),
              Text('💡 提示：描述越详细，建议越精准！', style: TextStyle(color: Colors.orange, fontStyle: FontStyle.italic)),
            ],
          ),
        ),
        actions: [TextButton(onPressed: () => Get.back(), child: const Text('知道了'))],
      ),
    );
  }
}

/// 帮助项组件
class _HelpItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final String description;

  const _HelpItem({required this.icon, required this.title, required this.description});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: Colors.blue[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 14)),
                const SizedBox(height: 2),
                Text(description, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 打字动画点组件
class _TypingDot extends StatefulWidget {
  final int delay;

  const _TypingDot({required this.delay});

  @override
  State<_TypingDot> createState() => _TypingDotState();
}

class _TypingDotState extends State<_TypingDot> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(duration: const Duration(milliseconds: 1200), vsync: this);

    _animation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));

    // 延迟启动动画
    Future.delayed(Duration(milliseconds: widget.delay), () {
      if (mounted) {
        _controller.repeat(reverse: true);
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Opacity(
          opacity: _animation.value,
          child: Container(
            width: 4,
            height: 4,
            decoration: const BoxDecoration(color: Colors.blue, shape: BoxShape.circle),
          ),
        );
      },
    );
  }
}

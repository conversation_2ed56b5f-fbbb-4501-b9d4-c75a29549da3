import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_ruler_picker/flutter_ruler_picker.dart';
import '../../../../core/services/user_guide_service.dart';
import '../../../data/models/body_data.dart';
import '../../../data/services/sqflite/dao/impl/body_data_dao.dart';
import '../../my/controllers/my_controller.dart';

/// 身体数据表单控制器
/// 负责管理身体数据表单的状态和业务逻辑
class BodyDataFormController extends GetxController {
  // 表单相关
  final formKey = GlobalKey<FormState>();
  final heightController = TextEditingController();
  final weightController = TextEditingController();
  final bodyFatController = TextEditingController();

  // 验证错误信息
  final RxString heightError = ''.obs;
  final RxString weightError = ''.obs;
  final RxString bodyFatError = ''.obs;

  // 加载状态
  final RxBool isLoading = false.obs;
  final RxBool isSaving = false.obs;

  // 表单字段
  final height = ''.obs;
  final weight = ''.obs;
  final bodyFat = ''.obs;

  // 新增：用于公共组件的响应式变量
  final selectedHeight = 170.obs; // 默认身高170cm
  final selectedWeight = 60.0.obs; // 默认体重60kg

  // 新增：尺子控制器
  late RulerPickerController heightRulerController;
  late RulerPickerController weightRulerController;

  // 表单验证状态
  final isFormValid = false.obs;

  // DAO实例
  final BodyDataDao _bodyDataDao = BodyDataDao();

  @override
  void onInit() {
    super.onInit();
    // 初始化尺子控制器
    heightRulerController = RulerPickerController(value: selectedHeight.value);
    weightRulerController = RulerPickerController(value: selectedWeight.value);
    _setupValidation();
    _initializeData();
  }

  @override
  void onClose() {
    heightController.dispose();
    weightController.dispose();
    bodyFatController.dispose();
    super.onClose();
  }

  /// 初始化数据
  /// 从数据库加载最新的身体数据
  Future<void> _initializeData() async {
    try {
      isLoading.value = true;

      final latestData = await _bodyDataDao.getLatestBodyData();
      if (latestData != null) {
        // 使用数据库中的最新数据
        height.value = latestData.height?.toString() ?? '';
        weight.value = latestData.weight?.toString() ?? '';
        bodyFat.value = latestData.bodyFatPercentage?.toString() ?? '';

        heightController.text = height.value;
        weightController.text = weight.value;
        bodyFatController.text = bodyFat.value;

        // 同时更新新的响应式变量
        if (latestData.height != null) {
          selectedHeight.value = latestData.height!;
          heightRulerController.value = latestData.height!.toDouble();
        }
        if (latestData.weight != null) {
          selectedWeight.value = latestData.weight!;
          weightRulerController.value = latestData.weight!;
        }
      }
    } catch (e) {
      _showErrorMessage('加载身体数据失败: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// 设置表单验证监听
  void _setupValidation() {
    ever(height, (_) => _validateForm());
    ever(weight, (_) => _validateForm());
    ever(bodyFat, (_) => _validateForm());

    // 监听新的响应式变量，同步更新旧的字符串变量
    ever(selectedHeight, (value) {
      height.value = value.toString();
      heightController.text = value.toString();
      // 清除身高错误信息
      heightError.value = '';
      // 重新验证表单
      _validateForm();
    });

    ever(selectedWeight, (value) {
      weight.value = value.toStringAsFixed(1);
      weightController.text = value.toStringAsFixed(1);
      // 清除体重错误信息
      weightError.value = '';
      // 重新验证表单
      _validateForm();
    });
  }

  /// 验证身高
  bool _validateHeight() {
    final heightValue = height.value.trim();
    if (heightValue.isEmpty) {
      heightError.value = '请输入身高';
      return false;
    }

    final heightNum = double.tryParse(heightValue);
    if (heightNum == null) {
      heightError.value = '请输入有效的身高数值';
      return false;
    }

    if (heightNum <= 0) {
      heightError.value = '身高必须大于0';
      return false;
    }

    if (heightNum < 140 || heightNum > 220) {
      heightError.value = '身高范围应在140-220cm之间';
      return false;
    }

    heightError.value = '';
    return true;
  }

  /// 验证体重
  bool _validateWeight() {
    final weightValue = weight.value.trim();
    if (weightValue.isEmpty) {
      weightError.value = '请输入体重';
      return false;
    }

    final weightNum = double.tryParse(weightValue);
    if (weightNum == null) {
      weightError.value = '请输入有效的体重数值';
      return false;
    }

    if (weightNum <= 0) {
      weightError.value = '体重必须大于0';
      return false;
    }

    if (weightNum < 30 || weightNum > 150) {
      weightError.value = '体重范围应在30-150kg之间';
      return false;
    }

    weightError.value = '';
    return true;
  }

  /// 验证体脂率（非必填）
  bool _validateBodyFat() {
    final bodyFatValue = bodyFat.value.trim();

    // 体脂率为空时，直接返回true（非必填）
    if (bodyFatValue.isEmpty) {
      bodyFatError.value = '';
      return true;
    }

    final bodyFatNum = double.tryParse(bodyFatValue);
    if (bodyFatNum == null) {
      bodyFatError.value = '请输入有效的体脂率数值';
      return false;
    }

    if (bodyFatNum < 0 || bodyFatNum > 100) {
      bodyFatError.value = '体脂率必须在0-100之间';
      return false;
    }

    bodyFatError.value = '';
    return true;
  }

  /// 验证所有表单字段
  bool _validateFormFields() {
    bool isValid = true;

    // 必填字段验证
    if (!_validateHeight()) isValid = false;
    if (!_validateWeight()) isValid = false;

    // 可选字段验证（体脂率）
    // 即使体脂率验证失败，也不影响整体表单的有效性
    // 但仍需要验证以显示错误信息
    _validateBodyFat();

    return isValid;
  }

  /// 验证表单并更新状态
  void _validateForm() {
    isFormValid.value = _isFormDataValid();
  }

  /// 验证表单数据是否完整
  /// 只要求身高和体重必填，体脂率为可选
  bool _isFormDataValid() {
    return height.value.trim().isNotEmpty && weight.value.trim().isNotEmpty;
  }

  /// 更新身高
  void updateHeight(String value) {
    height.value = value.trim();
    _validateHeight();
  }

  /// 更新体重
  void updateWeight(String value) {
    weight.value = value.trim();
    _validateWeight();
  }

  /// 更新体脂率
  void updateBodyFat(String value) {
    bodyFat.value = value.trim();
    _validateBodyFat();
  }

  /// 提交身体数据
  Future<bool> submitData() async {
    try {
      // 验证表单
      if (!_validateFormFields()) {
        _showErrorMessage('请检查并完善所有必填信息');
        return false;
      }

      if (isSaving.value) return false; // 防止重复提交

      isSaving.value = true;

      final bodyDataValues = _parseFormData();

      // 查询最新的身体数据记录作为基础
      final latestBodyData = await _bodyDataDao.getLatestBodyData();

      // 基于最新记录创建新的身体数据
      final bodyData = _createBodyDataModel(bodyDataValues, baseData: latestBodyData);

      await _bodyDataDao.addBodyData(bodyData);

      _clearForm();

      // 标记身体数据引导已完成
      _markBodyDataGuideComplete();

      // 刷新我的页面的所有数据
      if (Get.isRegistered<MyController>()) {
        Get.find<MyController>().refreshData();
      }

      // 返回上一页
      Get.back(result: {'action': 'body_data_saved', 'needRefresh': true});
      _showSuccessMessage('身体数据已保存');

      return true;
    } catch (e) {
      _showErrorMessage('保存身体数据失败: $e');
      return false;
    } finally {
      isSaving.value = false;
    }
  }

  /// 解析表单数据
  Map<String, double?> _parseFormData() {
    try {
      return {
        'height': double.parse(height.value),
        'weight': double.parse(weight.value),
        'bodyFat': bodyFat.value.trim().isNotEmpty ? double.parse(bodyFat.value) : null,
      };
    } catch (e) {
      throw const FormatException('请输入有效的数值');
    }
  }

  /// 创建BodyData模型
  /// [values] 表单输入的数据值
  /// [baseData] 基础数据，用于保持其他字段不变
  BodyData _createBodyDataModel(Map<String, double?> values, {BodyData? baseData}) {
    final heightValue = values['height']!;
    final weightValue = values['weight']!;
    final bodyFatValue = values['bodyFat']; // 可能为null

    // 计算BMI (身高转换为米)
    final heightInMeters = heightValue / 100;
    final bmiValue = weightValue / (heightInMeters * heightInMeters);

    return BodyData(
      recordDate: DateTime.now(),
      // 保持基础数据中的个人信息字段
      birthDate: baseData?.birthDate,
      gender: baseData?.gender,
      // 更新身体指标
      height: heightValue.toInt(),
      weight: weightValue, // 保持double类型，支持小数
      bodyFatPercentage: bodyFatValue, // 可能为null
      bmi: double.parse(bmiValue.toStringAsFixed(2)), // 保留2位小数
      // 保持基础数据中的其他测量数据
      waistCircumference: baseData?.waistCircumference,
      hipCircumference: baseData?.hipCircumference,
      systolicBloodPressure: baseData?.systolicBloodPressure,
      diastolicBloodPressure: baseData?.diastolicBloodPressure,
      restingHeartRate: baseData?.restingHeartRate,
    );
  }

  /// 标记身体数据引导已完成
  void _markBodyDataGuideComplete() {
    try {
      if (Get.isRegistered<UserGuideService>()) {
        final userGuideService = Get.find<UserGuideService>();
        userGuideService.bodyDataGuideShown.value = true;
      }
    } catch (e) {
      // 忽略错误，不影响主要功能
    }
  }

  /// 清空表单
  void _clearForm() {
    height.value = '';
    weight.value = '';
    bodyFat.value = '';

    heightController.clear();
    weightController.clear();
    bodyFatController.clear();

    // 清除错误信息
    heightError.value = '';
    weightError.value = '';
    bodyFatError.value = '';
  }

  /// 显示成功消息
  void _showSuccessMessage(String message) {
    Get.snackbar(
      '成功',
      message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.green.shade100,
      colorText: Colors.green.shade800,
    );
  }

  /// 显示错误消息
  void _showErrorMessage(String message) {
    Get.snackbar(
      '错误',
      message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.red.shade100,
      colorText: Colors.red.shade800,
    );
  }

  /// 重置表单
  void resetForm() {
    _clearForm();
    isFormValid.value = false;
  }

  /// 获取当前BMI值（用于实时显示）
  double? get currentBMI {
    try {
      // 只需要身高和体重就能计算BMI
      if (height.value.trim().isEmpty || weight.value.trim().isEmpty) return null;

      final heightValue = double.parse(height.value);
      final weightValue = double.parse(weight.value);

      if (heightValue <= 0 || weightValue <= 0) return null;

      final heightInMeters = heightValue / 100;
      final bmi = weightValue / (heightInMeters * heightInMeters);

      return double.parse(bmi.toStringAsFixed(2));
    } catch (e) {
      return null;
    }
  }
}

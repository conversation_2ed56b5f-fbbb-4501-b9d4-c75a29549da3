import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../shared/widgets/common_app_bar.dart';
import '../controllers/body_data_form_controller.dart';
import '../../../../core/widgets/user_data_input_widgets.dart';

class BodyDataFormView extends GetView<BodyDataFormController> {
  const BodyDataFormView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: SmartAppBar(
        title: '身体数据',
        actions: [
          Obx(
            () => TextButton(
              onPressed: controller.isSaving.value ? null : controller.submitData,
              child:
                  controller.isSaving.value
                      ? const SizedBox(
                        width: 18,
                        height: 18,
                        child: CircularProgressIndicator(
                          strokeWidth: 2.0,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                      : const Text(
                        '保存',
                        style: TextStyle(color: Colors.white, fontWeight: FontWeight.w500, fontSize: 16),
                      ),
            ),
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: _buildBody(),
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }

      return SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [_buildDataInputSection(), const SizedBox(height: 20)],
        ),
      );
    });
  }

  /// 构建数据输入区域
  Widget _buildDataInputSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 使用公共组件：身高选择
          UserDataInputWidgets.buildHeightSelection(controller.selectedHeight, controller.heightRulerController),
          Obx(
            () =>
                controller.heightError.value.isNotEmpty
                    ? Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        controller.heightError.value,
                        style: TextStyle(fontSize: 14, color: Colors.red.shade600, fontWeight: FontWeight.w500),
                      ),
                    )
                    : const SizedBox.shrink(),
          ),

          const SizedBox(height: 16),
          // 使用公共组件：体重选择
          UserDataInputWidgets.buildWeightSelection(controller.selectedWeight, controller.weightRulerController),
          Obx(
            () =>
                controller.weightError.value.isNotEmpty
                    ? Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        controller.weightError.value,
                        style: TextStyle(fontSize: 14, color: Colors.red.shade600, fontWeight: FontWeight.w500),
                      ),
                    )
                    : const SizedBox.shrink(),
          ),

          const SizedBox(height: 16),
          // 保留原有的体脂率输入
          _buildInputField(
            icon: Icons.pie_chart_outline,
            title: '体脂率（可选）',
            unit: '%',
            value: controller.bodyFat,
            error: controller.bodyFatError,
            onChanged: controller.updateBodyFat,
          ),

          Obx(() {
            final height = double.tryParse(controller.height.value) ?? 0;
            final weight = double.tryParse(controller.weight.value) ?? 0;

            if (height <= 0 || weight <= 0) {
              return const SizedBox.shrink();
            }

            final bmi = weight / ((height / 100) * (height / 100));
            String bmiCategory = '';
            Color bmiColor = Colors.grey;

            if (bmi < 18.5) {
              bmiCategory = '偏瘦';
              bmiColor = Colors.blue;
            } else if (bmi < 24) {
              bmiCategory = '正常';
              bmiColor = Colors.green;
            } else if (bmi < 28) {
              bmiCategory = '超重';
              bmiColor = Colors.orange;
            } else {
              bmiCategory = '肥胖';
              bmiColor = Colors.red;
            }

            return Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                // color: Colors.white,
                // borderRadius: BorderRadius.circular(16),
                // boxShadow: [
                //   BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2)),
                // ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.favorite, color: bmiColor, size: 24),
                      const SizedBox(width: 8),
                      const Text('BMI 指数', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              bmi.toStringAsFixed(1),
                              style: TextStyle(fontSize: 32, fontWeight: FontWeight.bold, color: bmiColor),
                            ),
                            const SizedBox(height: 4),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                              decoration: BoxDecoration(
                                color: bmiColor.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(bmiCategory, style: TextStyle(color: bmiColor, fontWeight: FontWeight.w500)),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(color: Colors.grey[50], borderRadius: BorderRadius.circular(12)),
                        child: Column(
                          children: [
                            Text('BMI 范围', style: TextStyle(fontSize: 14, color: Colors.grey[600])),
                            const SizedBox(height: 8),
                            const Text('18.5 - 23.9', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
                            const SizedBox(height: 4),
                            Text('正常范围', style: TextStyle(fontSize: 12, color: Colors.grey[600])),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  /// 构建输入字段
  Widget _buildInputField({
    required IconData icon,
    required String title,
    required String unit,
    required RxString value,
    required RxString error,
    required Function(String) onChanged,
  }) {
    // 判断是否为体脂率字段
    final isBodyFat = title.contains('体脂率');
    final hintText = isBodyFat ? '可选，如知道体脂率可填写' : '请输入$title';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(color: Colors.blue.shade50, borderRadius: BorderRadius.circular(8)),
              child: Icon(icon, size: 20, color: Colors.blue.shade600),
            ),
            const SizedBox(width: 12),
            Text(title, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
          ],
        ),
        const SizedBox(height: 12),
        Obx(
          () => TextFormField(
            initialValue: value.value,
            keyboardType: TextInputType.number,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            decoration: InputDecoration(
              hintText: hintText,
              suffixText: unit,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.blue.shade600),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.red.shade400),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.red.shade400),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              errorText: error.value.isNotEmpty ? error.value : null,
            ),
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }
}

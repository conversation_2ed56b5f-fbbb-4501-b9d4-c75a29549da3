import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../shared/widgets/common_app_bar.dart';
import '../controllers/goal_setting_controller.dart';

class GoalSettingView extends GetView<GoalSettingController> {
  const GoalSettingView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: SmartAppBar(
        title: '目标设定',
        actions: [
          Obx(
            () => TextButton(
              onPressed: controller.isSaving ? null : controller.saveGoalSettings,
              child:
                  controller.isSaving
                      ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                      : const Text('保存', style: TextStyle(color: Colors.white, fontSize: 14)),
            ),
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        return Form(
          key: controller.formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildCurrentStatusCard(),
                const SizedBox(height: 16),
                _buildGoalTypeSection(),
                const SizedBox(height: 16),
                _buildTargetWeightSection(),
                const SizedBox(height: 16),
                _buildActivityLevelSection(),
                const SizedBox(height: 16),
                _buildCaloriesSection(),
                const SizedBox(height: 16),
                _buildTimelineSection(),
                const SizedBox(height: 16),
                _buildGoalSummaryCard(),
                const SizedBox(height: 20),
              ],
            ),
          ),
        );
      }),
    );
  }

  /// 构建当前状态卡片
  Widget _buildCurrentStatusCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.person_outline, color: Colors.blue.shade600, size: 24),
              const SizedBox(width: 8),
              const Text('当前状态', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
            ],
          ),
          const SizedBox(height: 16),

          Obx(
            () => Row(
              children: [
                Expanded(
                  child: _buildStatusItem(
                    '当前体重',
                    '${controller.currentWeight.value.toStringAsFixed(1)}kg',
                    Icons.scale,
                  ),
                ),
                Expanded(
                  child: _buildStatusItem(
                    '身高',
                    '${controller.currentHeight.value.toStringAsFixed(0)}cm',
                    Icons.straighten,
                  ),
                ),
                Expanded(
                  child: _buildStatusItem('BMI', controller.currentBMI.value.toStringAsFixed(1), Icons.favorite),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建状态项目
  Widget _buildStatusItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.blue.shade400, size: 20),
        const SizedBox(height: 8),
        Text(value, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
        const SizedBox(height: 4),
        Text(label, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
      ],
    );
  }

  /// 构建目标类型选择
  Widget _buildGoalTypeSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('目标类型', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
          const SizedBox(height: 16),

          Obx(
            () => Wrap(
              spacing: 12,
              runSpacing: 12,
              children:
                  controller.goalTypes.map((type) {
                    final isSelected = controller.isGoalTypeSelected(type);
                    return GestureDetector(
                      onTap: () => controller.toggleGoalType(type),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                        decoration: BoxDecoration(
                          color: isSelected ? Colors.blue.shade600 : Colors.grey[100],
                          borderRadius: BorderRadius.circular(25),
                          border: Border.all(color: isSelected ? Colors.blue.shade600 : Colors.grey.shade300),
                        ),
                        child: Text(
                          type,
                          style: TextStyle(
                            color: isSelected ? Colors.white : Colors.grey[700],
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建目标体重设置
  Widget _buildTargetWeightSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('目标体重', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
          const SizedBox(height: 16),

          TextFormField(
            controller: controller.targetWeightController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              hintText: '请输入目标体重',
              suffixText: 'kg',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.blue.shade600),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            onChanged: controller.updateTargetWeight,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return '请输入目标体重';
              }
              final weight = double.tryParse(value);
              if (weight == null || weight <= 0) {
                return '请输入有效的体重';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  /// 构建活动水平选择
  Widget _buildActivityLevelSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('活动水平', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
          const SizedBox(height: 16),

          Obx(
            () => Column(
              children:
                  controller.activityLevels.map((activity) {
                    final isSelected = controller.activityLevel == activity['level'];
                    return Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      child: GestureDetector(
                        onTap: () => controller.updateActivityLevel(activity['level']!),
                        child: Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: isSelected ? Colors.blue.shade50 : Colors.grey[50],
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: isSelected ? Colors.blue.shade600 : Colors.grey.shade200,
                              width: isSelected ? 2 : 1,
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                                color: isSelected ? Colors.blue.shade600 : Colors.grey[400],
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      activity['level']!,
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                        color: isSelected ? Colors.blue.shade800 : Colors.grey[800],
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      activity['description']!,
                                      style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建卡路里目标设置
  Widget _buildCaloriesSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text('每日卡路里目标', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
              const Spacer(),
              TextButton(
                onPressed: controller.calculateRecommendedCalories,
                child: Text('智能推荐', style: TextStyle(color: Colors.blue.shade600, fontSize: 14)),
              ),
            ],
          ),
          const SizedBox(height: 16),

          TextFormField(
            controller: controller.dailyCaloriesController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              hintText: '请输入每日卡路里目标',
              suffixText: 'kcal',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.blue.shade600),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            onChanged: controller.updateDailyCalories,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return '请输入每日卡路里目标';
              }
              final calories = int.tryParse(value);
              if (calories == null || calories <= 0) {
                return '请输入有效的卡路里数值';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  /// 构建时间线设置
  Widget _buildTimelineSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('目标期限', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
          const SizedBox(height: 16),

          Obx(
            () => Wrap(
              spacing: 12,
              runSpacing: 12,
              children:
                  controller.targetDaysOptions.map((days) {
                    final isSelected = controller.targetDays == days;
                    return GestureDetector(
                      onTap: () => controller.updateTargetDays(days),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                        decoration: BoxDecoration(
                          color: isSelected ? Colors.blue.shade600 : Colors.grey[100],
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(color: isSelected ? Colors.blue.shade600 : Colors.grey.shade300),
                        ),
                        child: Text(
                          // ignore: unnecessary_brace_in_string_interps
                          '${days}天',
                          style: TextStyle(
                            color: isSelected ? Colors.white : Colors.grey[700],
                            fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建目标摘要卡片
  Widget _buildGoalSummaryCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade50, Colors.blue.shade100],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.flag, color: Colors.blue.shade600, size: 24),
              const SizedBox(width: 8),
              Text('目标摘要', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600, color: Colors.blue.shade800)),
            ],
          ),
          const SizedBox(height: 16),

          Obx(
            () => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  controller.getGoalDescription(),
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: Colors.blue.shade700),
                ),
                const SizedBox(height: 8),
                Text(
                  '建议变化速度: ${controller.getWeeklyWeightChange()}',
                  style: TextStyle(fontSize: 14, color: Colors.blue.shade600),
                ),
                const SizedBox(height: 8),
                Text(
                  '每日卡路里目标: ${controller.dailyCaloriesGoal} kcal',
                  style: TextStyle(fontSize: 14, color: Colors.blue.shade600),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

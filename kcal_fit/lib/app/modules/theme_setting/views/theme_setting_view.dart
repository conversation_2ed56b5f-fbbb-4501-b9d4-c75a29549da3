import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../shared/widgets/common_app_bar.dart';
import '../../../shared/widgets/common_card.dart';
import '../../../shared/widgets/common_button.dart';
import '../../../../core/theme/app_theme.dart';
import '../controllers/theme_setting_controller.dart';

/// 主题设置页面
class ThemeSettingView extends GetView<ThemeSettingController> {
  const ThemeSettingView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: SmartAppBar(
        title: '主题设置',
        actions: [
          Obx(
            () =>
                controller.hasUnsavedChanges
                    ? TextButton(
                      onPressed: controller.saveAndApply,
                      child:
                          controller.isSaving
                              ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                              : const Text('保存', style: TextStyle(color: Colors.white)),
                    )
                    : const SizedBox.shrink(),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildPreviewTip(),
            const SizedBox(height: 16),
            _buildThemeTypeSection(),
            const SizedBox(height: 16),
            _buildAppBarStyleSection(),
            const SizedBox(height: 24),
            _buildActionButtons(),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  /// 构建预览提示
  Widget _buildPreviewTip() {
    return Obx(() {
      if (!controller.showPreviewTip) return const SizedBox.shrink();

      return CommonCard(
        backgroundColor: Colors.blue.shade50,
        border: Border.all(color: Colors.blue.shade200),
        child: Row(
          children: [
            Icon(Icons.info_outline, color: Colors.blue.shade600, size: 20),
            const SizedBox(width: 12),
            const Expanded(
              child: Text('点击主题选项可以实时预览效果，记得保存设置哦！', style: TextStyle(fontSize: 14, color: Colors.black87)),
            ),
            IconButton(
              onPressed: controller.hidePreviewTip,
              icon: Icon(Icons.close, color: Colors.grey.shade600, size: 18),
              constraints: const BoxConstraints(),
              padding: EdgeInsets.zero,
            ),
          ],
        ),
      );
    });
  }

  /// 构建主题类型选择区域
  Widget _buildThemeTypeSection() {
    return CommonCard(
      title: '主题颜色',
      child: Column(
        children: [
          const Text('选择您喜欢的主题颜色', style: TextStyle(color: Colors.grey, fontSize: 14)),
          const SizedBox(height: 16),
          Obx(
            () => Wrap(
              spacing: 16,
              runSpacing: 16,
              children:
                  controller.availableThemeTypes.map((themeType) {
                    final isSelected = controller.selectedThemeType == themeType;
                    final color = controller.getThemeTypeColor(themeType);
                    final name = controller.getThemeTypeName(themeType);

                    return GestureDetector(
                      onTap: () => controller.previewThemeType(themeType),
                      child: Column(
                        children: [
                          Container(
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              color: color,
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: isSelected ? Colors.black : Colors.grey.shade300,
                                width: isSelected ? 3 : 1,
                              ),
                              boxShadow:
                                  isSelected
                                      ? [BoxShadow(color: color.withValues(alpha: 0.3), blurRadius: 8, spreadRadius: 2)]
                                      : null,
                            ),
                            child: isSelected ? const Icon(Icons.check, color: Colors.white, size: 24) : null,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            name,
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                              color: isSelected ? Colors.black : Colors.grey.shade700,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建AppBar样式选择区域
  Widget _buildAppBarStyleSection() {
    return CommonCard(
      title: 'AppBar样式',
      child: Column(
        children: [
          const Text('选择AppBar的显示样式', style: TextStyle(color: Colors.grey, fontSize: 14)),
          const SizedBox(height: 16),
          Obx(
            () => Column(
              children:
                  controller.availableAppBarStyles.map((appBarStyle) {
                    final isSelected = controller.selectedAppBarStyle == appBarStyle;
                    final name = controller.getAppBarStyleName(appBarStyle);
                    final description = _getAppBarStyleDescription(appBarStyle);

                    return Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      child: InkWell(
                        onTap: () => controller.previewAppBarStyle(appBarStyle),
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: isSelected ? Colors.blue.shade600 : Colors.grey.shade300,
                              width: isSelected ? 2 : 1,
                            ),
                            borderRadius: BorderRadius.circular(8),
                            color: isSelected ? Colors.blue.shade50 : Colors.white,
                          ),
                          child: Row(
                            children: [
                              Container(
                                width: 20,
                                height: 20,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: isSelected ? Colors.blue.shade600 : Colors.grey.shade400,
                                    width: 2,
                                  ),
                                  color: isSelected ? Colors.blue.shade600 : Colors.transparent,
                                ),
                                child: isSelected ? const Icon(Icons.check, color: Colors.white, size: 12) : null,
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      name,
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                        color: isSelected ? Colors.blue.shade700 : Colors.black,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(description, style: TextStyle(fontSize: 14, color: Colors.grey.shade600)),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Obx(
      () => Column(
        children: [
          Row(
            children: [
              Expanded(
                child: CommonButton(
                  text: '重置默认',
                  type: ButtonType.secondary,
                  onPressed: controller.isSaving ? null : controller.resetToDefault,
                  isLoading: controller.isSaving,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: CommonButton(
                  text: controller.hasUnsavedChanges ? '保存' : '完成',
                  onPressed:
                      controller.isSaving
                          ? null
                          : (controller.hasUnsavedChanges ? controller.saveAndApply : () => Get.back()),
                  isLoading: controller.isSaving,
                ),
              ),
            ],
          ),
          // 添加底部安全区域，避免按钮被遮盖
          SizedBox(height: MediaQuery.of(Get.context!).padding.bottom + 16),
        ],
      ),
    );
  }

  /// 获取AppBar样式描述
  String _getAppBarStyleDescription(AppBarStyle style) {
    switch (style) {
      case AppBarStyle.colored:
        return '使用主题颜色作为背景，适合大多数页面';
      case AppBarStyle.white:
        return '白色背景，适合设置和表单页面';
      case AppBarStyle.transparent:
        return '透明背景，适合特殊场景';
    }
  }
}

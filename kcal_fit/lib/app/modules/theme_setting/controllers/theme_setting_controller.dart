import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../shared/controllers/theme_controller.dart';
import '../../../../core/theme/app_theme.dart';

/// 主题设置页面控制器
/// 继承自ThemeController，专门用于主题设置页面
class ThemeSettingController extends ThemeController {
  /// 是否显示预览模式提示
  final RxBool _showPreviewTip = true.obs;

  /// 获取是否显示预览提示
  bool get showPreviewTip => _showPreviewTip.value;

  @override
  void onInit() {
    super.onInit();
    _loadPreviewTipSetting();
  }

  /// 加载预览提示设置
  void _loadPreviewTipSetting() {
    // 这里可以从本地存储加载用户是否要显示预览提示的设置
    // 暂时默认显示
  }

  /// 隐藏预览提示
  void hidePreviewTip() {
    _showPreviewTip.value = false;
    // 这里可以保存用户的选择到本地存储
  }

  /// 预览主题类型
  void previewThemeType(ThemeType themeType) {
    selectThemeType(themeType);
    previewTheme(themeType: themeType);
  }

  /// 预览AppBar样式
  void previewAppBarStyle(AppBarStyle appBarStyle) {
    selectAppBarStyle(appBarStyle);
    previewTheme(appBarStyle: appBarStyle);
  }

  /// 保存并应用设置
  Future<void> saveAndApply() async {
    await applyThemeSettings();
    Get.back(); // 返回上一页
  }

  /// 取消设置并返回
  void cancelAndBack() {
    if (hasUnsavedChanges) {
      // 显示确认对话框
      Get.dialog(
        AlertDialog(
          title: const Text('确认取消'),
          content: const Text('您有未保存的更改，确定要取消吗？'),
          actions: [
            TextButton(
              onPressed: () => Get.back(), // 关闭对话框
              child: const Text('继续编辑'),
            ),
            TextButton(
              onPressed: () {
                Get.back(); // 关闭对话框
                discardChanges();
                Get.back(); // 返回上一页
              },
              child: const Text('确定取消'),
            ),
          ],
        ),
      );
    } else {
      Get.back(); // 直接返回
    }
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/add_diet_record_controller.dart';
// ignore: depend_on_referenced_packages
import 'package:intl/intl.dart';

import '../../../shared/widgets/common_app_bar.dart';
import '../../../../core/services/theme_service.dart';
import '../../../../core/theme/app_theme.dart';

class AddDietRecordView extends GetView<AddDietRecordController> {
  const AddDietRecordView({super.key});

  // 缓存日期格式化器，避免重复创建
  static DateFormat? _cachedDateFormat;
  DateFormat get _dateFormat {
    _cachedDateFormat ??= DateFormat('EEEE', 'zh_CN');
    return _cachedDateFormat!;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: _buildBody(context),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  // 用于构建应用程序的顶部导航栏。
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return SmartAppBar(title: '记录饮食', actions: [_OptimizedSaveButton(controller: controller)]);
  }

  Widget _buildBody(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTitleSection(),
          const SizedBox(height: 8),
          _buildDescriptionSection(),
          const SizedBox(height: 20),
          _buildOptimizedInputSection(),
          const SizedBox(height: 20),
          _buildOptimizedMealTypeSection(),
          const SizedBox(height: 20),
          _buildOptimizedDateSection(),
          const SizedBox(height: 100), // 为悬浮按钮留出足够空间
        ],
      ),
    );
  }

  Widget _buildTitleSection() {
    return const Text('📝 记录今日饮食', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.black87));
  }

  Widget _buildDescriptionSection() {
    return Text(
      '详细记录您的饮食内容，系统将智能分析营养成分、热量摄入及各类维生素矿物质含量，助您科学管理健康饮食',
      style: TextStyle(fontSize: 14, color: Colors.grey[600], height: 1.4),
    );
  }

  /// 优化的输入框组件 - 减少重建和提升键盘响应速度
  Widget _buildOptimizedInputSection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[200]!, width: 1),
        boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.08), blurRadius: 12, offset: const Offset(0, 4))],
      ),
      child: TextField(
        autofocus: false, // 避免自动聚焦导致的键盘弹出延迟
        focusNode: controller.focusNode,
        maxLines: null,
        decoration: InputDecoration(
          hintText: '请详细描述您的饮食内容\n\n例如：\n• 吃了2个煎蛋，1杯牛奶，1片全麦面包',
          hintStyle: TextStyle(color: Colors.grey[400], fontSize: 15, height: 1.4),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(20),
        ),
        keyboardType: TextInputType.multiline,
        textInputAction: TextInputAction.newline,
        style: const TextStyle(fontSize: 16, height: 1.4),
        onChanged: (value) => controller.description.value = value,
        controller: controller.textEditingController,
        // 优化键盘性能
        enableInteractiveSelection: true,
        scrollPhysics: const ClampingScrollPhysics(),
      ),
    );
  }

  /// 优化的餐次选择组件 - 减少不必要的重建
  Widget _buildOptimizedMealTypeSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green[100]!, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.restaurant, size: 22, color: Colors.green[600]),
              const SizedBox(width: 8),
              const Text('餐次类型', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: Colors.black87)),
            ],
          ),
          const SizedBox(height: 12),
          _OptimizedMealTypeSelector(controller: controller),
        ],
      ),
    );
  }

  /// 优化的日期选择组件 - 缓存日期格式化
  Widget _buildOptimizedDateSection() {
    return InkWell(
      onTap: () => controller.selectDate(Get.context!),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.blue[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.blue[100]!, width: 1),
        ),
        child: Row(
          children: [
            Icon(Icons.calendar_today, size: 22, color: Colors.blue[600]),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('记录日期', style: TextStyle(fontSize: 14, color: Colors.grey, fontWeight: FontWeight.w500)),
                  const SizedBox(height: 2),
                  _OptimizedDateDisplay(controller: controller, dateFormat: _dateFormat),
                ],
              ),
            ),
            Icon(Icons.keyboard_arrow_right, color: Colors.blue[400], size: 20),
          ],
        ),
      ),
    );
  }

  // 底部悬浮按钮
  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: controller.startVoiceInput,
      icon: const Icon(Icons.mic, color: Colors.white),
      label: const Text('语音输入', style: TextStyle(color: Colors.white, fontWeight: FontWeight.w500)),
      backgroundColor: Colors.blue[600],
      elevation: 4,
    );
  }
}

/// 优化的保存按钮组件 - 避免重复查找 ThemeService，支持加载状态
class _OptimizedSaveButton extends StatelessWidget {
  final AddDietRecordController controller;

  const _OptimizedSaveButton({required this.controller});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final isSaving = controller.isSaving.value;

      return TextButton(
        onPressed: isSaving ? null : controller.saveRecord, // 保存时禁用按钮
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isSaving) ...[
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(_getButtonTextColor(context)),
                ),
              ),
              const SizedBox(width: 8),
            ],
            Text(
              isSaving ? "保存中..." : "保存",
              style: TextStyle(
                fontSize: 18,
                color: isSaving ? _getButtonTextColor(context).withValues(alpha: 0.6) : _getButtonTextColor(context),
              ),
            ),
          ],
        ),
      );
    });
  }

  Color _getButtonTextColor(BuildContext context) {
    // 缓存 ThemeService 查找
    final themeService = Get.find<ThemeService>();
    final isWhiteAppBar = themeService.currentAppBarStyle == AppBarStyle.white;
    return isWhiteAppBar ? Theme.of(context).primaryColor : Colors.white;
  }
}

/// 优化的餐次选择器组件 - 减少重建
class _OptimizedMealTypeSelector extends StatelessWidget {
  final AddDietRecordController controller;

  const _OptimizedMealTypeSelector({required this.controller});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final selectedMealType = controller.selectedMealType.value;
      return Wrap(
        spacing: 8,
        runSpacing: 8,
        children:
            controller.mealTypes.map((mealType) {
              final isSelected = selectedMealType == mealType;
              return _MealTypeChip(
                mealType: mealType,
                isSelected: isSelected,
                onTap: () => controller.selectMealType(mealType),
              );
            }).toList(),
      );
    });
  }
}

/// 餐次选择芯片组件 - 独立组件避免整体重建
class _MealTypeChip extends StatelessWidget {
  final String mealType;
  final bool isSelected;
  final VoidCallback onTap;

  const _MealTypeChip({required this.mealType, required this.isSelected, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? Colors.green[600] : Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: isSelected ? Colors.green[600]! : Colors.green[300]!, width: 1.5),
        ),
        child: Text(
          mealType,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isSelected ? Colors.white : Colors.green[700],
          ),
        ),
      ),
    );
  }
}

/// 优化的日期显示组件 - 缓存日期格式化
class _OptimizedDateDisplay extends StatelessWidget {
  final AddDietRecordController controller;
  final DateFormat dateFormat;

  const _OptimizedDateDisplay({required this.controller, required this.dateFormat});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final date = controller.dataDate.value;
      final now = DateTime.now();
      final isToday = date.year == now.year && date.month == now.month && date.day == now.day;

      return Text(
        '${date.month}月${date.day}日 ${dateFormat.format(date)}${isToday ? ' (今天)' : ''}',
        style: TextStyle(fontSize: 16, color: Colors.blue[700], fontWeight: FontWeight.w500),
      );
    });
  }
}

import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';

import '../../../../core/services/http/http_api.dart';
import '../../../../core/services/http/http_util.dart';
import '../../../../core/services/stt/speech_to_text.dart';
import '../../../data/services/sqflite/dao/impl/diet_analysis_dao.dart';
import '../../my/controllers/my_controller.dart';

var logger = Logger();

class AddDietRecordController extends GetxController {
  Rx<DateTime> dataDate = DateTime.now().obs; //数据日期--默认今天
  RxString selectedMealType = '早餐'.obs; // 选中的餐次类型

  // 延迟初始化语音识别工具，避免首次加载时的性能影响
  SpeechToTextUtil? _sttUtil;
  SpeechToTextUtil get sttUtil {
    _sttUtil ??= Get.find<SpeechToTextUtil>();
    return _sttUtil!;
  }

  final httpUtil = Get.find<HttpUtil>(); // 网络请求工具
  // final dietAnalysisDao = Get.find<DietAnalysisDao>(); // 数据库工具
  final Uuid _uuid = Uuid(); // 生成唯一标识符
  final textEditingController = TextEditingController(); // 文本输入控制器
  final description = ''.obs; // 饮食的描述信息（需要用户输入）
  final FocusNode focusNode = FocusNode(); // 焦点节点

  // 餐次选项列表 - 使用 const 提升性能
  final List<String> mealTypes = const ['早餐', '早加餐', '午餐', '午加餐', '晚餐', '晚加餐'];

  // 保存状态控制 - 防止重复提交
  final RxBool isSaving = false.obs;

  // 防抖控制 - 记录最后一次点击时间
  DateTime? _lastSaveAttempt;
  static const Duration _debounceDelay = Duration(milliseconds: 1000); // 1秒防抖

  // 语音识别监听器是否已初始化
  bool _speechListenersInitialized = false;

  @override
  void onInit() {
    super.onInit();
    // 延迟初始化语音识别监听器，只在需要时才设置
    _initializeSpeechListenersLazily();
  }

  /// 延迟初始化语音识别监听器
  void _initializeSpeechListenersLazily() {
    if (_speechListenersInitialized) return;

    try {
      ever(sttUtil.recognizedWords, (value) {
        textEditingController.text = "${description.value} $value";
      });
      ever(sttUtil.finalRecognizedWords, (value) {
        textEditingController.text = "${description.value} $value";
        description.value = textEditingController.text;
      });
      _speechListenersInitialized = true;
    } catch (e) {
      logger.w('语音识别监听器初始化失败: $e');
    }
  }

  @override
  void onClose() {
    // 重置保存状态，防止内存泄漏
    isSaving.value = false;

    textEditingController.dispose();
    focusNode.dispose();
    // 只有在语音识别工具已初始化时才调用 dispose
    if (_sttUtil != null) {
      _sttUtil!.dispose();
    }
    super.onClose();
  }

  /// 保存记录
  void saveRecord() async {
    final now = DateTime.now();

    // 防重复提交检查
    if (isSaving.value) {
      logger.w('保存操作正在进行中，忽略重复点击');
      return;
    }

    // 防抖检查 - 避免短时间内多次点击
    if (_lastSaveAttempt != null && now.difference(_lastSaveAttempt!) < _debounceDelay) {
      logger.w('点击过于频繁，忽略本次点击');
      return;
    }
    _lastSaveAttempt = now;

    if (description.isEmpty) {
      Get.snackbar('温馨提示', '请详细描述您的饮食内容\n例如: 早餐吃了2个鸡蛋和1杯牛奶', duration: const Duration(milliseconds: 2000));
      return;
    }

    // 设置保存状态，防止重复提交
    isSaving.value = true;
    logger.i('开始保存饮食记录');

    final analyzeId = _uuid.v4();
    final currentTime = DateTime.now();

    var data = {
      "diet_description": description(),
      "created_time": currentTime.toString(),
      "meal_time": dataDate().toString(),
      "meal_type": selectedMealType.value, // 添加餐次类型
      "analyze_id": analyzeId,
    };

    try {
      // 1. 先保存主饮食记录到本地数据库
      final initialRecord = {
        "analyze_id": analyzeId,
        "meal_time": dataDate().toIso8601String(),
        "meal_type": selectedMealType.value,
        "diet_description": description(),
        "notes": description(),
        "created_time": currentTime.toIso8601String(),
        "update_time": currentTime.toIso8601String(),
      };

      // 获取数据库DAO实例
      final dietAnalysisDao = Get.find<DietAnalysisDao>();
      await dietAnalysisDao.addAnalysis(initialRecord);
      logger.i('主饮食记录已保存到本地数据库，分析ID: $analyzeId');

      // 2. 提交饮食信息到服务器进行分析（设置超时）
      await HttpApi.submitDietInfo(data).timeout(
        const Duration(seconds: 30), // 30秒超时
        onTimeout: () {
          logger.w('服务器请求超时，但本地记录已保存');
          throw TimeoutException('服务器响应超时，请稍后重试', const Duration(seconds: 30));
        },
      );
      logger.i('饮食信息已提交到服务器进行分析');

      // 刷新我的页面的统计数据
      if (Get.isRegistered<MyController>()) {
        Get.find<MyController>().loadStatisticsData();
      }

      // 保存成功，立即重置状态并返回上一页
      isSaving.value = false;
      Get.back();
      Get.snackbar('保存成功', '您的饮食记录已成功保存！\n正在进行营养分析...', duration: const Duration(milliseconds: 2000));
      logger.i('饮食记录保存完成');
    } catch (e) {
      logger.e('保存饮食记录失败: $e');

      // 根据不同的错误类型提供不同的提示
      String errorMessage;
      if (e is TimeoutException) {
        errorMessage = '服务器响应超时，但本地记录已保存\n营养分析将稍后完成';
      } else if (e.toString().contains('network') || e.toString().contains('connection')) {
        errorMessage = '网络连接异常，但本地记录已保存\n请检查网络连接';
      } else {
        errorMessage = '保存过程中出现异常，请稍后重试\n本地记录可能已保存';
      }

      Get.snackbar(
        '保存提醒',
        errorMessage,
        duration: const Duration(milliseconds: 3000),
        backgroundColor: Colors.orange[100],
        colorText: Colors.orange[800],
      );
    } finally {
      // 无论成功还是失败，都要重置保存状态
      isSaving.value = false;
      logger.i('重置保存状态');
    }
  }

  // ---------------------语音输入相关方法---------------------
  // 开始语音输入
  void startVoiceInput() {
    // 确保语音识别监听器已初始化
    _initializeSpeechListenersLazily();

    // 使用缓存的语音识别工具实例
    sttUtil.handleVoiceInput();
    focusNode.unfocus();
  }

  //选择日期
  Future<void> selectDate(BuildContext context) async {
    final DateTime now = DateTime.now();
    await showCupertinoModalPopup<DateTime>(
      context: context,
      builder: (BuildContext context) {
        return Container(
          height: 216,
          padding: const EdgeInsets.only(top: 6.0),
          margin: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          color: CupertinoColors.systemBackground.resolveFrom(context),
          child: SafeArea(
            top: false,
            child: CupertinoDatePicker(
              mode: CupertinoDatePickerMode.date, // 仅选择日期
              initialDateTime: dataDate.value,
              maximumDate: now, // 限制最大日期
              onDateTimeChanged: (DateTime newDateTime) {
                // 保持原有的时间，只更新日期部分
                final currentTime = dataDate.value;
                dataDate.value = DateTime(
                  newDateTime.year,
                  newDateTime.month,
                  newDateTime.day,
                  currentTime.hour,
                  currentTime.minute,
                );
              },
            ),
          ),
        );
      },
    );
  }

  /// 选择餐次类型
  void selectMealType(String mealType) {
    selectedMealType.value = mealType;
  }

  /// 预加载页面资源 - 提升后续访问性能
  static void preloadResources() {
    try {
      // 预加载语音识别服务
      if (Get.isRegistered<SpeechToTextUtil>()) {
        Get.find<SpeechToTextUtil>();
      }

      // 预加载HTTP工具
      if (Get.isRegistered<HttpUtil>()) {
        Get.find<HttpUtil>();
      }

      logger.i('AddDietRecord 页面资源预加载完成');
    } catch (e) {
      logger.w('AddDietRecord 页面资源预加载失败: $e');
    }
  }
}

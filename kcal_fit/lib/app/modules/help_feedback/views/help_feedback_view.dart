import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../shared/widgets/common_app_bar.dart';
import '../controllers/help_feedback_controller.dart';

class HelpFeedbackView extends GetView<HelpFeedbackController> {
  const HelpFeedbackView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: const SmartAppBar(title: '帮助与反馈'),
      body: DefaultTabController(
        length: 2,
        child: Column(
          children: [
            Container(
              color: Colors.white,
              child: const TabBar(
                labelColor: Colors.blue,
                unselectedLabelColor: Colors.grey,
                indicatorColor: Colors.blue,
                tabs: [Tab(text: '常见问题'), Tab(text: '意见反馈')],
              ),
            ),
            Expanded(child: TabBarView(children: [_buildFaqTab(), _buildFeedbackTab()])),
          ],
        ),
      ),
    );
  }

  /// 构建常见问题标签页
  Widget _buildFaqTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 客服联系卡片
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.shade100),
            ),
            child: Column(
              children: [
                Icon(Icons.headset_mic, size: 32, color: Colors.blue.shade600),
                const SizedBox(height: 8),
                Text(
                  '需要人工帮助？',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: Colors.blue.shade800),
                ),
                const SizedBox(height: 4),
                Text('联系我们的客服团队', style: TextStyle(fontSize: 14, color: Colors.blue.shade600)),
                const SizedBox(height: 12),
                ElevatedButton(
                  onPressed: controller.contactCustomerService,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue.shade600,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                  ),
                  child: const Text('联系客服'),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // 常见问题列表
          Text('常见问题', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600, color: Colors.grey[800])),
          const SizedBox(height: 16),

          Obx(
            () => ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: controller.faqList.length,
              itemBuilder: (context, index) {
                final faq = controller.faqList[index];
                final isExpanded = controller.expandedStates[index];

                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 4, offset: const Offset(0, 2)),
                    ],
                  ),
                  child: ExpansionTile(
                    title: Text(faq['question']!, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
                    trailing: Icon(isExpanded ? Icons.expand_less : Icons.expand_more, color: Colors.blue.shade600),
                    onExpansionChanged: (expanded) {
                      controller.toggleFaqExpansion(index);
                    },
                    children: [
                      Padding(
                        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                        child: Text(
                          faq['answer']!,
                          style: TextStyle(fontSize: 14, color: Colors.grey[600], height: 1.5),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建反馈标签页
  Widget _buildFeedbackTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: controller.formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 反馈类型选择
            Text('反馈类型', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: Colors.grey[800])),
            const SizedBox(height: 12),
            Obx(
              () => Wrap(
                spacing: 12,
                runSpacing: 8,
                children:
                    controller.feedbackTypes.map((type) {
                      final isSelected = controller.selectedFeedbackType.value == type;
                      return GestureDetector(
                        onTap: () => controller.updateFeedbackType(type),
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            color: isSelected ? Colors.blue.shade600 : Colors.white,
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(color: isSelected ? Colors.blue.shade600 : Colors.grey.shade300),
                          ),
                          child: Text(
                            type,
                            style: TextStyle(
                              color: isSelected ? Colors.white : Colors.grey[700],
                              fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                            ),
                          ),
                        ),
                      );
                    }).toList(),
              ),
            ),
            const SizedBox(height: 24),

            // 反馈内容
            Text('反馈内容', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: Colors.grey[800])),
            const SizedBox(height: 12),
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 4, offset: const Offset(0, 2)),
                ],
              ),
              child: TextFormField(
                controller: controller.feedbackController,
                maxLines: 6,
                decoration: const InputDecoration(
                  hintText: '请详细描述您遇到的问题或建议...',
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.all(16),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '请输入反馈内容';
                  }
                  if (value.trim().length < 10) {
                    return '反馈内容至少需要10个字符';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(height: 24),

            // 联系方式
            Text('联系方式（选填）', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: Colors.grey[800])),
            const SizedBox(height: 12),
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 4, offset: const Offset(0, 2)),
                ],
              ),
              child: TextFormField(
                controller: controller.contactController,
                decoration: const InputDecoration(
                  hintText: '请输入您的邮箱或手机号或微信号',
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.all(16),
                ),
              ),
            ),
            const SizedBox(height: 32),

            // 提交按钮
            SizedBox(
              width: double.infinity,
              height: 48,
              child: Obx(
                () => ElevatedButton(
                  onPressed: controller.isSubmitting.value ? null : controller.submitFeedback,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue.shade600,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  ),
                  child:
                      controller.isSubmitting.value
                          ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                          : const Text('提交反馈', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

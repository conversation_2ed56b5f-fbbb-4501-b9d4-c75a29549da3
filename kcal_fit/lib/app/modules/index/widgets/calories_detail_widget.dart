import 'package:flutter/material.dart';
import 'package:health/health.dart';
import 'package:fl_chart/fl_chart.dart';

/// 健康数据详情组件
/// 用于显示卡路里消耗和步数的详细数据和图表
class CaloriesDetailWidget {
  /// 构建卡路里折线图
  static Widget buildCaloriesChart(List<HealthDataPoint> activeData, List<HealthDataPoint> basalData, Color color) {
    // 合并并排序所有数据
    final allData = <HealthDataPoint>[];
    allData.addAll(activeData);
    allData.addAll(basalData);

    if (allData.isEmpty) {
      return const Center(child: Text('暂无数据', style: TextStyle(color: Colors.grey)));
    }

    allData.sort((a, b) => a.dateFrom.compareTo(b.dateFrom));

    // 按小时分组数据
    final Map<int, double> hourlyActiveCalories = {};
    final Map<int, double> hourlyBasalCalories = {};

    for (var data in activeData) {
      final hour = data.dateFrom.hour;
      final value = (data.value as NumericHealthValue).numericValue.toDouble();
      hourlyActiveCalories[hour] = (hourlyActiveCalories[hour] ?? 0) + value;
    }

    for (var data in basalData) {
      final hour = data.dateFrom.hour;
      final value = (data.value as NumericHealthValue).numericValue.toDouble();
      hourlyBasalCalories[hour] = (hourlyBasalCalories[hour] ?? 0) + value;
    }

    // 创建图表数据点
    final List<FlSpot> activeSpots = [];
    final List<FlSpot> basalSpots = [];
    final List<FlSpot> totalSpots = [];

    for (int hour = 0; hour < 24; hour++) {
      final activeValue = hourlyActiveCalories[hour] ?? 0;
      final basalValue = hourlyBasalCalories[hour] ?? 0;
      final totalValue = activeValue + basalValue;

      if (activeValue > 0) activeSpots.add(FlSpot(hour.toDouble(), activeValue));
      if (basalValue > 0) basalSpots.add(FlSpot(hour.toDouble(), basalValue));
      if (totalValue > 0) totalSpots.add(FlSpot(hour.toDouble(), totalValue));
    }

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: true,
          horizontalInterval: 100,
          verticalInterval: 4,
          getDrawingHorizontalLine: (value) {
            return FlLine(color: Colors.grey.withValues(alpha: 0.2), strokeWidth: 1);
          },
          getDrawingVerticalLine: (value) {
            return FlLine(color: Colors.grey.withValues(alpha: 0.2), strokeWidth: 1);
          },
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: 4,
              getTitlesWidget: (double value, TitleMeta meta) {
                return SideTitleWidget(
                  axisSide: meta.axisSide,
                  child: Text('${value.toInt()}:00', style: const TextStyle(fontSize: 10, color: Colors.grey)),
                );
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              interval: 100,
              reservedSize: 40,
              getTitlesWidget: (double value, TitleMeta meta) {
                return Text('${value.toInt()}', style: const TextStyle(fontSize: 10, color: Colors.grey));
              },
            ),
          ),
        ),
        borderData: FlBorderData(show: true, border: Border.all(color: Colors.grey.withValues(alpha: 0.3))),
        minX: 0,
        maxX: 23,
        minY: 0,
        lineBarsData: [
          // 活动消耗线
          if (activeSpots.isNotEmpty)
            LineChartBarData(
              spots: activeSpots,
              isCurved: true,
              color: Colors.blue,
              barWidth: 2,
              isStrokeCapRound: true,
              dotData: const FlDotData(show: false),
              belowBarData: BarAreaData(show: true, color: Colors.blue.withValues(alpha: 0.1)),
            ),
          // 基础代谢线
          if (basalSpots.isNotEmpty)
            LineChartBarData(
              spots: basalSpots,
              isCurved: true,
              color: Colors.green,
              barWidth: 2,
              isStrokeCapRound: true,
              dotData: const FlDotData(show: false),
              belowBarData: BarAreaData(show: true, color: Colors.green.withValues(alpha: 0.1)),
            ),
          // 总消耗线（如果同时有活动和基础代谢数据）
          if (activeSpots.isNotEmpty && basalSpots.isNotEmpty && totalSpots.isNotEmpty)
            LineChartBarData(
              spots: totalSpots,
              isCurved: true,
              color: color,
              barWidth: 3,
              isStrokeCapRound: true,
              dotData: FlDotData(
                show: true,
                getDotPainter: (spot, percent, barData, index) {
                  return FlDotCirclePainter(radius: 3, color: color, strokeWidth: 1, strokeColor: Colors.white);
                },
              ),
            ),
        ],
        lineTouchData: LineTouchData(
          enabled: true,
          touchTooltipData: LineTouchTooltipData(
            getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
              return touchedBarSpots.map((barSpot) {
                final flSpot = barSpot;
                return LineTooltipItem(
                  '${flSpot.x.toInt()}:00\n${flSpot.y.toInt()} kcal',
                  const TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 12),
                );
              }).toList();
            },
          ),
        ),
      ),
    );
  }

  /// 构建卡路里数据列表
  static Widget buildCaloriesDataList(List<HealthDataPoint> activeData, List<HealthDataPoint> basalData, Color color) {
    final allData = <HealthDataPoint>[];
    allData.addAll(activeData);
    allData.addAll(basalData);

    if (allData.isEmpty) {
      return const Center(child: Text('暂无数据', style: TextStyle(color: Colors.grey)));
    }

    // 按时间排序
    allData.sort((a, b) => a.dateFrom.compareTo(b.dateFrom));

    return ListView.builder(
      shrinkWrap: true,
      itemCount: allData.length,
      itemBuilder: (context, index) {
        final data = allData[index];
        final value = (data.value as NumericHealthValue).numericValue.toInt();
        final timeRange = _formatTimeRange(data.dateFrom, data.dateTo);
        final isActive = data.type == HealthDataType.ACTIVE_ENERGY_BURNED;

        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Row(
            children: [
              Icon(
                isActive ? Icons.directions_run : Icons.favorite,
                size: 16,
                color: isActive ? Colors.blue[600] : Colors.green[600],
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(timeRange, style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500)),
                    Text(isActive ? '活动消耗' : '基础代谢', style: TextStyle(fontSize: 11, color: Colors.grey[600])),
                    Text('来源: ${data.sourceName}', style: TextStyle(fontSize: 11, color: Colors.grey[600])),
                  ],
                ),
              ),
              Text(
                '$value kcal',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: isActive ? Colors.blue[700] : Colors.green[700],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 格式化时间范围
  static String _formatTimeRange(DateTime start, DateTime end) {
    final startTime = '${start.hour.toString().padLeft(2, '0')}:${start.minute.toString().padLeft(2, '0')}';
    final endTime = '${end.hour.toString().padLeft(2, '0')}:${end.minute.toString().padLeft(2, '0')}';

    // 如果是同一时间点，只显示一个时间
    if (start.isAtSameMomentAs(end)) {
      return startTime;
    }

    return '$startTime - $endTime';
  }

  /// 构建步数折线图
  static Widget buildStepsChart(List<HealthDataPoint> stepsData, Color color) {
    if (stepsData.isEmpty) {
      return const Center(child: Text('暂无数据', style: TextStyle(color: Colors.grey)));
    }

    // 按小时分组步数数据
    final Map<int, int> hourlySteps = {};

    for (var data in stepsData) {
      final hour = data.dateFrom.hour;
      final value = (data.value as NumericHealthValue).numericValue.toInt();
      hourlySteps[hour] = (hourlySteps[hour] ?? 0) + value;
    }

    // 创建图表数据点
    final List<FlSpot> spots = [];
    int maxSteps = 0;

    for (int hour = 0; hour < 24; hour++) {
      final steps = hourlySteps[hour] ?? 0;
      if (steps > 0) {
        spots.add(FlSpot(hour.toDouble(), steps.toDouble()));
        if (steps > maxSteps) maxSteps = steps;
      }
    }

    if (spots.isEmpty) {
      return const Center(child: Text('暂无数据', style: TextStyle(color: Colors.grey)));
    }

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: true,
          horizontalInterval: maxSteps > 1000 ? (maxSteps / 5).roundToDouble() : 200,
          verticalInterval: 4,
          getDrawingHorizontalLine: (value) {
            return FlLine(color: Colors.grey.withValues(alpha: 0.2), strokeWidth: 1);
          },
          getDrawingVerticalLine: (value) {
            return FlLine(color: Colors.grey.withValues(alpha: 0.2), strokeWidth: 1);
          },
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: 4,
              getTitlesWidget: (double value, TitleMeta meta) {
                return SideTitleWidget(
                  axisSide: meta.axisSide,
                  child: Text('${value.toInt()}:00', style: const TextStyle(fontSize: 10, color: Colors.grey)),
                );
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              interval: maxSteps > 1000 ? (maxSteps / 5).roundToDouble() : 200,
              reservedSize: 50,
              getTitlesWidget: (double value, TitleMeta meta) {
                return Text(
                  value >= 1000 ? '${(value / 1000).toStringAsFixed(1)}k' : '${value.toInt()}',
                  style: const TextStyle(fontSize: 10, color: Colors.grey),
                );
              },
            ),
          ),
        ),
        borderData: FlBorderData(show: true, border: Border.all(color: Colors.grey.withValues(alpha: 0.3))),
        minX: 0,
        maxX: 23,
        minY: 0,
        maxY: maxSteps.toDouble() * 1.1,
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true,
            color: color,
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: FlDotData(
              show: true,
              getDotPainter: (spot, percent, barData, index) {
                return FlDotCirclePainter(radius: 4, color: color, strokeWidth: 2, strokeColor: Colors.white);
              },
            ),
            belowBarData: BarAreaData(show: true, color: color.withValues(alpha: 0.1)),
          ),
        ],
        lineTouchData: LineTouchData(
          enabled: true,
          touchTooltipData: LineTouchTooltipData(
            getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
              return touchedBarSpots.map((barSpot) {
                final flSpot = barSpot;
                return LineTooltipItem(
                  '${flSpot.x.toInt()}:00\n${flSpot.y.toInt()} 步',
                  const TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 12),
                );
              }).toList();
            },
          ),
        ),
      ),
    );
  }

  /// 构建步数数据列表
  static Widget buildStepsDataList(List<HealthDataPoint> stepsData, Color color) {
    if (stepsData.isEmpty) {
      return const Center(child: Text('暂无数据', style: TextStyle(color: Colors.grey)));
    }

    // 按时间排序
    final sortedData = List<HealthDataPoint>.from(stepsData);
    sortedData.sort((a, b) => a.dateFrom.compareTo(b.dateFrom));

    return ListView.builder(
      shrinkWrap: true,
      itemCount: sortedData.length,
      itemBuilder: (context, index) {
        final data = sortedData[index];
        final value = (data.value as NumericHealthValue).numericValue.toInt();
        final timeRange = _formatTimeRange(data.dateFrom, data.dateTo);

        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Row(
            children: [
              Icon(Icons.directions_walk, size: 16, color: color),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(timeRange, style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500)),
                    Text('来源: ${data.sourceName}', style: TextStyle(fontSize: 11, color: Colors.grey[600])),
                  ],
                ),
              ),
              Text('$value 步', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600, color: color)),
            ],
          ),
        );
      },
    );
  }
}

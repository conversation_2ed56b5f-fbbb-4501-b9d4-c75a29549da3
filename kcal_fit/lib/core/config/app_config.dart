import 'dart:core';

import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

var logger = Logger();

/// 应用全局配置管理类
class AppConfig {
  static late bool isProduction; // 是否为生产环境
  static late Map<String, dynamic> jpushConfig; // 极光推送配置
  static late Map<String, dynamic> apiConfig; // API配置

  // 生产环境配置常量
  static const String _jpushAppKeyProd = '99102b4e8d7ac2959dd1b780';
  static const String _jpushChannelProd = 'kcal_fit';
  static const bool _jpushProductionProd = true;
  static const bool _jpushDebugProd = false;
  static const String _baseUrlProd = "https://kcalfit.huiziqin.com/";
  static const Duration _connectTimeOutProd = Duration(seconds: 5); // 连接超时时间

  // 开发环境配置常量
  static const String _jpushAppKeyDev = '99102b4e8d7ac2959dd1b780';
  static const String _jpushChannelDev = 'kcal_fit_dev';
  static const bool _jpushProductionDev = false;
  static const bool _jpushDebugDev = true;
  // static const String _baseUrlDev = "http://172.20.10.4:8080";
  static const String _baseUrlDev = "https://kcalfit.huiziqin.com/";
  static const Duration _connectTimeOutDev = Duration(seconds: 5); // 连接超时时间

  /// 初始化应用配置
  static Future<void> init() async {
    isProduction = kReleaseMode;
    logger.i("当前环境：${isProduction ? "生产环境" : "开发环境"}");
    _loadJpushConfig();
    _loadApiConfig();
  }

  /// 加载极光推送配置
  static void _loadJpushConfig() {
    jpushConfig = {
      'appKey': isProduction ? _jpushAppKeyProd : _jpushAppKeyDev,
      'channel': isProduction ? _jpushChannelProd : _jpushChannelDev,
      'production': isProduction ? _jpushProductionProd : _jpushProductionDev,
      'debug': isProduction ? _jpushDebugProd : _jpushDebugDev,
    };
  }

  static void _loadApiConfig() {
    apiConfig = {
      'baseUrl': isProduction ? _baseUrlProd : _baseUrlDev,
      'timeOut': isProduction ? _connectTimeOutProd : _connectTimeOutDev,
    };
  }
}

import 'package:logger/logger.dart';

/// 日志工具类 - 简化日志记录
class LoggerUtil {
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 3,
      lineLength: 80,
      colors: true,
      printEmojis: true,
      dateTimeFormat: DateTimeFormat.none,
    ),
  );

  /// 调试日志 - 仅在开发环境显示
  static void d(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.d(message, error: error, stackTrace: stackTrace);
  }

  /// 信息日志
  static void i(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.i(message, error: error, stackTrace: stackTrace);
  }

  /// 警告日志
  static void w(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.w(message, error: error, stackTrace: stackTrace);
  }

  /// 错误日志
  static void e(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.e(message, error: error, stackTrace: stackTrace);
  }

  /// 控制器生命周期日志
  static void lifecycle(String controllerName, String event) {
    d('$controllerName - $event');
  }

  /// 异步操作日志
  static void async(String operation, {bool start = true}) {
    if (start) {
      d('开始: $operation');
    } else {
      d('完成: $operation');
    }
  }
}

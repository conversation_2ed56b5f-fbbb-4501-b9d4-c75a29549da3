import 'package:flutter/foundation.dart';

import 'logger_util.dart';

/// 性能监控工具
/// 用于监控应用启动和数据加载的性能指标
class PerformanceMonitor {
  static final Map<String, DateTime> _startTimes = {};
  static final Map<String, Duration> _durations = {};
  static final List<String> _performanceLogs = [];

  /// 开始计时
  /// [operation] 操作名称
  static void startTimer(String operation) {
    _startTimes[operation] = DateTime.now();
    LoggerUtil.d('⏱️ 开始计时: $operation');
  }

  /// 结束计时
  /// [operation] 操作名称
  static Duration endTimer(String operation) {
    final startTime = _startTimes[operation];
    if (startTime == null) {
      LoggerUtil.w('⚠️ 未找到操作的开始时间: $operation');
      return Duration.zero;
    }

    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);
    _durations[operation] = duration;

    final logMessage = '⏱️ $operation 耗时: ${duration.inMilliseconds}ms';
    LoggerUtil.i(logMessage);
    _performanceLogs.add(logMessage);

    // 清理开始时间
    _startTimes.remove(operation);

    return duration;
  }

  /// 记录性能指标
  /// [operation] 操作名称
  /// [duration] 持续时间
  static void recordMetric(String operation, Duration duration) {
    _durations[operation] = duration;
    final logMessage = '📊 $operation: ${duration.inMilliseconds}ms';
    LoggerUtil.i(logMessage);
    _performanceLogs.add(logMessage);
  }

  /// 获取操作耗时
  /// [operation] 操作名称
  static Duration? getDuration(String operation) {
    return _durations[operation];
  }

  /// 获取所有性能指标
  static Map<String, Duration> getAllMetrics() {
    return Map.from(_durations);
  }

  /// 获取性能日志
  static List<String> getPerformanceLogs() {
    return List.from(_performanceLogs);
  }

  /// 打印性能报告
  static void printPerformanceReport() {
    if (_durations.isEmpty) {
      LoggerUtil.i('📊 暂无性能数据');
      return;
    }

    LoggerUtil.i('📊 ========== 性能报告 ==========');
    
    // 按耗时排序
    final sortedEntries = _durations.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    for (final entry in sortedEntries) {
      final operation = entry.key;
      final duration = entry.value;
      final status = _getPerformanceStatus(duration);
      LoggerUtil.i('📊 $operation: ${duration.inMilliseconds}ms $status');
    }

    // 计算总耗时
    final totalDuration = _durations.values.fold<Duration>(
      Duration.zero,
      (total, duration) => total + duration,
    );
    LoggerUtil.i('📊 总耗时: ${totalDuration.inMilliseconds}ms');
    LoggerUtil.i('📊 ================================');
  }

  /// 获取性能状态标识
  /// [duration] 持续时间
  static String _getPerformanceStatus(Duration duration) {
    final ms = duration.inMilliseconds;
    if (ms < 100) return '🟢'; // 优秀
    if (ms < 500) return '🟡'; // 良好
    if (ms < 1000) return '🟠'; // 一般
    return '🔴'; // 需要优化
  }

  /// 清除所有性能数据
  static void clear() {
    _startTimes.clear();
    _durations.clear();
    _performanceLogs.clear();
    LoggerUtil.d('🧹 性能监控数据已清除');
  }

  /// 检查是否有长时间运行的操作
  static void checkLongRunningOperations() {
    final now = DateTime.now();
    final longRunningOps = <String>[];

    for (final entry in _startTimes.entries) {
      final operation = entry.key;
      final startTime = entry.value;
      final runningTime = now.difference(startTime);

      if (runningTime.inSeconds > 10) {
        longRunningOps.add('$operation (${runningTime.inSeconds}s)');
      }
    }

    if (longRunningOps.isNotEmpty) {
      LoggerUtil.w('⚠️ 检测到长时间运行的操作: ${longRunningOps.join(', ')}');
    }
  }

  /// 启动性能监控
  /// 定期检查长时间运行的操作
  static void startMonitoring() {
    if (kDebugMode) {
      // 每30秒检查一次长时间运行的操作
      Future.doWhile(() async {
        await Future.delayed(const Duration(seconds: 30));
        checkLongRunningOperations();
        return true;
      });
    }
  }

  /// 记录应用启动关键节点
  static void recordStartupMilestone(String milestone) {
    final now = DateTime.now();
    final logMessage = '🚀 启动节点: $milestone (${now.toIso8601String()})';
    LoggerUtil.i(logMessage);
    _performanceLogs.add(logMessage);
  }

  /// 分析启动性能
  static void analyzeStartupPerformance() {
    LoggerUtil.i('🔍 ========== 启动性能分析 ==========');
    
    final startupOperations = _durations.entries
        .where((entry) => entry.key.contains('初始化') || entry.key.contains('加载'))
        .toList();

    if (startupOperations.isEmpty) {
      LoggerUtil.i('🔍 暂无启动相关的性能数据');
      return;
    }

    // 计算启动总时间
    final totalStartupTime = startupOperations.fold<Duration>(
      Duration.zero,
      (total, entry) => total + entry.value,
    );

    LoggerUtil.i('🔍 启动总耗时: ${totalStartupTime.inMilliseconds}ms');

    // 找出最耗时的启动操作
    final slowestOperation = startupOperations.reduce(
      (a, b) => a.value > b.value ? a : b,
    );

    LoggerUtil.i('🔍 最耗时的启动操作: ${slowestOperation.key} (${slowestOperation.value.inMilliseconds}ms)');

    // 性能建议
    if (totalStartupTime.inMilliseconds > 3000) {
      LoggerUtil.w('🔍 启动时间较长，建议优化');
    } else if (totalStartupTime.inMilliseconds > 1500) {
      LoggerUtil.i('🔍 启动时间适中，可进一步优化');
    } else {
      LoggerUtil.i('🔍 启动性能良好');
    }

    LoggerUtil.i('🔍 =====================================');
  }
}

import 'package:get/get.dart';
import 'logger_util.dart';

/// 状态管理器 - 简化状态管理
mixin StateManager {
  final RxBool _isLoading = false.obs;
  final RxBool _hasError = false.obs;
  final RxString _errorMessage = ''.obs;
  final RxBool _isEmpty = false.obs;

  // Getters
  bool get isLoading => _isLoading.value;
  bool get hasError => _hasError.value;
  String get errorMessage => _errorMessage.value;
  bool get isEmpty => _isEmpty.value;

  // 状态控制
  void setLoading(bool loading) => _isLoading.value = loading;
  void setError(String message) {
    _hasError.value = true;
    _errorMessage.value = message;
    _isLoading.value = false;
    LoggerUtil.e('状态错误', message);
  }

  void clearError() {
    _hasError.value = false;
    _errorMessage.value = '';
  }

  void setEmpty(bool empty) => _isEmpty.value = empty;
  void resetStates() {
    _isLoading.value = false;
    _hasError.value = false;
    _errorMessage.value = '';
    _isEmpty.value = false;
  }

  /// 执行异步操作 - 简化版本
  Future<T?> executeAsync<T>(Future<T> Function() operation) async {
    try {
      setLoading(true);
      final result = await operation();
      setLoading(false);
      return result;
    } catch (e) {
      setLoading(false);
      setError(e.toString());
      return null;
    }
  }
}

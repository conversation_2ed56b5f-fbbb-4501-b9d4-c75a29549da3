import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 日期选择器工具类
/// 提供单日期选择和日期范围选择功能
class DatePickerUtil {
  /// 显示单个日期选择对话框
  /// [context]: 必传，构建上下文
  /// [initialDate]: 初始选中的日期，可选
  /// [firstDate]: 可选择的最早日期，可选
  /// [lastDate]: 可选择的最晚日期，可选(默认当前日期)
  /// [config]: 自定义配置，可选
  /// [dialogWidth]: 对话框宽度，可选(默认325)
  /// [dialogHeight]: 对话框高度，可选(默认400)
  /// 返回: 选择的日期(DateTime)或null(未选择)
  static Future<DateTime?> pickDate({
    DateTime? initialDate,
    DateTime? firstDate,
    DateTime? lastDate,
    CalendarDatePicker2Config? config,
    double? dialogWidth,
    double? dialogHeight,
  }) async {
    final selectedDates = await _showDialog(
      initialDates: initialDate != null ? [initialDate] : null,
      config:
          config ?? _defaultConfig(firstDate: firstDate, lastDate: lastDate),
      dialogWidth: dialogWidth,
      dialogHeight: dialogHeight,
    );

    return selectedDates?.isNotEmpty == true ? selectedDates!.first : null;
  }

  /// 显示日期范围选择对话框
  /// [context]: 必传，构建上下文
  /// [initialDates]: 初始选中的日期范围(包含开始和结束日期)，可选
  /// [firstDate]: 可选择的最早日期，可选
  /// [lastDate]: 可选择的最晚日期，可选(默认当前日期)
  /// [dialogWidth]: 对话框宽度，可选(默认325)
  /// [dialogHeight]: 对话框高度，可选(默认400)
  /// 返回: 选择的日期范围(`List<DateTime>`)或null(未选择)
  static Future<List<DateTime>?> pickDateRange({
    List<DateTime>? initialDates,
    DateTime? firstDate,
    DateTime? lastDate,
    double? dialogWidth,
    double? dialogHeight,
  }) async {
    final config = CalendarDatePicker2Config(
      calendarType: CalendarDatePicker2Type.range,
      firstDate: firstDate,
      lastDate: lastDate ?? DateTime.now(),
      selectedDayHighlightColor: Colors.blue[800],
      controlsHeight: 40,
      controlsTextStyle: const TextStyle(color: Colors.blue),
      dayTextStyle: TextStyle(color: Colors.grey[700]),
      selectedDayTextStyle: const TextStyle(
        color: Colors.white,
        fontWeight: FontWeight.w700,
      ),
    );

    final selectedDates = await _showDialog(
      initialDates: initialDates,
      config: config,
      dialogWidth: dialogWidth,
      dialogHeight: dialogHeight,
    );

    return selectedDates?.whereType<DateTime>().toList();
  }

  /// 内部方法：显示日期选择对话框
  /// [context]: 构建上下文
  /// [initialDates]: 初始选中的日期列表
  /// [config]: 日期选择器配置
  /// [dialogWidth]: 对话框宽度
  /// [dialogHeight]: 对话框高度
  /// 返回: 选择的日期列表或null
  static Future<List<DateTime?>?> _showDialog({
    List<DateTime?>? initialDates,
    required CalendarDatePicker2Config config,
    double? dialogWidth,
    double? dialogHeight,
  }) async {
    List<DateTime?>? selectedDates;
    final isRange = config.calendarType == CalendarDatePicker2Type.range;

    await Get.dialog(
      AlertDialog(
        insetPadding: const EdgeInsets.symmetric(horizontal: 16), // 添加水平边距
        content: SizedBox(
          width: dialogWidth ?? 350, // 从400调整为350
          height: dialogHeight ?? 325,
          child: CalendarDatePicker2(
            config: config,
            value: initialDates ?? [DateTime.now()],
            onValueChanged: (dates) {
              selectedDates = dates;
              // 单日期选择时点击即关闭
              if (!isRange && dates.isNotEmpty) {
                Get.back(result: dates);
              }
              // 范围选择时只有选了两个日期才关闭
              if (isRange && dates.length == 2) {
                Get.back(result: dates);
              }
            },
          ),
        ),
      ),
      barrierDismissible: true,
    );

    return selectedDates;
  }

  /// 获取默认的日期选择器配置
  /// [firstDate]: 可选择的最早日期
  /// [lastDate]: 可选择的最晚日期
  /// 返回: 配置好的CalendarDatePicker2Config对象
  static CalendarDatePicker2Config _defaultConfig({
    DateTime? firstDate,
    DateTime? lastDate,
  }) {
    return CalendarDatePicker2Config(
      calendarType: CalendarDatePicker2Type.single,
      firstDate: firstDate,
      lastDate: lastDate ?? DateTime.now(),
      selectedDayHighlightColor: Colors.blue[800],
      controlsHeight: 40,
      controlsTextStyle: const TextStyle(color: Colors.blue),
      dayTextStyle: TextStyle(color: Colors.grey[700]),
      selectedDayTextStyle: const TextStyle(
        color: Colors.white,
        fontWeight: FontWeight.w700,
      ),
    );
  }

  /// 显示带标记的日期选择对话框
  /// [markedDates]: 需要标记的日期列表
  /// [markerColor]: 标记颜色（默认蓝色）
  /// [markerSize]: 标记大小（默认4.0）
  static Future<DateTime?> pickDateWithMarks({
    DateTime? initialDate,
    DateTime? firstDate,
    DateTime? lastDate,
    CalendarDatePicker2Config? config,
    double? dialogWidth,
    double? dialogHeight,
    required List<DateTime> markedDates,
    Color markerColor = Colors.blue,
    double markerSize = 4.0,
  }) async {
    final defaultConfig = _defaultConfig(
      firstDate: firstDate,
      lastDate: lastDate,
    );

    final mergedConfig = (config ?? defaultConfig).copyWith(
      dayBuilder: ({
        required DateTime date,
        TextStyle? textStyle,
        BoxDecoration? decoration,
        bool? isSelected,
        bool? isDisabled,
        bool? isToday,
      }) {
        final isMarked = markedDates.any(
          (d) =>
              d.year == date.year && d.month == date.month && d.day == date.day,
        );

        return Container(
          decoration: decoration,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('${date.day}', style: textStyle),
                  if (isMarked)
                    Container(
                      margin: const EdgeInsets.only(top: 2),
                      height: markerSize,
                      width: markerSize,
                      decoration: BoxDecoration(
                        color: markerColor,
                        shape: BoxShape.circle,
                      ),
                    ),
                ],
              ),
            ],
          ),
        );
      },
    );

    final selectedDates = await _showDialog(
      initialDates: initialDate != null ? [initialDate] : null,
      config: mergedConfig,
      dialogWidth: dialogWidth,
      dialogHeight: dialogHeight,
    );

    return selectedDates?.isNotEmpty == true ? selectedDates!.first : null;
  }
}

import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:permission_handler/permission_handler.dart';

var logger = Logger();

class PermissionUtils {
  /// 检查麦克风权限
  static Future<void> checkMicrophonePermission() async {
    await Future.delayed(const Duration(milliseconds: 500));
    PermissionStatus status = await Permission.microphone.status;
    logger.i('麦克风当前权限状态: $status');
    if (status.isGranted) {
      logger.i('麦克风权限已授予');
    } else {
      // 请求麦克风权限
      Map<Permission, PermissionStatus> statuses =
          await [Permission.microphone].request();
      logger.i('请求麦克风权限后状态: ${statuses[Permission.microphone]}');
      if (statuses[Permission.microphone]!.isGranted) {
        logger.i('麦克风权限已授予');
      } else if (statuses[Permission.microphone]!.isPermanentlyDenied) {
        logger.e('麦克风权限被永久拒绝，需要引导用户到设置页面');
        Get.snackbar(
          '提示',
          '请在设置中打开麦克风权限',
          duration: const Duration(milliseconds: 1500),
        );
        await Future.delayed(const Duration(milliseconds: 1500));
        openAppSettings();
      } else {
        logger.e('麦克风权限被拒绝');
      }
    }
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 消息工具类 - 统一管理所有消息提示
class MessageUtil {
  MessageUtil._();

  // 预定义颜色
  static const _successColor = Color(0xFF4CAF50);
  static const _errorColor = Color(0xFFF44336);
  static const _warningColor = Color(0xFFFF9800);
  static const _infoColor = Color(0xFF2196F3);
  static const _textColor = Colors.white;

  /// 显示成功消息
  static void success(String message) => _show('成功', message, _successColor, 2);

  /// 显示错误消息
  static void error(String message) => _show('错误', message, _errorColor, 3);

  /// 显示警告消息
  static void warning(String message) => _show('警告', message, _warningColor, 3);

  /// 显示信息消息
  static void info(String message) => _show('提示', message, _infoColor, 2);

  /// 统一的消息显示方法
  static void _show(String title, String message, Color color, int seconds) {
    Get.snackbar(
      title,
      message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: color,
      colorText: _textColor,
      duration: Duration(seconds: seconds),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
    );
  }

  /// 显示确认对话框
  static Future<bool> confirm({
    required String title,
    required String message,
    String confirmText = '确认',
    String cancelText = '取消',
  }) async {
    final result = await Get.dialog<bool>(
      AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text(cancelText),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            child: Text(confirmText),
          ),
        ],
      ),
    );
    return result ?? false;
  }
}

import 'package:event_bus/event_bus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';

import '../../app/modules/index/controllers/index_controller.dart';
import '../config/app_config.dart';
import '../services/http/http_error_event.dart';
import '../services/http/http_util.dart';
import '../services/jpush/push_service.dart';
import '../services/theme_service.dart';
import '../services/user_guide_service.dart';
import '../services/data_preloader_service.dart';
import '../utils/performance_monitor.dart';
import '../../app/data/services/sqflite/core/database_connection.dart';
import '../../app/data/services/sqflite/core/database_service.dart';
import '../../app/data/services/sqflite/dao/impl/diet_analysis_dao.dart';
import '../../app/data/services/sqflite/dao/impl/food_analysis_dao.dart';
import '../services/stt/speech_to_text.dart';

/// 全局日志实例
final logger = Logger();

/// 应用程序初始化器
/// 负责在应用启动时初始化所有必要的服务和资源
///
/// 初始化顺序：
/// 1. Flutter绑定初始化
/// 2. 应用配置初始化
/// 3. 核心服务初始化
/// 4. 控制器初始化
/// 5. 事件总线初始化
/// 6. 第三方服务初始化
class AppInitializer {
  // 私有构造函数，防止实例化
  AppInitializer._();

  /// 初始化应用程序
  ///
  /// 该方法会按顺序初始化应用程序的各个模块
  /// 如果任何模块初始化失败，会抛出异常并阻止应用启动
  static Future<void> initialize() async {
    try {
      // 开始性能监控
      PerformanceMonitor.startTimer('应用程序初始化');
      PerformanceMonitor.recordStartupMilestone('开始初始化');

      // 确保Flutter绑定已初始化
      WidgetsFlutterBinding.ensureInitialized();

      logger.i('🚀 开始初始化KcalFit应用程序...');

      // 按顺序初始化各个模块
      await _initializeConfig();
      await _initializeCoreServices();
      await _initializeControllers();
      await _initializeEventBus();
      await _initializeThirdPartyServices();

      // 结束性能监控
      PerformanceMonitor.endTimer('应用程序初始化');
      PerformanceMonitor.recordStartupMilestone('初始化完成');

      // 启动性能监控服务
      PerformanceMonitor.startMonitoring();

      logger.i('✅ 应用程序初始化完成');

      // 延迟分析启动性能，避免阻塞
      Future.delayed(const Duration(seconds: 2), () {
        PerformanceMonitor.analyzeStartupPerformance();
      });
    } catch (e, stackTrace) {
      PerformanceMonitor.endTimer('应用程序初始化');
      logger.e('❌ 应用程序初始化失败', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 初始化应用配置
  static Future<void> _initializeConfig() async {
    PerformanceMonitor.startTimer('应用配置初始化');
    logger.d('📋 初始化应用配置...');
    try {
      await AppConfig.init();
      PerformanceMonitor.endTimer('应用配置初始化');
      logger.d('✅ 应用配置初始化成功');
    } catch (e) {
      PerformanceMonitor.endTimer('应用配置初始化');
      logger.e('❌ 应用配置初始化失败: $e');
      rethrow;
    }
  }

  /// 初始化核心服务
  static Future<void> _initializeCoreServices() async {
    PerformanceMonitor.startTimer('核心服务初始化');
    logger.d('🔧 初始化核心服务...');

    try {
      // 注册全局服务
      PerformanceMonitor.startTimer('HTTP服务初始化');
      Get.put(HttpUtil(), permanent: true);
      PerformanceMonitor.endTimer('HTTP服务初始化');

      PerformanceMonitor.startTimer('语音识别服务初始化');
      Get.put(SpeechToTextUtil(), permanent: true);
      PerformanceMonitor.endTimer('语音识别服务初始化');

      // 初始化数据库
      await _initializeDatabase();

      // 注册主题服务（需要在数据库初始化之后）
      PerformanceMonitor.startTimer('主题服务初始化');
      logger.d('🎨 初始化主题服务...');
      final themeService = ThemeService();
      await themeService.onInit();
      Get.put(themeService, permanent: true);
      PerformanceMonitor.endTimer('主题服务初始化');
      logger.d('✅ 主题服务初始化成功');

      // 注册用户引导服务
      PerformanceMonitor.startTimer('用户引导服务初始化');
      logger.d('👋 初始化用户引导服务...');
      final userGuideService = UserGuideService();
      await userGuideService.onInit();
      Get.put(userGuideService, permanent: true);
      PerformanceMonitor.endTimer('用户引导服务初始化');
      logger.d('✅ 用户引导服务初始化成功');

      // 注册数据预加载服务（异步初始化，不阻塞启动）
      PerformanceMonitor.startTimer('数据预加载服务初始化');
      logger.d('📦 初始化数据预加载服务...');
      Get.put(DataPreloaderService(), permanent: true);
      PerformanceMonitor.endTimer('数据预加载服务初始化');
      logger.d('✅ 数据预加载服务初始化成功');

      PerformanceMonitor.endTimer('核心服务初始化');
      logger.d('✅ 核心服务初始化成功');
    } catch (e) {
      PerformanceMonitor.endTimer('核心服务初始化');
      logger.e('❌ 核心服务初始化失败: $e');
      rethrow;
    }
  }

  /// 初始化控制器
  static Future<void> _initializeControllers() async {
    logger.d('🎮 初始化控制器...');

    try {
      // 注册全局控制器 - 使用懒加载模式，避免启动时立即初始化
      Get.lazyPut<IndexController>(() => IndexController(), fenix: true);

      logger.d('✅ 控制器初始化成功');
    } catch (e) {
      logger.e('❌ 控制器初始化失败: $e');
      rethrow;
    }
  }

  /// 初始化事件总线
  static Future<void> _initializeEventBus() async {
    logger.d('📡 初始化事件总线...');

    try {
      // 注册事件总线
      Get.put(EventBus(), permanent: true);

      // 监听HTTP错误事件
      Get.find<EventBus>().on<HttpErrorEvent>().listen((event) {
        _handleHttpError(event);
      });

      logger.d('✅ 事件总线初始化成功');
    } catch (e) {
      logger.e('❌ 事件总线初始化失败: $e');
      rethrow;
    }
  }

  /// 初始化第三方服务
  static Future<void> _initializeThirdPartyServices() async {
    logger.d('🔌 初始化第三方服务...');

    // 初始化极光推送（非关键服务，失败不影响应用启动）
    try {
      await PushService.init();
      logger.d('✅ 推送服务初始化成功');
    } catch (e) {
      logger.w('⚠️ 推送服务初始化失败: $e');
      // 推送服务失败不影响应用启动
    }

    logger.d('✅ 第三方服务初始化完成');
  }

  /// 处理HTTP错误事件
  /// 当网络请求发生错误时，显示错误提示
  static void _handleHttpError(HttpErrorEvent event) {
    Get.snackbar(
      '网络错误',
      event.message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.red,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
    );
  }

  /// 初始化数据库
  static Future<void> _initializeDatabase() async {
    logger.d('💾 初始化数据库连接...');

    try {
      // 初始化数据库连接
      await Get.putAsync(() => DatabaseConnection().database);

      // 注册数据库服务
      Get.put(DatabaseService(), permanent: true);

      // 初始化DAO
      _initializeDataAccessObjects();

      logger.d('✅ 数据库初始化成功');
    } catch (e) {
      logger.e('❌ 数据库初始化失败: $e');
      // 数据库初始化失败不应该阻止应用启动，记录错误即可
      // 但是会影响数据持久化功能
    }
  }

  /// 初始化数据访问对象
  static void _initializeDataAccessObjects() {
    logger.d('🗃️ 初始化DAO...');

    try {
      // 注册DAO实例
      Get.put<DietAnalysisDao>(DietAnalysisDao(), permanent: true);
      Get.put<FoodAnalysisDao>(FoodAnalysisDao(), permanent: true);

      logger.d('✅ DAO初始化成功');
    } catch (e) {
      logger.e('❌ DAO初始化失败: $e');
    }
  }
}

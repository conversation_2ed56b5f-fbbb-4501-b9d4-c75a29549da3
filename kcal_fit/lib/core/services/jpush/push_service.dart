import 'dart:convert';

import 'package:get/get.dart';
import 'package:jpush_flutter/jpush_flutter.dart';
import 'package:logger/logger.dart';

import '../../../app/modules/index/controllers/index_controller.dart';
import '../../config/app_config.dart';
import '../../../app/data/models/analysis_response_push_msg.dart';
import '../../../app/data/services/hive/hive_service.dart';
import '../../../app/data/services/sqflite/dao/impl/diet_analysis_dao.dart';
import '../../../app/data/services/sqflite/dao/impl/food_analysis_dao.dart';
import 'nutrition_data_processor.dart';

/// 极光推送服务
///
/// 负责处理极光推送的初始化、消息接收和处理
class PushService {
  /// 极光推送实例
  static final JPush jpush = JPush();

  /// 饮食分析数据访问对象
  static final DietAnalysisDao dietAnalysisDao = DietAnalysisDao();

  /// 日志记录器
  static final _logger = Logger(printer: PrettyPrinter(methodCount: 3));

  /// 获取registrationID
  static Future<void> getRegistrationID() async {
    try {
      final rid = await jpush.getRegistrationID();
      HiveService.saveData("jpush", "registrationID", rid);
      _logger.i("获取registrationID成功: $rid");
    } catch (e) {
      _logger.e("获取registrationID失败: $e");
    }
  }

  /// 初始化极光推送
  ///
  /// 该方法会在应用程序启动时自动执行
  /// 可以通过调用 [PushService.init] 方法来初始化应用程序
  /// 注意：该方法会阻塞应用程序的启动，因此需要在异步方法中执行
  static Future<void> init() async {
    _logger.i("初始化极光推送");
    try {
      await _setupJPush();
      await getRegistrationID();
      _applyPushAuthority();
      _addEventHandlers();
    } catch (e) {
      _logger.e("极光推送初始化失败: $e");
    }
  }

  /// 设置极光推送配置
  static Future<void> _setupJPush() async {
    jpush.setup(
      appKey: AppConfig.jpushConfig['appKey'],
      channel: AppConfig.jpushConfig['channel'],
      production: AppConfig.jpushConfig['production'],
      debug: AppConfig.jpushConfig['debug'],
    );
  }

  /// 申请推送权限
  static void _applyPushAuthority() {
    // 必须在用户首次使用推送功能前调用
    jpush.applyPushAuthority(NotificationSettingsIOS(sound: true, alert: true, badge: true));
  }

  /// 添加事件处理器
  static void _addEventHandlers() {
    jpush.addEventHandler(
      // 1. 收到通知时触发（通知栏显示前）
      onReceiveNotification: _onReceiveNotification,
      // 2. 点击通知栏消息时触发
      onOpenNotification: _onOpenNotification,
      // 3. 收到自定义消息时触发（不显示在通知栏）
      onReceiveMessage: _handleCustomMessage,
      // 4. 通知权限状态变化时触发
      onReceiveNotificationAuthorization: _onReceiveNotificationAuthorization,
      // 5. JPush连接状态变化时触发
      onConnected: _onConnected,
      // 6. 通知消息未展示时触发
      onNotifyMessageUnShow: _onNotifyMessageUnShow,
      // 7. 应用内消息展示时触发
      onInAppMessageShow: _onInAppMessageShow,
      // 8. 应用内消息点击时触发
      onInAppMessageClick: _onInAppMessageClick,
      // 9. 命令执行结果回调
      onCommandResult: _onCommandResult,
    );
  }

  /// 收到通知时的处理
  /// 使用场景：记录收到的推送内容，不涉及UI操作
  static Future<void> _onReceiveNotification(Map<String, dynamic> message) async {
    _logger.i("收到推送: $message");
  }

  /// 点击通知栏消息时的处理
  /// 使用场景：用户点击通知后的跳转逻辑处理
  static Future<void> _onOpenNotification(Map<String, dynamic> message) async {
    _logger.i("点击推送: $message");
  }

  /// 通知权限状态变化时的处理
  /// 使用场景：跟踪用户通知权限变更
  static Future<void> _onReceiveNotificationAuthorization(Map<String, dynamic> message) async {
    _logger.i("通知权限状态变化: $message");
  }

  /// JPush连接状态变化时的处理
  /// 使用场景：监控推送服务连接状态
  static Future<void> _onConnected(Map<String, dynamic> message) async {
    _logger.i("JPush连接状态: $message");
  }

  /// 通知消息未展示时的处理
  /// 使用场景：分析通知到达率
  static Future<void> _onNotifyMessageUnShow(Map<String, dynamic> message) async {
    _logger.i("通知消息未展示: $message");
  }

  /// 应用内消息展示时的处理
  /// 使用场景：统计应用内消息曝光
  static Future<void> _onInAppMessageShow(Map<String, dynamic> message) async {
    _logger.i("内推消息展示: $message");
  }

  /// 应用内消息点击时的处理
  /// 使用场景：处理应用内消息点击行为
  static Future<void> _onInAppMessageClick(Map<String, dynamic> message) async {
    _logger.i("内推消息点击: $message");
  }

  /// 命令执行结果回调的处理
  /// 使用场景：处理JPush API调用结果
  static Future<void> _onCommandResult(Map<String, dynamic> message) async {
    _logger.i("命令执行结果: $message");
  }

  /// 处理自定义消息
  /// 使用场景：应用内消息处理，如即时通讯
  static Future<void> _handleCustomMessage(Map<String, dynamic> message) async {
    _logger.i("收到自定义消息: $message");
    Get.snackbar("提示", "饮食记录已更新", duration: Duration(seconds: 3));

    switch (message['content_type']) {
      case 'analysisData':
        await _handleAnalysisData(message);
        break;
      default:
        _logger.w("未知的消息类型: ${message['content_type']}");
    }
  }

  /// 处理饮食分析数据
  static Future<void> _handleAnalysisData(Map<String, dynamic> message) async {
    try {
      _logger.i("开始处理饮食分析数据");
      final analysis = AnalysisResponsePushMsg.fromJson(json.decode(json.encode(message)) as Map<String, dynamic>);

      _logger.i("分析数据解析成功，分析ID: ${analysis.title}");

      // 保存总分析记录
      await _saveTotalAnalysisRecord(analysis);
      _logger.i("总分析记录保存完成");

      // 保存食物记录
      await _saveFoodRecords(analysis);
      _logger.i("食物记录保存完成");

      // 刷新界面
      _logger.i("开始刷新UI界面");
      await _refreshUI();
      _logger.i("饮食分析数据处理完成");
    } catch (e) {
      _logger.e("处理饮食分析数据失败: $e");
    }
  }

  /// 保存总分析记录
  static Future<void> _saveTotalAnalysisRecord(AnalysisResponsePushMsg analysis) async {
    try {
      // 查询是否已存在该分析ID的记录
      final existingRecord = await dietAnalysisDao.findByAnalyzeId(analysis.title);

      if (existingRecord != null) {
        // 如果记录已存在，使用更新方式，保留原有的meal_type等字段
        _logger.i("找到已存在的记录，将进行更新操作");
        final existingMap = existingRecord.toMap();
        final totalRecord = NutritionDataProcessor.buildTotalAnalysisRecord(analysis, existingRecord: existingMap);
        _logger.i("更新分析记录: ${jsonEncode(totalRecord)}");

        await dietAnalysisDao.updateAnalysis(totalRecord);
        _logger.i("总分析记录更新成功: ${analysis.title}");
      } else {
        // 如果记录不存在，创建新记录
        _logger.i("未找到已存在的记录，将创建新记录");
        final totalRecord = NutritionDataProcessor.buildTotalAnalysisRecord(analysis);
        _logger.i("新增分析记录: ${jsonEncode(totalRecord)}");

        await dietAnalysisDao.addAnalysis(totalRecord);
        _logger.i("总分析记录保存成功: ${analysis.title}");
      }
    } catch (e) {
      _logger.e("保存总分析记录失败: $e");
      rethrow; // 重新抛出异常，阻止后续食物记录保存
    }
  }

  /// 保存食物记录
  static Future<void> _saveFoodRecords(AnalysisResponsePushMsg analysis) async {
    try {
      // 获取食物分析DAO实例
      final foodAnalysisDao = Get.find<FoodAnalysisDao>();

      // 先删除该分析ID下的所有旧食物记录
      final deletedCount = await foodAnalysisDao.deleteByAnalyzeId(analysis.title);
      _logger.i("删除了 $deletedCount 条旧的食物记录");

      // 添加新的食物记录
      for (var food in analysis.extras.foods) {
        try {
          final foodRecord = NutritionDataProcessor.buildFoodRecord(food, analysis.title);
          _logger.i("食物记录: ${food.name} - ${jsonEncode(foodRecord)}");

          await dietAnalysisDao.addFoodRecord(analysis.title, foodRecord);
          _logger.i("食物记录保存成功: ${food.name}");
        } catch (e) {
          _logger.e("保存食物记录失败: ${food.name} - $e");
          // 继续处理其他食物记录，不中断整个流程
        }
      }
    } catch (e) {
      _logger.e("保存食物记录过程失败: $e");
      rethrow;
    }
  }

  /// 刷新UI界面
  static Future<void> _refreshUI() async {
    try {
      // 等待数据保存完成
      await Future.delayed(Duration(milliseconds: 500));

      // 验证数据是否保存成功
      await _verifyDataSaved();

      final controller = Get.find<IndexController>();
      _logger.i("强制刷新今日数据");

      // 使用强制刷新方法，确保切换到今天并更新数据
      await controller.forceRefreshTodayData();

      _logger.i("UI刷新完成");
    } catch (e) {
      _logger.e("刷新UI失败: $e");
    }
  }

  /// 验证数据是否保存成功
  static Future<void> _verifyDataSaved() async {
    try {
      final today = DateTime.now().toString().split(' ')[0];
      final todayRecords = await dietAnalysisDao.getTodayAnalysis(today);
      _logger.i("验证数据保存 - 今天($today)的记录数量: ${todayRecords.length}");

      if (todayRecords.isNotEmpty) {
        final latestRecord = todayRecords.last;
        _logger.i("最新记录: 卡路里=${latestRecord.calories}, 蛋白质=${latestRecord.protein}");
      }
    } catch (e) {
      _logger.e("验证数据保存失败: $e");
    }
  }
}

/// 数据库字段常量定义
/// 
/// 用于定义各个数据库表支持的字段列表，便于维护和复用
class DatabaseFieldConstants {
  /// 饮食分析表支持的字段
  static const Set<String> dietAnalysisFields = {
    // 基础信息
    'analyze_id',
    'meal_time',
    'diet_description',
    'notes',
    'created_time',
    'update_time',
    'tags',
    'photo_path',
    
    // 宏量营养素
    'calories',
    'protein',
    'carbs',
    'fat',
    'fiber',
    'sugar',
    
    // 关键风险营养素
    'sodium',
    'cholesterol',
    
    // 维生素
    'vitamin_a',
    'vitamin_b1',
    'vitamin_b2',
    'vitamin_b3',
    'vitamin_b6',
    'vitamin_b12',
    'folate',
    'vitamin_c',
    'vitamin_d',
    'vitamin_e',
    'vitamin_k',
    
    // 矿物质
    'calcium',
    'iron',
    'magnesium',
    'phosphorus',
    'potassium',
    'zinc',
    'copper',
    'manganese',
    'selenium',
    'iodine',
    'chromium',
    'molybdenum',
    
    // 脂肪酸
    'omega3',
    'omega6',
    'trans_fat',
    'saturated_fat',
    'monounsaturated_fat',
    'polyunsaturated_fat',
  };

  /// 食物分析表支持的字段
  static const Set<String> foodAnalysisFields = {
    // 基础信息
    'id',
    'diet_analyze_id',
    'food_name',
    'quantity',
    
    // 宏量营养素
    'calories',
    'protein',
    'carbs',
    'fat',
    'fiber',
    'sugar',
    
    // 关键风险营养素
    'sodium',
    'cholesterol',
    
    // 维生素
    'vitamin_a',
    'vitamin_b1',
    'vitamin_b2',
    'vitamin_b3',
    'vitamin_b6',
    'vitamin_b12',
    'folate',
    'vitamin_c',
    'vitamin_d',
    'vitamin_e',
    'vitamin_k',
    
    // 矿物质
    'calcium',
    'iron',
    'magnesium',
    'phosphorus',
    'potassium',
    'zinc',
    'copper',
    'manganese',
    'selenium',
    'iodine',
    'chromium',
    'molybdenum',
    
    // 脂肪酸
    'omega3',
    'omega6',
    'trans_fat',
    'saturated_fat',
    'monounsaturated_fat',
    'polyunsaturated_fat',
  };

  /// 获取指定表的支持字段
  static Set<String> getSupportedFields(String tableName) {
    switch (tableName) {
      case 'diet_analysis':
        return dietAnalysisFields;
      case 'food_analysis':
        return foodAnalysisFields;
      default:
        return <String>{};
    }
  }

  /// 判断字段是否在指定表中存在
  static bool isFieldSupported(String fieldName, String tableName) {
    return getSupportedFields(tableName).contains(fieldName);
  }
}

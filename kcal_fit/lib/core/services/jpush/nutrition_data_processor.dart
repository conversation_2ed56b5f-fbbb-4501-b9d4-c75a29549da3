import 'package:logger/logger.dart';

import '../../constants/constants.dart';
import '../../../app/data/models/analysis_response_push_msg.dart';
import 'database_field_constants.dart';

/// 营养数据处理器
///
/// 负责处理营养素数据的转换、映射和验证
class NutritionDataProcessor {
  static final _logger = Logger(printer: PrettyPrinter(methodCount: 3));

  /// 处理宏量营养素数据
  ///
  /// [macronutrients] 宏量营养素数据
  /// 返回处理后的数据映射
  static Map<String, dynamic> processMacronutrients(Macronutrients macronutrients) {
    return {
      "calories": macronutrients.energy.value.toInt(),
      "protein": macronutrients.protein.value.toInt(),
      "fat": macronutrients.fat.value.toInt(),
      "carbs": macronutrients.carbohydrates.value.toInt(),
    };
  }

  /// 处理微量营养素数据
  ///
  /// [micronutrients] 微量营养素映射
  /// [tableName] 目标数据库表名
  /// [logPrefix] 日志前缀
  /// 返回处理后的数据映射
  static Map<String, dynamic> processMicronutrients(
    Map<String, Macronutrient> micronutrients,
    String tableName, {
    String logPrefix = "微量营养素",
  }) {
    final result = <String, dynamic>{};

    micronutrients.forEach((name, nutrient) {
      _logger.i("处理$logPrefix: $name -> ${nutrient.value}");

      // 获取数据库字段名
      final key = _mapNutrientNameToDbField(name);

      // 检查字段是否在数据库表中存在
      if (!DatabaseFieldConstants.isFieldSupported(key, tableName)) {
        _logger.i("跳过不支持的字段: $name -> $key ($tableName表中不存在)");
        return;
      }

      // 转换数值
      final value = _convertNutrientValue(nutrient.value);
      result[key] = value;

      _logger.i("$logPrefix映射: $name -> $key = $value");
    });

    return result;
  }

  /// 将营养素名称映射到数据库字段名
  ///
  /// [name] 营养素名称
  /// 返回对应的数据库字段名
  static String _mapNutrientNameToDbField(String name) {
    // 优先使用中文映射，如果没有则直接使用英文名
    String key = FoodMicronutrient.micronutrientMapping[name] ?? name;

    // 如果还是没有找到合适的映射，使用标准化的英文名称
    if (key == name && !key.contains('_')) {
      key = name.toLowerCase().replaceAll(' ', '_');
    }

    return key;
  }

  /// 转换营养素数值
  ///
  /// [value] 原始数值
  /// 返回转换后的整数值
  static int _convertNutrientValue(double value) {
    // 保存数值，保留小数精度后转换为整数（乘以适当倍数）
    if (value < 1) {
      // 对于小于1的值，乘以1000保存（如0.1mg -> 100）
      return (value * 1000).round();
    } else {
      return value.round();
    }
  }

  /// 构建总分析记录
  ///
  /// [analysis] 分析数据
  /// [existingRecord] 已存在的记录（可选，用于保留meal_type等字段）
  /// 返回构建好的总分析记录
  static Map<String, dynamic> buildTotalAnalysisRecord(
    AnalysisResponsePushMsg analysis, {
    Map<String, dynamic>? existingRecord,
  }) {
    final now = DateTime.now();
    final record = <String, dynamic>{
      "analyze_id": analysis.title,
      "diet_description": analysis.extras.content.isNotEmpty ? analysis.extras.content : "饮食分析记录",
      "notes": analysis.extras.content,
      "update_time": now.toIso8601String(),
    };

    // 如果有已存在的记录，保留其中的关键字段
    if (existingRecord != null) {
      // 保留原有的meal_time和meal_type
      if (existingRecord['meal_time'] != null) {
        record['meal_time'] = existingRecord['meal_time'];
      }
      if (existingRecord['meal_type'] != null) {
        record['meal_type'] = existingRecord['meal_type'];
      }
      if (existingRecord['created_time'] != null) {
        record['created_time'] = existingRecord['created_time'];
      }
      _logger.i("保留已存在记录的字段 - meal_type: ${existingRecord['meal_type']}, meal_time: ${existingRecord['meal_time']}");
    } else {
      // 如果没有已存在的记录，使用当前时间
      record['meal_time'] = now.toIso8601String();
      record['created_time'] = now.toIso8601String();
    }

    _logger.i("构建总分析记录 - meal_time: ${record['meal_time']}, meal_type: ${record['meal_type']}");

    // 添加宏量营养素总计（如果存在）
    if (analysis.extras.macronutrientTotals != null) {
      record.addAll(processMacronutrients(analysis.extras.macronutrientTotals!));
    }

    // 添加微量营养素总计（如果存在）
    if (analysis.extras.micronutrientTotals != null) {
      record.addAll(processMicronutrients(analysis.extras.micronutrientTotals!, 'diet_analysis', logPrefix: "总计微量营养素"));
    }

    return record;
  }

  /// 构建食物记录
  ///
  /// [food] 食物数据
  /// [analyzeId] 分析ID
  /// 返回构建好的食物记录
  static Map<String, dynamic> buildFoodRecord(Food food, String analyzeId) {
    // 构建基础食物记录
    final record = <String, dynamic>{
      "diet_analyze_id": analyzeId,
      "food_name": food.name,
      "quantity": food.quantity.value.toInt(),
    };

    // 添加宏量营养素
    record.addAll(processMacronutrients(food.macronutrients));

    // 添加微量营养素
    final micronutrients = {
      for (var nutrient in food.micronutrients)
        nutrient.name: Macronutrient(value: nutrient.value, unit: nutrient.unit),
    };

    record.addAll(processMicronutrients(micronutrients, 'food_analysis', logPrefix: "食物微量营养素"));

    return record;
  }
}

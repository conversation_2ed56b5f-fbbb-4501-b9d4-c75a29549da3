import 'package:logger/logger.dart';

import '../../../app/data/models/analysis_data.dart';
import '../../../app/data/services/hive/hive_service.dart';
// import '../sqflite/diet_analysis_dao.dart';
import 'http_util.dart';

var logger = Logger();

class HttpApi {
  static final HttpUtil _httpUtil = HttpUtil();
  // static final dietAnalysisDao = Get.find<DietAnalysisDao>();

  /// 提交饮食信息
  static Future<dynamic> submitDietInfo(Map<String, dynamic> data) async {
    // 获取极光推送的注册ID
    final registrationID = await HiveService.getData("jpush", "registrationID");
    // 提交到服务器
    final result = await _httpUtil.post(
      '/api/dietInfo/submit',
      data: {
        'description': data['diet_description'],
        'analyzeId': data['analyze_id'],
        "registrationID": registrationID,
      },
    );
    return result;
  }

  /// 根据analyzeId获取饮食信息
  static Future<AnalysisData> getDietInfoByanalyzeId(String analyzeId) async {
    var res = await _httpUtil.get('/api/dietInfo/getAnalysisData', queryParameters: {'analyzeId': analyzeId});
    logger.i(res.data);
    return AnalysisData.fromJson(res.data);
  }

  /// 提交意见反馈
  static Future<dynamic> submitFeedback({
    required String content,
    String? contact,
    String? deviceInfo,
    String? appVersion,
  }) async {
    final result = await _httpUtil.post(
      '/api/app/submitFeedback',
      data: {
        'content': content,
        if (contact != null) 'contact': contact,
        if (deviceInfo != null) 'deviceInfo': deviceInfo,
        if (appVersion != null) 'appVersion': appVersion,
      },
    );
    return result;
  }
}

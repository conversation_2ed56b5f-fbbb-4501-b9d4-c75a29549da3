import 'package:dio/dio.dart' hide Response; // 排除Dio的Response类
import 'package:get/get.dart'; // 保留GetX的导入
import 'package:dio/dio.dart' as dio;
import 'package:logger/logger.dart';
import 'dart:async';
import 'package:event_bus/event_bus.dart';
import '../../config/app_config.dart';
import 'http_error_event.dart';

class HttpUtil {
  final baseUrl = AppConfig.apiConfig['baseUrl']; // 基础URL
  static final HttpUtil _instance = HttpUtil._internal(); // 私有构造函数
  late Dio _dio; // 声明_dio属性
  final Logger _logger = Logger(); // 日志记录器
  bool _isRefreshing = false; // 是否正在刷新Token
  final List<Function()> _requestsWaitingForRefresh = []; // 等待刷新的请求队列
  factory HttpUtil() => _instance; // 工厂方法，返回单例实例

  HttpUtil._internal() {
    logger.i("初始化HttpUtil");
    _dio = Dio(
      BaseOptions(
        baseUrl: baseUrl,
        connectTimeout: AppConfig.apiConfig['timeOut'], // 连接超时时间
        receiveTimeout: AppConfig.apiConfig['timeOut'], // 接收超时时间
        sendTimeout: AppConfig.apiConfig['timeOut'], // 发送超时时间
        headers: {'Content-Type': 'application/json; charset=utf-8'},
      ),
    );

    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          // 添加Token自动刷新逻辑
          if (_shouldRefreshToken(options)) {
            if (_isRefreshing) {
              return _addToWaitingQueue(options, handler);
            }
            _isRefreshing = true;
            try {
              await _refreshToken();
              _isRefreshing = false;
              _retryAllWaitingRequests();
            } catch (e) {
              _isRefreshing = false;
              _clearWaitingQueue();
              return handler.reject(
                DioException(
                  requestOptions: options,
                  error: 'Token refresh failed',
                ),
              );
            }
          }
          return handler.next(options);
        },
        onError: (DioException e, handler) async {
          // 全局错误处理
          _logger.e('Error: ${e.type} ${e.requestOptions.uri}');

          // 请求重试机制
          if (_shouldRetry(e)) {
            await Future.delayed(const Duration(seconds: 1));
            return handler.resolve(await _retryRequest(e.requestOptions));
          }

          // 统一错误处理
          return handler.next(_handleGlobalError(e));
        },
      ),
    );
  }

  /// Token自动刷新条件判断
  bool _shouldRefreshToken(RequestOptions options) {
    // 实现Token刷新条件判断逻辑
    return false; // 根据实际业务逻辑修改
  }

  /// Token刷新
  Future<void> _refreshToken() async {
    // 实现Token刷新逻辑
  }

  /// 请求重试条件判断
  bool _shouldRetry(DioException e) {
    // 实现重试条件判断
    return e.type == DioExceptionType.connectionTimeout ||
        e.type == DioExceptionType.receiveTimeout;
  }

  /// 请求重试
  Future<dio.Response> _retryRequest(RequestOptions options) async {
    // 实现请求重试逻辑
    return _dio.request(
      options.path,
      data: options.data,
      queryParameters: options.queryParameters,
      options: Options(method: options.method, headers: options.headers),
    );
  }

  /// 统一错误处理
  DioException _handleGlobalError(DioException e) {
    // 实现全局错误处理逻辑
    // 通过事件总线发送错误通知
    Get.find<EventBus>().fire(
      HttpErrorEvent(
        errorType: e.type,
        uri: e.requestOptions.uri,
        message: '请求失败: ${_getErrorMessage(e)}',
      ),
    );
    return e;
  }

  /// 添加到等待队列
  void _addToWaitingQueue(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) {
    _requestsWaitingForRefresh.add(() async {
      try {
        handler.resolve(
          await _dio.request(
            options.path,
            data: options.data,
            queryParameters: options.queryParameters,
            options: Options(method: options.method, headers: options.headers),
          ),
        );
      } catch (e) {
        handler.reject(e as DioException);
      }
    });
  }

  /// 重试所有等待中的请求
  void _retryAllWaitingRequests() {
    for (var request in _requestsWaitingForRefresh) {
      request();
    }
    _clearWaitingQueue();
  }

  /// 清空等待队列
  void _clearWaitingQueue() {
    _requestsWaitingForRefresh.clear();
  }

  /// 流式GET请求
  Future<dio.Response> getStream(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    required void Function(List<int>)? onData,
    void Function(Object)? onError,
    void Function()? onDone,
  }) async {
    final response = await _dio.get(
      path,
      queryParameters: queryParameters,
      options: options ?? Options(responseType: ResponseType.stream),
      cancelToken: cancelToken,
    );

    if (response.data is ResponseBody) {
      final stream = response.data.stream;
      stream.listen(onData, onError: onError, onDone: onDone);
    }

    return response;
  }

  /// 流式POST请求
  /// response.subscription.cancel(); // 取消订阅
  /// response.subscription.pause(); // 暂停订阅
  /// response.subscription.resume(); // 恢复订阅
  /// response.subscription.isPaused; // 检查是否暂停订阅
  /// response.subscription.isClosed; // 检查是否已关闭订阅
  Future<dio.Response> postStream(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    required void Function(List<int>)? onData,
    void Function(Object)? onError,
    void Function()? onDone,
  }) async {
    try {
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: (options ?? Options(responseType: ResponseType.stream))
            .copyWith(
              validateStatus: (status) => status != null && status < 500,
              headers: {
                'Accept': 'text/event-stream', // 明确指定SSE协议
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
              },
            ),
        cancelToken: cancelToken,
      );
      if (response.data is! ResponseBody) {
        _logger.w('非流式响应: ${response.data?.runtimeType}');
        throw DioException(
          requestOptions: response.requestOptions,
          error: 'Expected stream but got ${response.data?.runtimeType}',
          type: DioExceptionType.badResponse,
          response: response,
        );
      }

      final stream = response.data.stream;
      final subscription = stream.listen(
        onData,
        onError: (error) {
          _logger.e('流传输错误: ${error.toString()}');
          onError?.call(
            error is DioException
                ? error
                : DioException(
                  requestOptions: response.requestOptions,
                  error: error.toString(),
                ),
          );
        },
        onDone: () {
          onDone?.call();
        },
        cancelOnError: true,
      );

      // Return custom response with subscription
      return StreamResponse(
        data: response.data,
        requestOptions: response.requestOptions,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        isRedirect: response.isRedirect,
        redirects: response.redirects,
        extra: response.extra,
        headers: response.headers,
        subscription: subscription,
      );
    } on DioException catch (e) {
      _logger.e('POST Stream请求失败[${e.type}] ${e.message}');
      if (e.response != null) {
        _logger.e('响应详情: ${e.response?.data}');
      }
      rethrow;
    } catch (e, stack) {
      _logger.e('未知错误: $e\n$stack');
      rethrow;
    }
  }

  /// 基础GET请求
  Future<dio.Response> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _dio.get(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      _logger.i('GET请求成功: ${response.statusCode} $path');
      return response;
    } catch (e) {
      _logger.e('GET请求失败: $path - $e');
      rethrow;
    }
  }

  /// 基础POST请求
  // httpUtil.post("/api/chat", data: requestData).then((response) {
  //   print(response);
  // })
  Future<dio.Response> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      return response;
    } catch (e) {
      _logger.e('POST请求失败: $path - $e');
      if (e is DioException) {
        return dio.Response(
          requestOptions: RequestOptions(path: path),
          statusCode: e.response?.statusCode,
          statusMessage: e.message,
          data: e.response?.data,
        );
      }
      rethrow;
    }
  }

  // 根据 DioException 的类型返回对应的错误信息。
  String _getErrorMessage(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return '连接超时';
      case DioExceptionType.receiveTimeout:
        return '接收超时';
      case DioExceptionType.sendTimeout:
        return '发送超时';
      case DioExceptionType.connectionError:
        return '网络连接错误';
      default:
        return '网络请求失败';
    }
  }
}

class StreamResponse<T> extends dio.Response<T> {
  final StreamSubscription<List<int>>? subscription;

  StreamResponse({
    required super.data,
    required super.requestOptions,
    super.statusCode,
    super.statusMessage,
    super.isRedirect,
    super.redirects,
    super.extra,
    super.headers,
    this.subscription,
  });
}

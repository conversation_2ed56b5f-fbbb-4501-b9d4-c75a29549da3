enum HttpErrorType {
  connectionTimeout, // 连接超时
  sendTimeout, // 发送超时
  receiveTimeout, // 接收超时
  badCertificate, // 证书错误
  badResponse, // 响应错误
  cancel, // 取消
  connectionError, // 连接错误
  unknown, // 未知错误
}

class HttpError {
  final HttpErrorType type;
  final String message;
  final int? statusCode;

  HttpError({required this.type, required this.message, this.statusCode});

  @override
  String toString() =>
      'HttpError(type: $type, message: $message, statusCode: $statusCode)';
}

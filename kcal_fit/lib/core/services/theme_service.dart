import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../theme/app_theme.dart';
import '../utils/logger_util.dart';
import '../../app/data/services/hive/hive_service.dart';

/// 主题管理服务
/// 负责管理应用的主题状态和持久化存储
class ThemeService extends GetxService {
  static const String _themeBoxName = 'theme_settings';
  static const String _themeTypeKey = 'theme_type';
  static const String _appBarStyleKey = 'app_bar_style';

  /// 当前主题类型
  final Rx<ThemeType> _currentThemeType = ThemeType.blue.obs;

  /// 当前AppBar样式
  final Rx<AppBarStyle> _currentAppBarStyle = AppBarStyle.colored.obs;

  /// 获取当前主题类型
  ThemeType get currentThemeType => _currentThemeType.value;

  /// 获取当前AppBar样式
  AppBarStyle get currentAppBarStyle => _currentAppBarStyle.value;

  /// 主题类型变化流
  Rx<ThemeType> get themeTypeStream => _currentThemeType;

  /// AppBar样式变化流
  Rx<AppBarStyle> get appBarStyleStream => _currentAppBarStyle;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _loadThemeSettings();
  }

  /// 加载主题设置
  Future<void> _loadThemeSettings() async {
    try {
      // 加载主题类型
      final savedThemeType = await HiveService.getData<String>(_themeBoxName, _themeTypeKey);
      if (savedThemeType != null) {
        final themeType = _parseThemeType(savedThemeType);
        if (themeType != null) {
          _currentThemeType.value = themeType;
        }
      }

      // 加载AppBar样式
      final savedAppBarStyle = await HiveService.getData<String>(_themeBoxName, _appBarStyleKey);
      if (savedAppBarStyle != null) {
        final appBarStyle = _parseAppBarStyle(savedAppBarStyle);
        if (appBarStyle != null) {
          _currentAppBarStyle.value = appBarStyle;
        }
      }
    } catch (e) {
      LoggerUtil.e('加载主题设置失败', e);
    }
  }

  /// 保存主题设置
  Future<void> _saveThemeSettings() async {
    try {
      await HiveService.saveData(_themeBoxName, _themeTypeKey, _currentThemeType.value.name);
      await HiveService.saveData(_themeBoxName, _appBarStyleKey, _currentAppBarStyle.value.name);
    } catch (e) {
      LoggerUtil.e('保存主题设置失败', e);
    }
  }

  /// 切换主题类型
  Future<void> changeThemeType(ThemeType themeType) async {
    if (_currentThemeType.value != themeType) {
      _currentThemeType.value = themeType;
      await _saveThemeSettings();
      _updateAppTheme();
    }
  }

  /// 切换AppBar样式
  Future<void> changeAppBarStyle(AppBarStyle appBarStyle) async {
    if (_currentAppBarStyle.value != appBarStyle) {
      _currentAppBarStyle.value = appBarStyle;
      await _saveThemeSettings();
      _updateAppTheme();
    }
  }

  /// 同时切换主题类型和AppBar样式
  Future<void> changeTheme({ThemeType? themeType, AppBarStyle? appBarStyle}) async {
    bool changed = false;

    if (themeType != null && _currentThemeType.value != themeType) {
      _currentThemeType.value = themeType;
      changed = true;
    }

    if (appBarStyle != null && _currentAppBarStyle.value != appBarStyle) {
      _currentAppBarStyle.value = appBarStyle;
      changed = true;
    }

    if (changed) {
      await _saveThemeSettings();
      _updateAppTheme();
    }
  }

  /// 重置为默认主题
  Future<void> resetToDefault() async {
    await changeTheme(themeType: ThemeType.blue, appBarStyle: AppBarStyle.colored);
  }

  /// 获取当前主题数据
  ThemeData getCurrentTheme() {
    return AppTheme.getTheme(themeType: _currentThemeType.value, appBarStyle: _currentAppBarStyle.value);
  }

  /// 更新应用主题
  void _updateAppTheme() {
    final newTheme = getCurrentTheme();
    Get.changeTheme(newTheme);
  }

  /// 解析主题类型字符串
  ThemeType? _parseThemeType(String themeTypeString) {
    try {
      return ThemeType.values.firstWhere((e) => e.name == themeTypeString);
    } catch (e) {
      return null;
    }
  }

  /// 解析AppBar样式字符串
  AppBarStyle? _parseAppBarStyle(String appBarStyleString) {
    try {
      return AppBarStyle.values.firstWhere((e) => e.name == appBarStyleString);
    } catch (e) {
      return null;
    }
  }

  /// 获取所有可用的主题类型
  List<ThemeType> get availableThemeTypes => ThemeType.values;

  /// 获取所有可用的AppBar样式
  List<AppBarStyle> get availableAppBarStyles => AppBarStyle.values;

  /// 获取主题类型的显示名称
  String getThemeTypeName(ThemeType themeType) {
    return AppTheme.getThemeTypeName(themeType);
  }

  /// 获取AppBar样式的显示名称
  String getAppBarStyleName(AppBarStyle appBarStyle) {
    return AppTheme.getAppBarStyleName(appBarStyle);
  }

  /// 获取主题类型的主色调
  Color getThemeTypeColor(ThemeType themeType) {
    return AppTheme.getPrimaryColor(themeType);
  }
}

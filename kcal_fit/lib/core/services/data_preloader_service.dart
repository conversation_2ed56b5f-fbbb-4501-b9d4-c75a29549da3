import 'package:get/get.dart';

import '../../core/utils/logger_util.dart';
import '../../app/data/models/diet_analysis.dart';
import '../../app/data/services/sqflite/dao/impl/diet_analysis_dao.dart';
import '../../app/data/services/sqflite/dao/impl/body_data_dao.dart';
import '../../app/data/services/hive/hive_service.dart';

/// 数据预加载服务
/// 在应用启动时预加载常用数据，提高用户体验
class DataPreloaderService extends GetxService {
  /// 饮食分析数据访问对象
  final DietAnalysisDao _dietAnalysisDao = DietAnalysisDao();

  /// 身体数据访问对象
  final BodyDataDao _bodyDataDao = BodyDataDao();

  /// 预加载的数据缓存
  final Map<String, dynamic> _preloadedData = {};

  /// 数据预加载状态
  final RxBool _isPreloading = false.obs;
  final RxBool _isPreloaded = false.obs;

  // Getter方法
  bool get isPreloading => _isPreloading.value;
  bool get isPreloaded => _isPreloaded.value;

  @override
  Future<void> onInit() async {
    super.onInit();
    LoggerUtil.d('数据预加载服务初始化');

    // 延迟启动预加载，避免阻塞应用启动
    Future.delayed(const Duration(milliseconds: 500), () {
      startPreloading();
    });
  }

  /// 开始数据预加载
  Future<void> startPreloading() async {
    if (_isPreloading.value || _isPreloaded.value) {
      return;
    }

    _isPreloading.value = true;
    LoggerUtil.d('开始数据预加载');

    try {
      // 并行预加载多种数据
      await Future.wait([_preloadTodayAnalysis(), _preloadUserData(), _preloadBodyData(), _preloadStatisticsData()]);

      _isPreloaded.value = true;
      LoggerUtil.i('数据预加载完成');
    } catch (e) {
      LoggerUtil.e('数据预加载失败', e);
    } finally {
      _isPreloading.value = false;
    }
  }

  /// 预加载今日饮食分析数据
  Future<void> _preloadTodayAnalysis() async {
    try {
      final today = DateTime.now().toString().split(" ")[0];
      final analysisList = await _dietAnalysisDao.getTodayAnalysis(today);
      _preloadedData['todayAnalysis'] = analysisList;
      _preloadedData['todayAnalysisDate'] = today;
      LoggerUtil.d('预加载今日饮食分析数据: ${analysisList.length} 条记录');
    } catch (e) {
      LoggerUtil.e('预加载今日饮食分析数据失败', e);
    }
  }

  /// 预加载用户数据
  Future<void> _preloadUserData() async {
    try {
      final userData = await HiveService.getAllData("user");
      _preloadedData['userData'] = userData;
      LoggerUtil.d('预加载用户数据完成');
    } catch (e) {
      LoggerUtil.e('预加载用户数据失败', e);
    }
  }

  /// 预加载身体数据
  Future<void> _preloadBodyData() async {
    try {
      final bodyData = await _bodyDataDao.getLatestBodyData();
      _preloadedData['bodyData'] = bodyData;
      LoggerUtil.d('预加载身体数据完成');
    } catch (e) {
      LoggerUtil.e('预加载身体数据失败', e);
    }
  }

  /// 预加载统计数据
  Future<void> _preloadStatisticsData() async {
    try {
      final results = await Future.wait([
        _dietAnalysisDao.getRecordDaysCount(),
        _dietAnalysisDao.getContinuousCheckInDays(),
        _bodyDataDao.getWeightChangeAmount(),
      ]);

      _preloadedData['statisticsData'] = {
        'recordDays': results[0],
        'continuousCheckIn': results[1],
        'weightChange': results[2],
      };
      LoggerUtil.d('预加载统计数据完成');
    } catch (e) {
      LoggerUtil.e('预加载统计数据失败', e);
    }
  }

  /// 获取预加载的数据
  /// [key] 数据键名
  T? getPreloadedData<T>(String key) {
    if (!_isPreloaded.value) {
      return null;
    }
    return _preloadedData[key] as T?;
  }

  /// 检查是否有预加载的数据
  /// [key] 数据键名
  bool hasPreloadedData(String key) {
    return _isPreloaded.value && _preloadedData.containsKey(key);
  }

  /// 清除预加载的数据
  void clearPreloadedData() {
    _preloadedData.clear();
    _isPreloaded.value = false;
    LoggerUtil.d('预加载数据已清除');
  }

  /// 刷新预加载数据
  Future<void> refreshPreloadedData() async {
    clearPreloadedData();
    await startPreloading();
  }

  /// 获取今日饮食分析数据（优先使用预加载数据）
  Future<List<DietAnalysis>> getTodayAnalysisOptimized(String date) async {
    // 检查是否有预加载的数据且日期匹配
    if (hasPreloadedData('todayAnalysis') && getPreloadedData<String>('todayAnalysisDate') == date) {
      LoggerUtil.d('使用预加载的今日饮食分析数据');
      final cachedData = getPreloadedData<List<dynamic>>('todayAnalysis') ?? [];
      return cachedData.map((item) => item as DietAnalysis).toList();
    }

    // 如果没有预加载数据或日期不匹配，从数据库查询
    LoggerUtil.d('从数据库查询今日饮食分析数据');
    return await _dietAnalysisDao.getTodayAnalysis(date);
  }
}

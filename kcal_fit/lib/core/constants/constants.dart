import 'package:flutter/widgets.dart';

/// 全局布局常量

/// 用于统一管理项目中的布局常量，如边距、间距、圆角等。
class LayoutConstants {
  // 边距
  static const defaultPadding = EdgeInsets.all(16);
  static const horizontalPadding = EdgeInsets.symmetric(horizontal: 16);
  static const userSectionPadding = EdgeInsets.fromLTRB(16, 24, 16, 16);
  static const quickActionsPadding = EdgeInsets.symmetric(
    horizontal: 16,
    vertical: 8,
  );

  // 间距
  static const spacingSmall = SizedBox(height: 4);
  static const spacingMedium = SizedBox(height: 12);
  static const spacingLarge = SizedBox(height: 16);
  static const spacingExtraLarge = SizedBox(height: 24);

  // 圆角
  static const borderRadiusSmall = 4.0;
  static const borderRadiusMedium = 8.0;
  static const borderRadiusLarge = 12.0;
}

// 元素常量
class FoodMicronutrient {
  // 常量定义 - 按照国际营养学标准命名
  static const Map<String, String> micronutrientMapping = {
    // 基础营养成分
    '能量': 'calories', // 能量/卡路里(千卡)
    '蛋白质': 'protein', // 蛋白质(克)
    '碳水化合物': 'carbs', // 碳水化合物(克)
    '脂肪': 'fat', // 总脂肪(克)
    '膳食纤维': 'fiber', // 膳食纤维(克)
    '糖分': 'sugar', // 糖分(克)
    '钠': 'sodium', // 钠(毫克)
    '胆固醇': 'cholesterol', // 胆固醇(毫克)
    // 维生素类
    '维生素A': 'vitamin_a', // 维生素A(微克视黄醇当量)
    '维生素B1': 'vitamin_b1', // 维生素B1/硫胺素(毫克)
    '维生素B2': 'vitamin_b2', // 维生素B2/核黄素(毫克)
    '维生素B3': 'vitamin_b3', // 维生素B3/烟酸(毫克)
    '维生素B6': 'vitamin_b6', // 维生素B6/吡哆醇(毫克)
    '维生素B12': 'vitamin_b12', // 维生素B12/钴胺素(微克)
    '叶酸': 'folate', // 叶酸(微克)
    '维生素C': 'vitamin_c', // 维生素C(毫克)
    '维生素D': 'vitamin_d', // 维生素D(微克)
    '维生素E': 'vitamin_e', // 维生素E(毫克α-生育酚当量)
    '维生素K': 'vitamin_k', // 维生素K(微克)
    // 矿物质类
    '钙': 'calcium', // 钙(毫克)
    '铁': 'iron', // 铁(毫克)
    '镁': 'magnesium', // 镁(毫克)
    '磷': 'phosphorus', // 磷(毫克)
    '钾': 'potassium', // 钾(毫克)
    '锌': 'zinc', // 锌(毫克)
    '铜': 'copper', // 铜(毫克)
    '锰': 'manganese', // 锰(毫克)
    '硒': 'selenium', // 硒(微克)
    '碘': 'iodine', // 碘(微克)
    '铬': 'chromium', // 铬(微克)
    '钼': 'molybdenum', // 钼(微克)
    // 脂肪酸类
    'ω-3脂肪酸': 'omega3', // ω-3脂肪酸(克)
    'ω-6脂肪酸': 'omega6', // ω-6脂肪酸(克)
    '反式脂肪': 'trans_fat', // 反式脂肪(克)
    '饱和脂肪酸': 'saturated_fat', // 饱和脂肪酸(克)
    '不饱和脂肪酸': 'unsaturated_fat', // 不饱和脂肪酸(克)
  };

  // 获取所有营养素分类
  static Map<String, List<String>> get categories {
    return {
      '基础营养': ['能量', '蛋白质', '碳水化合物', '脂肪', '膳食纤维', '糖分', '钠', '胆固醇'],
      '维生素': [
        '维生素A',
        '维生素B1',
        '维生素B2',
        '维生素B3',
        '维生素B6',
        '维生素B12',
        '叶酸',
        '维生素C',
        '维生素D',
        '维生素E',
        '维生素K',
      ],
      '矿物质': ['钙', '铁', '镁', '磷', '钾', '锌', '铜', '锰', '硒', '碘', '铬', '钼'],
      '脂肪酸': ['ω-3脂肪酸', 'ω-6脂肪酸', '反式脂肪', '饱和脂肪酸', '不饱和脂肪酸'],
    };
  }

  // 获取所有营养素中文名称
  static List<String> get allChineseNames => micronutrientMapping.keys.toList();

  // 获取所有数据库字段名
  static List<String> get allFieldNames => micronutrientMapping.values.toList();

  // 根据中文名获取数据库字段名
  static String? getFieldName(String chineseName) {
    return micronutrientMapping[chineseName];
  }

  // 根据数据库字段名获取中文名
  static String? getChineseName(String fieldName) {
    return micronutrientMapping.entries
        .firstWhere(
          (entry) => entry.value == fieldName,
          orElse: () => MapEntry('', ''),
        )
        .key;
  }
}

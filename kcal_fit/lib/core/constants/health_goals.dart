/// 健康目标常量
/// 统一管理应用中的健康目标选项，确保各页面数据一致性
class HealthGoals {
  /// 健康目标选项列表
  /// 用于个人信息页面的健康目标选择和目标设置页面的目标类型选择
  static const List<String> options = [
    '减脂',
    '增肌', 
    '塑型',
    '养生',
  ];
  
  /// 默认健康目标
  static const String defaultGoal = '减脂';
  
  /// 健康目标优先级映射
  /// 用于从多个健康目标中选择主要目标时的优先级判断
  static const Map<String, int> priority = {
    '减脂': 1,
    '增肌': 2,
    '塑型': 3,
    '养生': 4,
  };
  
  /// 根据优先级从多个目标中选择主要目标
  /// [goals] 目标列表
  /// 返回优先级最高的目标，如果列表为空则返回默认目标
  static String getPrimaryGoal(List<String> goals) {
    if (goals.isEmpty) return defaultGoal;
    
    // 过滤出有效的目标
    final validGoals = goals.where((goal) => options.contains(goal)).toList();
    if (validGoals.isEmpty) return defaultGoal;
    
    // 按优先级排序，返回优先级最高的目标
    validGoals.sort((a, b) => (priority[a] ?? 999).compareTo(priority[b] ?? 999));
    return validGoals.first;
  }
  
  /// 验证目标是否有效
  /// [goal] 要验证的目标
  /// 返回目标是否在有效选项列表中
  static bool isValidGoal(String goal) {
    return options.contains(goal);
  }
}

import 'package:flutter/material.dart';

/// 主题类型枚举
enum ThemeType {
  /// 蓝色主题
  blue,

  /// 绿色主题
  green,

  /// 紫色主题
  purple,

  /// 橙色主题
  orange,

  /// 红色主题
  red,
}

/// AppBar样式类型枚举
enum AppBarStyle {
  /// 彩色AppBar（使用主题色）
  colored,

  /// 白色AppBar
  white,

  /// 透明AppBar
  transparent,
}

/// 应用主题配置类
/// 定义应用的颜色、字体、间距等视觉规范
class AppTheme {
  // 私有构造函数，防止实例化
  AppTheme._();

  // ==================== 颜色定义 ====================

  /// 蓝色主题色调
  static const Color bluePrimary = Color(0xFF2196F3);
  static const Color bluePrimaryLight = Color(0xFF64B5F6);
  static const Color bluePrimaryDark = Color(0xFF1976D2);

  /// 绿色主题色调
  static const Color greenPrimary = Color(0xFF4CAF50);
  static const Color greenPrimaryLight = Color(0xFF81C784);
  static const Color greenPrimaryDark = Color(0xFF388E3C);

  /// 紫色主题色调
  static const Color purplePrimary = Color(0xFF9C27B0);
  static const Color purplePrimaryLight = Color(0xFFBA68C8);
  static const Color purplePrimaryDark = Color(0xFF7B1FA2);

  /// 橙色主题色调
  static const Color orangePrimary = Color(0xFFFF9800);
  static const Color orangePrimaryLight = Color(0xFFFFB74D);
  static const Color orangePrimaryDark = Color(0xFFF57C00);

  /// 红色主题色调
  static const Color redPrimary = Color(0xFFF44336);
  static const Color redPrimaryLight = Color(0xFFE57373);
  static const Color redPrimaryDark = Color(0xFFD32F2F);

  /// 默认主色调（蓝色）
  static const Color primaryColor = bluePrimary;
  static const Color primaryColorLight = bluePrimaryLight;
  static const Color primaryColorDark = bluePrimaryDark;

  /// 辅助色
  static const Color accentColor = Color(0xFF4CAF50);
  static const Color accentColorLight = Color(0xFF81C784);
  static const Color accentColorDark = Color(0xFF388E3C);

  /// 背景色
  static const Color backgroundColor = Color(0xFFF5F5F5);
  static const Color surfaceColor = Color(0xFFFFFFFF);
  static const Color cardColor = Color(0xFFFFFFFF);

  /// 文本色
  static const Color textPrimaryColor = Color(0xFF212121);
  static const Color textSecondaryColor = Color(0xFF757575);
  static const Color textHintColor = Color(0xFF9E9E9E);

  /// 状态色
  static const Color successColor = Color(0xFF4CAF50);
  static const Color warningColor = Color(0xFFFF9800);
  static const Color errorColor = Color(0xFFF44336);
  static const Color infoColor = Color(0xFF2196F3);

  /// 分割线色
  static const Color dividerColor = Color(0xFFE0E0E0);
  static const Color borderColor = Color(0xFFE0E0E0);

  // ==================== 字体定义 ====================

  /// 字体大小
  static const double fontSizeSmall = 12.0;
  static const double fontSizeNormal = 14.0;
  static const double fontSizeMedium = 16.0;
  static const double fontSizeLarge = 18.0;
  static const double fontSizeXLarge = 20.0;
  static const double fontSizeXXLarge = 24.0;

  /// 字体权重
  static const FontWeight fontWeightLight = FontWeight.w300;
  static const FontWeight fontWeightNormal = FontWeight.w400;
  static const FontWeight fontWeightMedium = FontWeight.w500;
  static const FontWeight fontWeightBold = FontWeight.w700;

  // ==================== 间距定义 ====================

  /// 标准间距
  static const double spacingXSmall = 4.0;
  static const double spacingSmall = 8.0;
  static const double spacingMedium = 16.0;
  static const double spacingLarge = 24.0;
  static const double spacingXLarge = 32.0;

  /// 页面边距
  static const double pageHorizontalPadding = 16.0;
  static const double pageVerticalPadding = 16.0;

  /// 卡片间距
  static const double cardPadding = 16.0;
  static const double cardMargin = 8.0;
  static const double cardRadius = 8.0;

  // ==================== 主题颜色获取方法 ====================

  /// 根据主题类型获取主色调
  static Color getPrimaryColor(ThemeType themeType) {
    switch (themeType) {
      case ThemeType.blue:
        return bluePrimary;
      case ThemeType.green:
        return greenPrimary;
      case ThemeType.purple:
        return purplePrimary;
      case ThemeType.orange:
        return orangePrimary;
      case ThemeType.red:
        return redPrimary;
    }
  }

  /// 根据主题类型获取浅色主色调
  static Color getPrimaryColorLight(ThemeType themeType) {
    switch (themeType) {
      case ThemeType.blue:
        return bluePrimaryLight;
      case ThemeType.green:
        return greenPrimaryLight;
      case ThemeType.purple:
        return purplePrimaryLight;
      case ThemeType.orange:
        return orangePrimaryLight;
      case ThemeType.red:
        return redPrimaryLight;
    }
  }

  /// 根据主题类型获取深色主色调
  static Color getPrimaryColorDark(ThemeType themeType) {
    switch (themeType) {
      case ThemeType.blue:
        return bluePrimaryDark;
      case ThemeType.green:
        return greenPrimaryDark;
      case ThemeType.purple:
        return purplePrimaryDark;
      case ThemeType.orange:
        return orangePrimaryDark;
      case ThemeType.red:
        return redPrimaryDark;
    }
  }

  /// 获取主题类型的显示名称
  static String getThemeTypeName(ThemeType themeType) {
    switch (themeType) {
      case ThemeType.blue:
        return '蓝色主题';
      case ThemeType.green:
        return '绿色主题';
      case ThemeType.purple:
        return '紫色主题';
      case ThemeType.orange:
        return '橙色主题';
      case ThemeType.red:
        return '红色主题';
    }
  }

  /// 获取AppBar样式的显示名称
  static String getAppBarStyleName(AppBarStyle style) {
    switch (style) {
      case AppBarStyle.colored:
        return '彩色AppBar';
      case AppBarStyle.white:
        return '白色AppBar';
      case AppBarStyle.transparent:
        return '透明AppBar';
    }
  }

  // ==================== 主题配置 ====================

  /// 根据主题类型和AppBar样式生成主题数据
  static ThemeData getTheme({ThemeType themeType = ThemeType.blue, AppBarStyle appBarStyle = AppBarStyle.colored}) {
    final primaryColor = getPrimaryColor(themeType);

    return ThemeData(
      // 基础配置
      useMaterial3: true,
      brightness: Brightness.light,

      // 颜色方案
      colorScheme: ColorScheme.light(
        primary: primaryColor,
        secondary: accentColor,
        surface: surfaceColor,
        error: errorColor,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: textPrimaryColor,
        onError: Colors.white,
      ),

      // 应用栏主题
      appBarTheme: _getAppBarTheme(appBarStyle, primaryColor),

      // 卡片主题
      cardTheme: CardThemeData(
        color: cardColor,
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(cardRadius)),
        margin: const EdgeInsets.all(cardMargin),
      ),

      // 按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          elevation: 2,
          padding: const EdgeInsets.symmetric(horizontal: spacingLarge, vertical: spacingMedium),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(cardRadius)),
          textStyle: const TextStyle(fontSize: fontSizeNormal, fontWeight: fontWeightMedium),
        ),
      ),

      // 输入框主题
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: surfaceColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(cardRadius),
          borderSide: const BorderSide(color: borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(cardRadius),
          borderSide: const BorderSide(color: borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(cardRadius),
          borderSide: BorderSide(color: primaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(cardRadius),
          borderSide: const BorderSide(color: errorColor),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: spacingMedium, vertical: spacingMedium),
        hintStyle: const TextStyle(color: textHintColor, fontSize: fontSizeNormal),
      ),

      // 文本主题
      textTheme: const TextTheme(
        displayLarge: TextStyle(color: textPrimaryColor, fontSize: fontSizeXXLarge, fontWeight: fontWeightBold),
        displayMedium: TextStyle(color: textPrimaryColor, fontSize: fontSizeXLarge, fontWeight: fontWeightBold),
        displaySmall: TextStyle(color: textPrimaryColor, fontSize: fontSizeLarge, fontWeight: fontWeightMedium),
        headlineMedium: TextStyle(color: textPrimaryColor, fontSize: fontSizeMedium, fontWeight: fontWeightMedium),
        bodyLarge: TextStyle(color: textPrimaryColor, fontSize: fontSizeNormal, fontWeight: fontWeightNormal),
        bodyMedium: TextStyle(color: textSecondaryColor, fontSize: fontSizeNormal, fontWeight: fontWeightNormal),
        bodySmall: TextStyle(color: textHintColor, fontSize: fontSizeSmall, fontWeight: fontWeightNormal),
      ),

      // 分割线主题
      dividerTheme: const DividerThemeData(color: dividerColor, thickness: 1, space: 1),
    );
  }

  /// 根据AppBar样式获取AppBar主题
  static AppBarTheme _getAppBarTheme(AppBarStyle style, Color primaryColor) {
    switch (style) {
      case AppBarStyle.colored:
        return AppBarTheme(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          elevation: 0,
          centerTitle: true,
          titleTextStyle: const TextStyle(color: Colors.white, fontSize: fontSizeLarge, fontWeight: fontWeightMedium),
          iconTheme: const IconThemeData(color: Colors.white),
          actionsIconTheme: const IconThemeData(color: Colors.white),
        );
      case AppBarStyle.white:
        return const AppBarTheme(
          backgroundColor: Colors.white,
          foregroundColor: textPrimaryColor,
          elevation: 0,
          centerTitle: true,
          titleTextStyle: TextStyle(color: textPrimaryColor, fontSize: fontSizeLarge, fontWeight: fontWeightMedium),
          iconTheme: IconThemeData(color: textPrimaryColor),
          actionsIconTheme: IconThemeData(color: textPrimaryColor),
        );
      case AppBarStyle.transparent:
        return const AppBarTheme(
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.white,
          elevation: 0,
          centerTitle: true,
          titleTextStyle: TextStyle(color: Colors.white, fontSize: fontSizeLarge, fontWeight: fontWeightMedium),
          iconTheme: IconThemeData(color: Colors.white),
          actionsIconTheme: IconThemeData(color: Colors.white),
        );
    }
  }

  /// 浅色主题（保持向后兼容）
  static ThemeData get lightTheme {
    return getTheme(themeType: ThemeType.blue, appBarStyle: AppBarStyle.colored);
  }

  /// 深色主题（预留）
  static ThemeData get darkTheme {
    return lightTheme;
  }
}

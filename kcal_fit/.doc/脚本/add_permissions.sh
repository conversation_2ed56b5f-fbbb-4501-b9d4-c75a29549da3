#!/bin/bash

# 定义要添加的权限配置
PERMISSIONS='
<!-- 语音识别权限 -->
    <key>NSSpeechRecognitionUsageDescription</key>
    <string>需要访问语音识别功能以提供语音输入服务</string>
    
    <!-- 麦克风权限 -->
    <key>NSMicrophoneUsageDescription</key>
    <string>需要访问麦克风以录制音频</string>
    
    <!-- 本地网络权限 -->
    <key>NSLocalNetworkUsageDescription</key>
    <string>需要访问本地网络以发现和连接本地设备</string>
    <key>NSBonjourServices</key>
    <array>
        <string>_services._dns-sd._udp</string>
    </array>
'

# 在Info.plist中添加权限配置
sed -i '' "/<dict>/a\\$PERMISSIONS" ios/Runner/Info.plist

echo "权限配置已自动添加到Info.plist"
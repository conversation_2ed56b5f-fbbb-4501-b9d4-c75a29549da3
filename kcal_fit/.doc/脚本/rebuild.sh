#!/bin/bash

# 全局清理
echo "正在执行全局清理..."
flutter clean || { echo "Flutter清理失败"; exit 1; }
flutter pub cache clean || { echo "Pub缓存清理失败"; exit 1; }

# iOS特定清理
echo "正在清理iOS项目..."
(cd ios && {
    pod deintegrate || { echo "Pod解除集成失败"; exit 1; }
    pod cache clean --all || { echo "Pod缓存清理失败"; exit 1; }
}) || exit 1

# 重新获取依赖
echo "正在获取Flutter依赖..."
flutter pub get || { echo "获取Flutter依赖失败"; exit 1; }

# 重建iOS项目
echo "正在重建iOS项目..."
flutter create --platforms ios . || { echo "iOS项目重建失败"; exit 1; }

# 安装iOS依赖
echo "正在安装iOS依赖..."
(cd ios && {
    pod install || { echo "Pod安装失败"; exit 1; }
}) || exit 1

# 运行应用
echo "正在启动应用..."
flutter run || { echo "应用启动失败"; exit 1; }

echo "重建完成"
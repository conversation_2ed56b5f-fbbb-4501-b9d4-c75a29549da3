# ios权限配置，不配置会导致发版失败
文件位置：ios/Runner/Info.plist
```xml
<!-- 语音识别权限 -->
    <key>NSSpeechRecognitionUsageDescription</key>
    <string>需要访问语音识别功能以提供语音输入服务</string>
    
    <!-- 麦克风权限 -->
    <key>NSMicrophoneUsageDescription</key>
    <string>需要访问麦克风以录制音频</string>
    
    <!-- 本地网络权限 -->
    <key>NSLocalNetworkUsageDescription</key>
    <string>需要访问本地网络以发现和连接本地设备</string>
    <key>NSBonjourServices</key>
    <array>
        <string>_services._dns-sd._udp</string>
    </array>
    <key>io.flutter.embedded_views_preview</key>
    <true/>

    <!-- 健康数据权限 -->
    <key>NSHealthShareUsageDescription</key>
    <string>需要访问健康数据以提供健康管理服务</string>
    <key>NSHealthUpdateUsageDescription</key>
    <string>需要更新健康数据以提供健康管理服务</string>

    <!-- 位置权限 -->
    <key>NSLocationWhenInUseUsageDescription</key>
    <string>需要访问位置信息以提供定位服务</string>

    <!-- 加密声明 - 声明应用不使用加密以简化App Store提交流程 -->
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
```
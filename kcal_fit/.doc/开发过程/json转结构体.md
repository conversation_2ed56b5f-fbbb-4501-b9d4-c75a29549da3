使用json_serializable自动化处理
```yaml
dependencies:
  json_annotation: ^4.9.0

dev_dependencies:
  build_runner: ^2.4.15
  json_serializable: ^6.9.0
```

## 定义模型类：
> 根据JSON
```json
{"extras":{"content":"一杯奶茶约500ml，提供250大卡能量，主要成分为碳水化合物(50g)，脂肪含量适中(5g)。含少量钙和维生素D。","error":"","foods":[{"micronutrients":[{"daily_value":"10%","value":100,"name":"钙","unit":"mg"},{"daily_value":"5%","value":1,"name":"维生素D","unit":"µg"}],"quantity":{"value":500,"unit":"ml"},"name":"奶茶","macronutrients":{"protein":{"value":2,"unit":"g"},"fat":{"value":5,"unit":"g"},"carbohydrates":{"value":50,"unit":"g"},"energy":{"value":250,"unit":"kcal"}}}]},"title":"饮食分析结果","content":"一杯奶茶","_j_msgid":18102207923348618,"content_type":"analysisData"}
```
## 创建模型类（可以借助AI） food_analysis.dart
```dart
import 'package:json_annotation/json_annotation.dart';

part 'food_analysis.g.dart';

/// 最外层响应模型
@JsonSerializable()
class AnalysisResponse {
  final String title;
  final String content;

  @JsonKey(name: 'content_type')
  final String contentType;

  @JsonKey(name: '_j_msgid')
  final int msgId;

  final Extras extras;

  AnalysisResponse({
    required this.title,
    required this.content,
    required this.contentType,
    required this.msgId,
    required this.extras,
  });

  factory AnalysisResponse.fromJson(Map<String, dynamic> json) =>
      _$AnalysisResponseFromJson(json);
  Map<String, dynamic> toJson() => _$AnalysisResponseToJson(this);
}

/// 中间层数据容器
@JsonSerializable()
class Extras {
  final String content;
  final String error; // 根据数据示例可能为空字符串
  final List<Food> foods;

  Extras({required this.content, required this.error, required this.foods});

  factory Extras.fromJson(Map<String, dynamic> json) => _$ExtrasFromJson(json);
  Map<String, dynamic> toJson() => _$ExtrasToJson(this);
}

/// 食物主体模型
@JsonSerializable()
class Food {
  final String name;
  final Quantity quantity;
  final List<Micronutrient> micronutrients;
  final Macronutrients macronutrients;

  Food({
    required this.name,
    required this.quantity,
    required this.micronutrients,
    required this.macronutrients,
  });

  factory Food.fromJson(Map<String, dynamic> json) => _$FoodFromJson(json);
  Map<String, dynamic> toJson() => _$FoodToJson(this);
}

/// 通用数值单位模型
@JsonSerializable()
class Quantity {
  final double value;
  final String unit;

  Quantity({required this.value, required this.unit});

  factory Quantity.fromJson(Map<String, dynamic> json) =>
      _$QuantityFromJson(json);
  Map<String, dynamic> toJson() => _$QuantityToJson(this);
}

/// 微量营养素模型
@JsonSerializable()
class Micronutrient {
  final String name;
  final String unit;
  final double value;

  @JsonKey(name: 'daily_value')
  final String dailyValue;

  Micronutrient({
    required this.name,
    required this.unit,
    required this.value,
    required this.dailyValue,
  });

  factory Micronutrient.fromJson(Map<String, dynamic> json) =>
      _$MicronutrientFromJson(json);
  Map<String, dynamic> toJson() => _$MicronutrientToJson(this);
}

/// 宏量营养素容器
@JsonSerializable()
class Macronutrients {
  final Macronutrient protein;
  final Macronutrient fat;
  final Macronutrient carbohydrates;
  final Macronutrient energy;

  Macronutrients({
    required this.protein,
    required this.fat,
    required this.carbohydrates,
    required this.energy,
  });

  factory Macronutrients.fromJson(Map<String, dynamic> json) =>
      _$MacronutrientsFromJson(json);
  Map<String, dynamic> toJson() => _$MacronutrientsToJson(this);
}

/// 宏量营养素单项
@JsonSerializable()
class Macronutrient {
  final double value;
  final String unit;

  Macronutrient({required this.value, required this.unit});

  factory Macronutrient.fromJson(Map<String, dynamic> json) =>
      _$MacronutrientFromJson(json);
  Map<String, dynamic> toJson() => _$MacronutrientToJson(this);
}

```
## 生成代码
```
flutter pub run build_runner build --delete-conflicting-outputs
```
## 使用示例
```dart
 final analysis = AnalysisResponse.fromJson( json.decode(json.encode(safeMessage)) as Map<String, dynamic>);
```
# [!] CocoaPods did not set the base configuration of your project because your project already has a custom config set. In order for CocoaPods integration to work at all, please either set the base configurations of the target Runner to Target Support Files/Pods-Runner/Pods-Runner.profile.xcconfig or include the Target Support Files/Pods-Runner/Pods-Runner.profile.xcconfig in your build configuration (Flutter/Release.xcconfig).

我们首先用Xcode打开ios/Runner.xcworkspace；
然后选中Project Navigator下的Runner项目，再选中PROJECT下面的Runner，再然后选中Info；
我们找到Configurations，把Debug、Release和Profile下面的Runner/Runner全部选为None;
再一次运行pod update，警告消息。



# 极光通知消息推送成功，但是手机上没有收到消息推送
>启用推送通知功能:要将所需的权限添加到您的应用中，请在您的 Xcode 项目中启用推送通知功能
>参考：[官方文档-Apple Push Notification 服务（APNs）](http://developer.apple.com/documentation/usernotifications/registering-your-app-with-apns#Enable-the-push-notifications-capability)
xcode->runner->Signing & Capabilities-> +Capability + Push Notifications 


# xcode编译报错  framework 'Pods_Runner' not found
```
cd /Users/<USER>/Documents/prod_project/diet-management/kcal_fit/ios
pod deintegrate
pod cache clean --all
cd ..
flutter clean
flutter pub get
cd ios
pod install
```

# ios/Flutter/Release.xcconfig:3:1 could not find included file 'Target Support Files/Pods-Runner/Pods-Runner.profile.xcconfig' in search paths
>根据错误信息和查看的文件内容，问题出在iOS项目的Release.xcconfig文件中无法找到Pods-Runner.profile.xcconfig文件。这是一个典型的CocoaPods配置问题
```
首先需要运行pod install来生成所需的Pods配置文件
cd /Users/<USER>/Documents/prod_project/diet-management/kcal_fit/ios
pod install
如果pod install后问题仍然存在，可以尝试修改Release.xcconfig文件，移除对profile配置的引用
#include? "Pods/Target Support Files/Pods-Runner/Pods-Runner.release.xcconfig"
#include "Generated.xcconfig"
// ... existing code ...
如果问题持续，可以尝试清理并重新生成Pod文件
cd /Users/<USER>/Documents/prod_project/diet-management/kcal_fit/ios
rm -rf Pods Podfile.lock
pod install
```
这个错误通常是由于Pod配置不完整或过时导致的，重新运行pod install应该能解决问题。

# 发布失败
> 检查权限配置
```
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    // ... existing code ...
    
    <!-- 语音识别权限 -->
    <key>NSSpeechRecognitionUsageDescription</key>
    <string>需要访问语音识别功能以提供语音输入服务</string>
    
    <!-- 麦克风权限 -->
    <key>NSMicrophoneUsageDescription</key>
    <string>需要访问麦克风以录制音频</string>
    
    <!-- 本地网络权限 -->
    <key>NSLocalNetworkUsageDescription</key>
    <string>需要访问本地网络以发现和连接本地设备</string>
    <key>NSBonjourServices</key>
    <array>
        <string>_services._dns-sd._udp</string>
    </array>
    
    // ... existing code ...
</dict>
</plist>
```
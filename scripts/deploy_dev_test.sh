#!/bin/bash

# 定义变量
IMAGE_NAME="diet-management"
TAG="latest"
TAR_FILE="${IMAGE_NAME}-${TAG}.tar"
REMOTE_USER="huiziqin"
REMOTE_HOST="huiziqin.me"
REMOTE_DIR="/home/<USER>"
DOCKERFILE_PATH="/Users/<USER>/Documents/prod_project/diet-management/admin/Dockerfile"
CONTAINER_NAME="diet-management-container"

# 构建Docker镜像
echo "正在构建Docker镜像..."
docker build -t ${IMAGE_NAME}:${TAG} -f ${DOCKERFILE_PATH} /Users/<USER>/Documents/prod_project/diet-management/admin/

# 检查构建是否成功
if [ $? -ne 0 ]; then
    echo "镜像构建失败"
    exit 1
fi

# 导出镜像为tar文件
echo "正在导出镜像..."
docker save -o ${TAR_FILE} ${IMAGE_NAME}:${TAG}

# 检查导出是否成功
if [ $? -ne 0 ]; then
    echo "镜像导出失败"
    exit 1
fi

# 上传到远程服务器
echo "正在上传到服务器..."
scp -i ~/.ssh/id_rsa ${TAR_FILE} ${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_DIR}/${TAR_FILE}
# 检查上传是否成功
if [ $? -ne 0 ]; then
    echo "上传失败"
    exit 1
fi

# 停止并删除旧容器，加载新镜像并运行
echo "正在停止旧容器并启动新容器..."
ssh -i ~/.ssh/id_rsa ${REMOTE_USER}@${REMOTE_HOST} "
    # 停止旧容器（如果存在）
    if docker ps -q -f name=${CONTAINER_NAME} | grep -q .; then
        echo '停止旧容器...'
        docker stop ${CONTAINER_NAME}
    fi
    
    # 删除旧容器（如果存在）
    if docker ps -aq -f name=${CONTAINER_NAME} | grep -q .; then
        echo '删除旧容器...'
        docker rm ${CONTAINER_NAME}
    fi
    
    # 删除旧镜像（可选，节省空间）
    if docker images -q ${IMAGE_NAME}:${TAG} | grep -q .; then
        echo '删除旧镜像...'
        docker rmi ${IMAGE_NAME}:${TAG}
    fi
    
    # 加载新镜像
    echo '加载新镜像...'
    docker load -i ${REMOTE_DIR}/${TAR_FILE}
    
    # 启动新容器
    echo '启动新容器...'
    docker run --name ${CONTAINER_NAME} -d -p 18998:8080 --network bridge ${IMAGE_NAME}:${TAG}
"

# 检查远程操作是否成功
if [ $? -ne 0 ]; then
    echo "远程部署失败"
    exit 1
fi

# 清理本地文件
echo "清理本地文件:${TAR_FILE}"
rm -f ${TAR_FILE}
# 清理服务器上的tar文件
echo "清理服务器上的tar文件:${REMOTE_DIR}/${TAR_FILE}"
ssh -i ~/.ssh/id_rsa ${REMOTE_USER}@${REMOTE_HOST} "rm -rf ${REMOTE_DIR}/${TAR_FILE}"

echo "部署流程完成"

## 关于使用密钥登录，不用手动输密码
# ssh-keygen -t rsa -b 4096
# ssh-copy-id -i ~/.ssh/id_rsa.pub <EMAIL> 
# ssh -i ~/.ssh/id_rsa <EMAIL>
# - scp命令用于本地和远程服务器之间的文件传输
# - ssh命令用于在远程服务器上执行命令

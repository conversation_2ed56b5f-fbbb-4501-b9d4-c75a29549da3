# 生产环境部署 117.72.80.143 

## 数据库部署
- 将 docker-compose-redis.yml 文件上传至服务器,并执行  并指定网络 kcal_fit

## nsq 中间件部署
- 将 docker-compose-nsq.yml 文件上传至服务器,并执行  并指定网络 kcal_fit

## 后端服务部署
- 运行 deploy_prod.sh 脚本，并指定网络 kcal_fit

https://kcalfit.huiziqin.com/api/dietInfo/getAnalysisData
NGINX反向代理：
```bash
     # 新增反向代理配置 - START
    location ^~ /api/ {
        proxy_pass http://127.0.0.1:18998/api/;  # 末尾斜杠确保路径正确传递
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 可选：超时设置
        proxy_connect_timeout 60s;
        proxy_read_timeout 600s;
        proxy_send_timeout 600s;
    }
```
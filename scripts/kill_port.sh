#!/bin/bash

# 函数：记录日志 ./kill_port.sh 8080
function log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# 检查参数
if [ $# -eq 0 ]; then
    log "错误：缺少端口参数"
    log "使用方法: $0 <端口号>"
    exit 1
fi

PORT=$1

# 验证端口号有效性
if ! [[ "$PORT" =~ ^[0-9]+$ ]] || [ "$PORT" -lt 1 ] || [ "$PORT" -gt 65535 ]; then
    log "错误：无效端口号 '$PORT'"
    exit 1
fi

# 查找进程
log "正在查找运行在端口 $PORT 的进程..."
PID=$(lsof -ti :$PORT 2>/dev/null)

if [ -z "$PID" ]; then
    log "未找到运行在端口 $PORT 的进程"
    exit 0
fi

log "找到进程ID: $PID"

# 结束进程
for pid in $PID; do
    log "正在终止进程 $pid"
    if kill -9 $pid 2>/dev/null; then
        log "成功终止进程 $pid"
    else
        log "终止进程 $pid 失败"
        exit 1
    fi
done

exit 0
#!/bin/bash

# KcalFit iOS版官网部署脚本
# 用于自动化部署iOS专用网站到服务器

set -e  # 遇到错误时退出

# 配置变量
WEBSITE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_DIR="$WEBSITE_DIR/dist"
BACKUP_DIR="$WEBSITE_DIR/backup"
LOG_FILE="$WEBSITE_DIR/deploy.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# 显示帮助信息
show_help() {
    echo "KcalFit iOS版官网部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -b, --build    构建网站"
    echo "  -d, --deploy   部署到服务器"
    echo "  -c, --clean    清理构建文件"
    echo "  -t, --test     本地测试服务器"
    echo "  --backup       备份当前版本"
    echo "  --restore      恢复备份版本"
    echo ""
    echo "示例:"
    echo "  $0 --build     # 构建网站"
    echo "  $0 --deploy    # 部署网站"
    echo "  $0 --test      # 启动本地测试服务器"
}

# 检查依赖
check_dependencies() {
    log "检查依赖..."
    
    # 检查必要的命令
    local deps=("rsync" "tar" "gzip")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            error "缺少依赖: $dep"
            exit 1
        fi
    done
    
    success "依赖检查完成"
}

# 创建目录
create_directories() {
    log "创建必要目录..."
    
    mkdir -p "$BUILD_DIR"
    mkdir -p "$BACKUP_DIR"
    
    success "目录创建完成"
}

# 构建网站
build_website() {
    log "开始构建网站..."
    
    # 清理构建目录
    rm -rf "$BUILD_DIR"
    mkdir -p "$BUILD_DIR"
    
    # 复制文件
    log "复制网站文件..."
    cp -r "$WEBSITE_DIR"/*.html "$BUILD_DIR/"
    cp -r "$WEBSITE_DIR/assets" "$BUILD_DIR/"
    
    # 压缩CSS和JS文件（如果有相关工具）
    if command -v uglifyjs &> /dev/null; then
        log "压缩JavaScript文件..."
        uglifyjs "$BUILD_DIR/assets/js/main.js" -o "$BUILD_DIR/assets/js/main.min.js"
        mv "$BUILD_DIR/assets/js/main.min.js" "$BUILD_DIR/assets/js/main.js"
    fi
    
    if command -v cleancss &> /dev/null; then
        log "压缩CSS文件..."
        cleancss -o "$BUILD_DIR/assets/css/style.min.css" "$BUILD_DIR/assets/css/style.css"
        mv "$BUILD_DIR/assets/css/style.min.css" "$BUILD_DIR/assets/css/style.css"
    fi
    
    # 生成sitemap.xml
    generate_sitemap
    
    # 生成robots.txt
    generate_robots
    
    success "网站构建完成"
}

# 生成sitemap.xml
generate_sitemap() {
    log "生成sitemap.xml..."
    
    cat > "$BUILD_DIR/sitemap.xml" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    <url>
        <loc>https://kcal_fit.huiziqin.com/</loc>
        <lastmod>$(date +%Y-%m-%d)</lastmod>
        <changefreq>weekly</changefreq>
        <priority>1.0</priority>
    </url>
    <url>
        <loc>https://kcal_fit.huiziqin.com/privacy.html</loc>
        <lastmod>$(date +%Y-%m-%d)</lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.5</priority>
    </url>
    <url>
        <loc>https://kcal_fit.huiziqin.com/terms.html</loc>
        <lastmod>$(date +%Y-%m-%d)</lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.5</priority>
    </url>
</urlset>
EOF
    
    success "sitemap.xml 生成完成"
}

# 生成robots.txt
generate_robots() {
    log "生成robots.txt..."
    
    cat > "$BUILD_DIR/robots.txt" << EOF
User-agent: *
Allow: /

Sitemap:https://kcalfit.huiziqin.com//sitemap.xml
EOF
    
    success "robots.txt 生成完成"
}

# 备份当前版本
backup_current() {
    log "备份当前版本..."
    
    local backup_name="backup_$(date +%Y%m%d_%H%M%S).tar.gz"
    local backup_path="$BACKUP_DIR/$backup_name"
    
    if [ -d "$BUILD_DIR" ]; then
        tar -czf "$backup_path" -C "$BUILD_DIR" .
        success "备份完成: $backup_name"
    else
        warning "没有找到构建文件，跳过备份"
    fi
}

# 恢复备份
restore_backup() {
    log "恢复备份..."
    
    # 列出可用备份
    local backups=($(ls -1 "$BACKUP_DIR"/*.tar.gz 2>/dev/null | sort -r))
    
    if [ ${#backups[@]} -eq 0 ]; then
        error "没有找到备份文件"
        exit 1
    fi
    
    echo "可用备份:"
    for i in "${!backups[@]}"; do
        echo "  $((i+1)). $(basename "${backups[$i]}")"
    done
    
    read -p "请选择要恢复的备份 (1-${#backups[@]}): " choice
    
    if [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -ge 1 ] && [ "$choice" -le ${#backups[@]} ]; then
        local selected_backup="${backups[$((choice-1))]}"
        
        # 清理当前构建目录
        rm -rf "$BUILD_DIR"
        mkdir -p "$BUILD_DIR"
        
        # 恢复备份
        tar -xzf "$selected_backup" -C "$BUILD_DIR"
        success "备份恢复完成: $(basename "$selected_backup")"
    else
        error "无效选择"
        exit 1
    fi
}

# 部署到服务器
deploy_to_server() {
    log "开始部署到服务器..."
    
    # 检查构建文件是否存在
    if [ ! -d "$BUILD_DIR" ]; then
        error "构建文件不存在，请先运行构建"
        exit 1
    fi
    
    # 这里需要根据实际服务器配置修改
    # 示例配置（请根据实际情况修改）
    local SERVER_HOST="your-server.com"
    local SERVER_USER="www-data"
    local SERVER_PATH="/var/www/kcalfit"
    
    warning "请确保已配置服务器连接信息"
    read -p "是否继续部署? (y/N): " confirm
    
    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        # 使用rsync同步文件
        log "同步文件到服务器..."
        rsync -avz --delete "$BUILD_DIR/" "$SERVER_USER@$SERVER_HOST:$SERVER_PATH/"
        
        success "部署完成"
    else
        log "部署已取消"
    fi
}

# 启动本地测试服务器
start_test_server() {
    log "启动本地测试服务器..."
    
    local port=8080
    local dir="$BUILD_DIR"
    
    if [ ! -d "$dir" ]; then
        warning "构建文件不存在，使用源文件启动服务器"
        dir="$WEBSITE_DIR"
    fi
    
    # 检查端口是否被占用
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null; then
        warning "端口 $port 已被占用，尝试使用其他端口"
        port=8081
    fi
    
    log "在端口 $port 启动服务器..."
    log "访问地址: http://localhost:$port"
    
    # 使用Python启动简单HTTP服务器
    if command -v python3 &> /dev/null; then
        cd "$dir" && python3 -m http.server $port
    elif command -v python &> /dev/null; then
        cd "$dir" && python -m SimpleHTTPServer $port
    else
        error "未找到Python，无法启动测试服务器"
        exit 1
    fi
}

# 清理构建文件
clean_build() {
    log "清理构建文件..."
    
    rm -rf "$BUILD_DIR"
    
    success "清理完成"
}

# 主函数
main() {
    log "KcalFit 官网部署脚本启动"
    
    # 检查参数
    if [ $# -eq 0 ]; then
        show_help
        exit 0
    fi
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -b|--build)
                check_dependencies
                create_directories
                build_website
                shift
                ;;
            -d|--deploy)
                deploy_to_server
                shift
                ;;
            -c|--clean)
                clean_build
                shift
                ;;
            -t|--test)
                start_test_server
                shift
                ;;
            --backup)
                backup_current
                shift
                ;;
            --restore)
                restore_backup
                shift
                ;;
            *)
                error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    success "脚本执行完成"
}

# 运行主函数
main "$@"

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="robots" content="noindex, nofollow">
    <title>页面未找到 - KcalFit健康饮食管理应用</title>
    <meta name="description" content="抱歉，您访问的页面不存在。返回KcalFit首页继续浏览我们的健康饮食管理应用。">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="./assets/images/favicon.ico">
    
    <!-- Styles -->
    <link rel="stylesheet" href="./assets/css/style.css">
    
    <style>
        .error-page {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            color: white;
            text-align: center;
            padding: 20px;
        }
        
        .error-content {
            max-width: 600px;
        }
        
        .error-code {
            font-size: 8rem;
            font-weight: 700;
            margin-bottom: 20px;
            opacity: 0.8;
        }
        
        .error-title {
            font-size: 2rem;
            margin-bottom: 20px;
        }
        
        .error-description {
            font-size: 1.2rem;
            margin-bottom: 40px;
            opacity: 0.9;
        }
        
        .error-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: white;
            color: #2196F3;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255,255,255,0.3);
        }
        
        .btn-secondary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }
        
        .btn-secondary:hover {
            background: rgba(255,255,255,0.3);
        }
        
        @media (max-width: 768px) {
            .error-code {
                font-size: 6rem;
            }
            
            .error-title {
                font-size: 1.5rem;
            }
            
            .error-description {
                font-size: 1rem;
            }
            
            .error-actions {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="error-page">
        <div class="error-content">
            <div class="error-code">404</div>
            <h1 class="error-title">页面未找到</h1>
            <p class="error-description">
                抱歉，您访问的页面不存在或已被移动。<br>
                让我们帮您找到正确的页面。
            </p>
            
            <div class="error-actions">
                <a href="/" class="btn btn-primary">返回首页</a>
                <a href="/#download" class="btn btn-secondary">下载应用</a>
            </div>
            
            <div style="margin-top: 40px;">
                <p style="opacity: 0.8;">
                    或者您可以：<br>
                    • <a href="/#features" style="color: white;">了解功能特性</a><br>
                    • <a href="/#help" style="color: white;">查看帮助中心</a><br>
                    • <a href="/#contact" style="color: white;">联系我们</a>
                </p>
            </div>
        </div>
    </div>
    
    <!-- 自动跳转脚本 -->
    <script>
        // 5秒后自动跳转到首页
        setTimeout(function() {
            if (confirm('是否自动跳转到首页？')) {
                window.location.href = '/';
            }
        }, 5000);
    </script>
</body>
</html>

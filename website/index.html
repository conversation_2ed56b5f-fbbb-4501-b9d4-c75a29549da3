<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description"
        content="KcalFit - 专业的iOS健康饮食管理应用，智能卡路里记录、营养分析、减肥目标设定。支持语音输入，科学饮食管理，助您轻松达成健康目标。免费下载，10万+用户信赖选择。">
    <meta name="keywords" content="卡路里记录,饮食管理,健康减肥,营养分析,体重管理,iOS健康应用,智能饮食记录,减肥APP,热量计算,营养追踪,健康生活,饮食日记,体重控制,健身饮食,食物热量查询">
    <meta name="author" content="KcalFit Team">
    <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1">
    <meta name="googlebot" content="index, follow">
    <meta name="bingbot" content="index, follow">
    <meta name="language" content="zh-CN">
    <meta name="geo.region" content="CN">
    <meta name="geo.placename" content="China">
    <meta name="application-name" content="KcalFit">
    <meta name="msapplication-TileColor" content="#2196F3">
    <meta name="theme-color" content="#2196F3">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://kcalfit.huiziqin.com/">
    <meta property="og:title" content="KcalFit - 专业iOS健康饮食管理应用 | 智能卡路里记录减肥助手">
    <meta property="og:description"
        content="KcalFit是专业的iOS健康饮食管理应用，提供智能卡路里记录、营养分析、减肥目标设定。支持语音输入，10万+用户信赖选择，助您科学管理饮食，轻松达成健康目标。">
    <meta property="og:image" content="https://kcalfit.huiziqin.com/assets/images/og-image.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:image:alt" content="KcalFit iOS健康饮食管理应用截图">
    <meta property="og:site_name" content="KcalFit">
    <meta property="og:locale" content="zh_CN">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://kcalfit.huiziqin.com/">
    <meta name="twitter:title" content="KcalFit - 专业iOS健康饮食管理应用 | 智能卡路里记录减肥助手">
    <meta name="twitter:description" content="KcalFit是专业的iOS健康饮食管理应用，提供智能卡路里记录、营养分析、减肥目标设定。支持语音输入，10万+用户信赖选择。">
    <meta name="twitter:image" content="https://kcalfit.huiziqin.com/assets/images/og-image.jpg">
    <meta name="twitter:image:alt" content="KcalFit iOS健康饮食管理应用截图">
    <meta name="twitter:creator" content="@KcalFit">
    <meta name="twitter:site" content="@KcalFit">

    <title>KcalFit - 专业iOS健康饮食管理应用 | 智能卡路里记录减肥助手 | 免费下载</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="./assets/images/favicon.ico">
    <link rel="apple-touch-icon" href="./assets/images/apple-touch-icon.png">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Styles -->
    <link rel="stylesheet" href="./assets/css/style.css">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- 结构化数据 -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "KcalFit",
        "description": "专业的iOS健康饮食管理应用，提供智能卡路里记录、营养分析、减肥目标设定。支持语音输入，科学饮食管理，助您轻松达成健康目标。",
        "url": "https://kcalfit.huiziqin.com/",
        "image": "https://kcalfit.huiziqin.com/assets/images/logo.png",
        "applicationCategory": "HealthApplication",
        "operatingSystem": "iOS",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "CNY"
        },
        "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.8",
            "ratingCount": "10000"
        },
        "author": {
            "@type": "Organization",
            "name": "KcalFit Team",
            "url": "https://kcalfit.huiziqin.com/"
        },
        "publisher": {
            "@type": "Organization",
            "name": "KcalFit Team",
            "logo": {
                "@type": "ImageObject",
                "url": "https://kcalfit.huiziqin.com/assets/images/logo.png"
            }
        },
        "downloadUrl": "https://apps.apple.com/app/kcalfit",
        "screenshot": [
            "https://kcalfit.huiziqin.com/assets/images/screenshot/app-screenshot-1.png",
            "https://kcalfit.huiziqin.com/assets/images/screenshot/app-screenshot-2.png",
            "https://kcalfit.huiziqin.com/assets/images/screenshot/app-screenshot-3.png",
            "https://kcalfit.huiziqin.com/assets/images/screenshot/app-screenshot-4.png"
        ],
        "featureList": [
            "智能饮食记录",
            "语音识别输入",
            "营养成分分析",
            "热量趋势图表",
            "个性化目标设定",
            "进度跟踪提醒"
        ],
        "requirements": "iOS 12.0或更高版本",
        "softwareVersion": "1.1.0",
        "datePublished": "2024-01-01",
        "dateModified": "2025-01-01"
    }
    </script>

    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "KcalFit Team",
        "url": "https://kcalfit.huiziqin.com/",
        "logo": "https://kcalfit.huiziqin.com/assets/images/logo.png",
        "description": "专注健康科技，为iOS用户打造最优秀的健康饮食管理体验",
        "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "+86-************",
            "contactType": "customer service",
            "email": "<EMAIL>"
        },
        "sameAs": [
            "https://weibo.com/kcalfit",
            "https://www.zhihu.com/people/kcalfit"
        ]
    }
    </script>

    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "KcalFit",
        "url": "https://kcalfit.huiziqin.com/",
        "description": "专业的iOS健康饮食管理应用官网",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "https://kcalfit.huiziqin.com/search?q={search_term_string}",
            "query-input": "required name=search_term_string"
        }
    }
    </script>
</head>

<body>
    <!-- Navigation -->
    <header>
        <nav class="navbar" id="navbar" role="navigation" aria-label="主导航">
            <div class="nav-container">
                <div class="nav-logo">
                    <img src="./assets/images/logo.png" alt="KcalFit健康饮食管理应用Logo" class="logo-img">
                    <span class="logo-text">KcalFit</span>
                </div>

                <ul class="nav-menu" id="nav-menu">
                    <li class="nav-item">
                        <a href="#home" class="nav-link active">首页</a>
                    </li>
                    <li class="nav-item">
                        <a href="#features" class="nav-link">功能特性</a>
                    </li>
                    <li class="nav-item">
                        <a href="#about" class="nav-link">关于我们</a>
                    </li>
                    <li class="nav-item">
                        <a href="#help" class="nav-link">帮助中心</a>
                    </li>
                    <li class="nav-item">
                        <a href="#contact" class="nav-link">联系我们</a>
                    </li>
                    <li class="nav-item">
                        <a href="#download" class="nav-link download-btn">立即下载</a>
                    </li>
                </ul>

                <div class="hamburger" id="hamburger">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- 主要内容区域 -->
    <main role="main">
        <!-- Hero Section -->
        <section id="home" class="hero">
            <div class="hero-container">
                <div class="hero-content">
                    <div class="hero-text">
                        <h1 class="hero-title">
                            您的专属<span class="highlight">健康饮食</span>管家
                        </h1>
                        <p class="hero-subtitle">
                            通过<strong>智能饮食记录</strong>、<strong>个性化热量管理</strong>与<strong>科学的减肥目标设定</strong>，<br>
                            助您掌握每一天的营养与卡路里，轻松迈向健康生活！专业的<em>iOS健康应用</em>，<a href="#download"
                                style="color: #fff; text-decoration: underline;">免费下载</a>体验。
                        </p>
                        <div class="hero-buttons">
                            <a href="#download" class="btn btn-primary">
                                <i class="fas fa-download"></i>
                                立即下载
                            </a>
                            <a href="#features" class="btn btn-secondary">
                                <i class="fas fa-play"></i>
                                了解更多
                            </a>
                        </div>
                        <div class="hero-stats">
                            <div class="stat-item">
                                <span class="stat-number">10K+</span>
                                <span class="stat-label">活跃用户</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">50K+</span>
                                <span class="stat-label">饮食记录</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">95%</span>
                                <span class="stat-label">用户满意度</span>
                            </div>
                        </div>
                    </div>
                    <div class="hero-image">
                        <div class="phone-mockup">
                            <img src="./assets/images/screenshot/app-screenshot-1.png"
                                alt="KcalFit iOS健康饮食管理应用主界面截图，显示智能卡路里记录和营养分析功能" class="app-screenshot">
                            <div class="floating-card card-1">
                                <i class="fas fa-utensils"></i>
                                <span>智能饮食记录</span>
                            </div>
                            <div class="floating-card card-2">
                                <i class="fas fa-chart-line"></i>
                                <span>数据分析</span>
                            </div>
                            <div class="floating-card card-3">
                                <i class="fas fa-target"></i>
                                <span>健康目标</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="hero-bg-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
            </div>
        </section>

        <!-- Features Section -->
        <section id="features" class="features">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">核心功能</h2>
                    <p class="section-subtitle">科学记录，智能分析，让健康生活更简单</p>
                </div>

                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <h3 class="feature-title">智能饮食记录</h3>
                        <p class="feature-description">快速记录每日饮食，自动计算卡路里摄入，支持语音输入，让记录更便捷</p>
                        <ul class="feature-list">
                            <li>语音识别快速输入</li>
                            <li>智能食物识别</li>
                            <li>自动营养计算</li>
                            <li>餐次分类管理</li>
                        </ul>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="feature-title">数据分析</h3>
                        <p class="feature-description">详细的营养分析和趋势图表，帮您了解饮食习惯，科学调整营养结构</p>
                        <ul class="feature-list">
                            <li>营养成分分析</li>
                            <li>热量趋势图表</li>
                            <li>饮食习惯报告</li>
                            <li>健康建议推荐</li>
                        </ul>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-target"></i>
                        </div>
                        <h3 class="feature-title">健康目标</h3>
                        <p class="feature-description">个性化的减重和健康目标设定，制定专属的健康计划，追踪进度</p>
                        <ul class="feature-list">
                            <li>个性化目标设定</li>
                            <li>进度跟踪提醒</li>
                            <li>阶段性成果展示</li>
                            <li>智能调整建议</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- How It Works Section -->
        <section class="how-it-works">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">如何使用</h2>
                    <p class="section-subtitle">三步开启您的健康饮食之旅</p>
                </div>

                <div class="steps-container">
                    <div class="step-item">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h3>完善个人信息</h3>
                            <p>输入身高、体重、年龄等基本信息，系统为您计算个性化的营养需求</p>
                        </div>
                        <div class="step-image">
                            <img src="./assets/images/screenshot/app-screenshot-6.png"
                                alt="KcalFit个人信息设置页面，用户可输入身高体重年龄等基本信息进行个性化营养需求计算">
                        </div>
                    </div>

                    <div class="step-item">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h3>记录每日饮食</h3>
                            <p>通过语音或手动输入记录三餐，系统自动计算营养成分和卡路里</p>
                        </div>
                        <div class="step-image">
                            <img src="./assets/images/screenshot/app-screenshot-3.png"
                                alt="KcalFit饮食记录界面，支持语音输入和手动记录，自动计算食物卡路里和营养成分">
                        </div>
                    </div>

                    <div class="step-item">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h3>查看分析报告</h3>
                            <p>获得详细的营养分析报告，了解饮食习惯，调整健康计划</p>
                        </div>
                        <div class="step-image">
                            <img src="./assets/images/screenshot/app-screenshot-1.png"
                                alt="KcalFit营养分析报告页面，显示详细的饮食习惯分析和健康建议">
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Screenshots Section -->
        <section class="screenshots">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">应用截图</h2>
                    <p class="section-subtitle">简洁美观的界面设计，流畅的用户体验</p>
                </div>

                <div class="screenshots-slider">
                    <div class="screenshot-item">
                        <img src="./assets/images/screenshot/app-screenshot-1.png"
                            alt="KcalFit首页界面截图，展示智能饮食记录和营养分析功能入口">
                        <h4>首页界面</h4>
                    </div>
                    <div class="screenshot-item">
                        <img src="./assets/images/screenshot/app-screenshot-2.png"
                            alt="KcalFit饮食记录页面截图，支持语音输入和智能食物识别功能">
                        <h4>饮食记录</h4>
                    </div>
                    <div class="screenshot-item">
                        <img src="./assets/images/screenshot/app-screenshot-3.png"
                            alt="KcalFit数据分析页面截图，显示营养成分分析和热量趋势图表">
                        <h4>数据分析</h4>
                    </div>
                    <div class="screenshot-item">
                        <img src="./assets/images/screenshot/app-screenshot-4.png"
                            alt="KcalFit个人中心页面截图，包含健康目标设定和进度跟踪功能">
                        <h4>个人中心</h4>
                    </div>
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section id="about" class="about">
            <div class="container">
                <div class="about-content">
                    <div class="about-text">
                        <div class="section-header">
                            <h2 class="section-title">关于我们</h2>
                            <p class="section-subtitle">专注健康科技，让每个人都能享受科学的饮食管理</p>
                        </div>

                        <div class="about-description">
                            <p>KcalFit 团队由营养专家、iOS开发者和产品设计师组成，我们致力于通过科技的力量，为iOS用户打造最优秀的健康饮食管理体验。</p>
                            <p>我们专注于iOS平台，充分利用iPhone和iPad的强大功能，为用户提供流畅、直观的健康饮食管理服务，帮助建立良好的饮食习惯。</p>
                        </div>

                        <div class="team-info">
                            <div class="team-item">
                                <div class="team-icon">
                                    <i class="fas fa-palette"></i>
                                </div>
                                <div class="team-content">
                                    <h4>产品设计</h4>
                                    <p>专注用户体验，打造简洁易用的界面</p>
                                </div>
                            </div>
                            <div class="team-item">
                                <div class="team-icon">
                                    <i class="fas fa-code"></i>
                                </div>
                                <div class="team-content">
                                    <h4>iOS开发</h4>
                                    <p>采用Swift和最新iOS技术，确保应用稳定流畅</p>
                                </div>
                            </div>
                            <div class="team-item">
                                <div class="team-icon">
                                    <i class="fas fa-heartbeat"></i>
                                </div>
                                <div class="team-content">
                                    <h4>营养专家</h4>
                                    <p>提供专业的营养知识和健康建议</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="about-image">
                        <img src="./assets/images/screenshot/wechat.jpg" alt="关于我们">
                    </div>
                </div>
            </div>
        </section>

        <!-- Help Section -->
        <section id="help" class="help">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">帮助中心</h2>
                    <p class="section-subtitle">常见问题解答，让您快速上手</p>
                </div>

                <div class="help-content">
                    <div class="faq-list">
                        <div class="faq-item">
                            <div class="faq-question">
                                <h4>如何记录饮食数据？</h4>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>在首页点击"记录"按钮，选择食物类型，输入食物名称和重量，系统会自动计算卡路里。您也可以使用语音输入功能，让记录更加便捷。</p>
                            </div>
                        </div>

                        <div class="faq-item">
                            <div class="faq-question">
                                <h4>如何设置健康目标？</h4>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>进入"我的"页面，点击"目标设定"，根据个人情况设置目标体重和时间计划。系统会根据您的目标制定个性化的饮食建议。</p>
                            </div>
                        </div>

                        <div class="faq-item">
                            <div class="faq-question">
                                <h4>数据安全如何保障？</h4>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>我们采用加密技术保护您的数据安全，所有个人信息都存储在您的iPhone本地设备中，不会上传到服务器。您可以随时删除个人数据。</p>
                            </div>
                        </div>

                        <div class="faq-item">
                            <div class="faq-question">
                                <h4>支持哪些iOS设备？</h4>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>KcalFit支持iOS 12.0及以上版本的所有iPhone和iPad设备，包括iPhone 6s及更新机型，以及iPad Air 2及更新机型。</p>
                            </div>
                        </div>

                        <div class="faq-item">
                            <div class="faq-question">
                                <h4>如何修改个人信息？</h4>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>在"我的"页面点击头像或个人信息区域，即可进入个人信息编辑页面，修改身高、体重、年龄等基本信息。</p>
                            </div>
                        </div>
                    </div>

                    <div class="help-contact">
                        <div class="help-card">
                            <i class="fas fa-question-circle"></i>
                            <h4>还有其他问题？</h4>
                            <p>如果您遇到其他问题，欢迎联系我们的客服团队</p>
                            <a href="#contact" class="btn btn-primary">联系客服</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section id="contact" class="contact">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">联系我们</h2>
                    <p class="section-subtitle">我们很乐意听到您的声音</p>
                </div>

                <div class="contact-content">
                    <div class="contact-info">
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="contact-details">
                                <h4>邮箱联系</h4>
                                <p><EMAIL></p>
                            </div>
                        </div>

                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-comments"></i>
                            </div>
                            <div class="contact-details">
                                <h4>在线客服</h4>
                                <p>工作日 9:00-18:00</p>
                            </div>
                        </div>

                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fab fa-weixin"></i>
                            </div>
                            <div class="contact-details">
                                <h4>微信客服</h4>
                                <p>BD6JDO</p>
                            </div>
                        </div>
                    </div>

                    <div class="contact-form">
                        <form class="feedback-form">
                            <div class="form-group">
                                <label for="name">姓名</label>
                                <input type="text" id="name" name="name" required>
                            </div>

                            <div class="form-group">
                                <label for="email">邮箱</label>
                                <input type="email" id="email" name="email" required>
                            </div>

                            <div class="form-group">
                                <label for="subject">主题</label>
                                <select id="subject" name="subject" required>
                                    <option value="">请选择主题</option>
                                    <option value="bug">Bug反馈</option>
                                    <option value="feature">功能建议</option>
                                    <option value="support">技术支持</option>
                                    <option value="other">其他</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="message">消息内容</label>
                                <textarea id="message" name="message" rows="5" required></textarea>
                            </div>

                            <button type="submit" class="btn btn-primary">发送消息</button>
                        </form>
                    </div>
                </div>
            </div>
        </section>

        <!-- Download Section -->
        <section id="download" class="download">
            <div class="container">
                <div class="download-content">
                    <div class="download-text">
                        <h2 class="download-title">立即下载 KcalFit</h2>
                        <p class="download-subtitle">专为iOS设计，开始您的健康饮食管理之旅</p>

                        <div class="download-options">
                            <div class="download-primary">
                                <a href="#" class="download-btn ios-btn">
                                    <img src="./assets/images/app-store.png" alt="Download on App Store">
                                </a>
                                <div class="ios-requirements">
                                    <p class="requirements-text">
                                        <i class="fab fa-apple"></i>
                                        系统要求：iOS 12.0 或更高版本
                                    </p>
                                </div>
                            </div>

                            <div class="download-alternative">
                                <div class="qr-section">
                                    <div class="qr-code">
                                        <img src="./assets/images/qr-code.png" alt="扫码下载iOS版">
                                    </div>
                                    <div class="qr-text">
                                        <p class="qr-title">扫码下载</p>
                                        <p class="qr-subtitle">使用相机扫描二维码</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="download-features">
                            <div class="feature-highlight">
                                <i class="fas fa-shield-alt"></i>
                                <span>安全可靠</span>
                            </div>
                            <div class="feature-highlight">
                                <i class="fas fa-mobile-alt"></i>
                                <span>原生iOS</span>
                            </div>
                            <div class="feature-highlight">
                                <i class="fas fa-download"></i>
                                <span>免费下载</span>
                            </div>
                        </div>
                    </div>

                    <div class="download-visual">
                        <div class="app-preview">
                            <div class="phone-frame">
                                <img src="./assets/images/screenshot/app-screenshot-1.png" alt="KcalFit iOS App"
                                    class="app-screen">
                            </div>
                            <div class="floating-elements">
                                <div class="float-card card-download">
                                    <i class="fas fa-star"></i>
                                    <span>4.8分好评</span>
                                </div>
                                <div class="float-card card-users">
                                    <i class="fas fa-users"></i>
                                    <span>10K+用户</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <img src="./assets/images/logo.png" alt="KcalFit Logo">
                        <span>KcalFit</span>
                    </div>
                    <p class="footer-description">您的专属健康饮食管家，科学记录，智能分析，健康生活从这里开始。</p>
                    <div class="social-links">
                        <a href="#" class="social-link"><i class="fab fa-weixin"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-weibo"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-qq"></i></a>
                    </div>
                </div>

                <div class="footer-section">
                    <h4>产品</h4>
                    <ul class="footer-links">
                        <li><a href="#features">功能特性</a></li>
                        <li><a href="#download">下载应用</a></li>
                        <li><a href="#help">使用指南</a></li>
                        <li><a href="#">更新日志</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4>支持</h4>
                    <ul class="footer-links">
                        <li><a href="#help">帮助中心</a></li>
                        <li><a href="#contact">联系我们</a></li>
                        <li><a href="./privacy.html">隐私政策</a></li>
                        <li><a href="./terms.html">用户协议</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4>关于</h4>
                    <ul class="footer-links">
                        <li><a href="#about">关于我们</a></li>
                        <li><a href="#">加入我们</a></li>
                        <li><a href="#">媒体报道</a></li>
                        <li><a href="#">合作伙伴</a></li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="footer-copyright">
                    <p>&copy; 2025 KcalFit. 保留所有权利。</p>
                </div>
                <div class="footer-version">
                    <p>当前版本：v1.1.0</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Umami Analytics -->
    <script defer src="https://cloud.umami.is/script.js"
        data-website-id="19d54868-270a-4d87-99eb-e14ffdc592fe"></script>

    <!-- Scripts -->
    <script src="./assets/js/main.js"></script>
</body>

</html>
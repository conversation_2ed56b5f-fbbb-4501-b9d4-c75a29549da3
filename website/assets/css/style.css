/* ==================== 基础样式 ==================== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* ==================== 通用组件 ==================== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.btn-primary {
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
}

.btn-secondary {
    background: transparent;
    color: #2196F3;
    border: 2px solid #2196F3;
}

.btn-secondary:hover {
    background: #2196F3;
    color: white;
    transform: translateY(-2px);
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 16px;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.2rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* ==================== 导航栏 ==================== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.5rem;
    font-weight: 700;
    color: #2196F3;
    text-decoration: none;
}

.logo-img {
    width: 32px;
    height: 32px;
    border-radius: 6px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 32px;
    align-items: center;
}

.nav-link {
    color: #333;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: #2196F3;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: #2196F3;
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.download-btn {
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
    color: white !important;
    padding: 10px 20px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.download-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.download-btn::after {
    display: none;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.bar {
    width: 25px;
    height: 3px;
    background: #333;
    transition: 0.3s;
    border-radius: 2px;
}

/* ==================== 英雄区域 ==================== */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    overflow: hidden;
}

.hero-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    min-height: 80vh;
}

.hero-text {
    animation: fadeInUp 1s ease-out;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 24px;
    color: #1a1a1a;
}

.highlight {
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 32px;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 16px;
    margin-bottom: 48px;
    flex-wrap: wrap;
}

.hero-stats {
    display: flex;
    gap: 32px;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #2196F3;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
}

.hero-image {
    position: relative;
    animation: fadeInRight 1s ease-out 0.3s both;
}

.phone-mockup {
    position: relative;
    max-width: 400px;
    margin: 0 auto;
}

.app-screenshot {
    width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.floating-card {
    position: absolute;
    background: white;
    padding: 12px 16px;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    animation: float 3s ease-in-out infinite;
}

.floating-card i {
    color: #2196F3;
}

.card-1 {
    top: 20%;
    left: -20%;
    animation-delay: 0s;
}

.card-2 {
    top: 50%;
    right: -25%;
    animation-delay: 1s;
}

.card-3 {
    bottom: 20%;
    left: -15%;
    animation-delay: 2s;
}

.hero-bg-shapes {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    overflow: hidden;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.1) 0%, rgba(25, 118, 210, 0.1) 100%);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    right: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    bottom: 20%;
    left: 5%;
    animation-delay: 2s;
}

.shape-3 {
    width: 100px;
    height: 100px;
    top: 60%;
    right: 20%;
    animation-delay: 4s;
}

/* ==================== 动画 ==================== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(-10px);
    }
}

/* ==================== 功能特性区域 ==================== */
.features {
    padding: 100px 0;
    background: #fff;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.feature-card {
    background: white;
    padding: 40px 30px;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    font-size: 2rem;
    color: white;
}

.feature-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 16px;
    color: #1a1a1a;
}

.feature-description {
    color: #666;
    margin-bottom: 24px;
    line-height: 1.6;
}

.feature-list {
    list-style: none;
    text-align: left;
}

.feature-list li {
    padding: 8px 0;
    color: #555;
    position: relative;
    padding-left: 24px;
}

.feature-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #2196F3;
    font-weight: bold;
}

/* ==================== 使用步骤区域 ==================== */
.how-it-works {
    padding: 120px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
    overflow: hidden;
}

.how-it-works::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="%23e0e7ff" opacity="0.3"/></svg>') repeat;
    background-size: 50px 50px;
    z-index: 1;
}

.steps-container {
    display: grid;
    gap: 80px;
    margin-top: 80px;
    position: relative;
    z-index: 2;
}

.step-item {
    display: grid;
    grid-template-columns: 80px 2fr 300px;
    gap: 40px;
    align-items: center;
    background: white;
    padding: 50px 40px;
    border-radius: 20px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
}

.step-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.02) 0%, rgba(25, 118, 210, 0.02) 100%);
    border-radius: 20px;
    z-index: -1;
}

.step-item:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.12);
}

.step-item:hover::before {
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.05) 0%, rgba(25, 118, 210, 0.05) 100%);
}

/* 移除偶数行的特殊布局，保持所有步骤一致 */

.step-number {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: 700;
    color: white;
    position: relative;
    box-shadow: 0 10px 25px rgba(33, 150, 243, 0.3);
    transition: all 0.3s ease;
}

.step-number::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.2) 0%, rgba(25, 118, 210, 0.2) 100%);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.step-item:hover .step-number::before {
    opacity: 1;
}

.step-content {
    min-width: 0;
    padding: 0 20px;
}

.step-content h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: #1a1a1a;
    word-wrap: break-word;
    line-height: 1.3;
    position: relative;
}

.step-content h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
    border-radius: 2px;
}

.step-content p {
    color: #666;
    line-height: 1.8;
    word-wrap: break-word;
    font-size: 1.1rem;
    margin: 0;
}

.step-image {
    position: relative;
    overflow: hidden;
    border-radius: 16px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.step-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.1) 0%, rgba(25, 118, 210, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.step-item:hover .step-image::before {
    opacity: 1;
}

.step-image img {
    width: 100%;
    height: auto;
    border-radius: 16px;
    transition: transform 0.3s ease;
    display: block;
}

.step-item:hover .step-image img {
    transform: scale(1.05);
}

/* ==================== 应用截图区域 ==================== */
.screenshots {
    padding: 100px 0;
    background: #fff;
}

.screenshots-slider {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.screenshot-item {
    text-align: center;
    transition: all 0.3s ease;
}

.screenshot-item:hover {
    transform: translateY(-10px);
}

.screenshot-item img {
    width: 100%;
    max-width: 250px;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    margin-bottom: 16px;
    transition: all 0.3s ease;
}

.screenshot-item:hover img {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.screenshot-item h4 {
    font-size: 1.1rem;
    font-weight: 500;
    color: #333;
}

/* ==================== 关于我们区域 ==================== */
.about {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-description {
    margin-bottom: 40px;
}

.about-description p {
    color: #666;
    line-height: 1.8;
    margin-bottom: 20px;
}

.team-info {
    display: grid;
    gap: 24px;
}

.team-item {
    display: flex;
    align-items: center;
    gap: 20px;
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.team-item:hover {
    transform: translateX(10px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.team-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.team-content h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: #1a1a1a;
}

.team-content p {
    color: #666;
    line-height: 1.5;
}

.about-image img {
    width: 100%;
    height: auto;
    border-radius: 16px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

/* ==================== 帮助中心区域 ==================== */
.help {
    padding: 100px 0;
    background: #fff;
}

.help-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;
    margin-top: 60px;
}

.faq-list {
    display: grid;
    gap: 20px;
}

.faq-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.faq-question {
    padding: 24px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.faq-question:hover {
    background: #e9ecef;
}

.faq-question h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0;
}

.faq-question i {
    color: #2196F3;
    transition: transform 0.3s ease;
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 24px;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 24px;
    max-height: 200px;
}

.faq-answer p {
    color: #666;
    line-height: 1.6;
    margin: 0;
}

.help-contact {
    display: flex;
    align-items: flex-start;
}

.help-card {
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
    color: white;
    padding: 40px 30px;
    border-radius: 16px;
    text-align: center;
    width: 100%;
}

.help-card i {
    font-size: 3rem;
    margin-bottom: 20px;
    opacity: 0.9;
}

.help-card h4 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 16px;
}

.help-card p {
    margin-bottom: 24px;
    opacity: 0.9;
    line-height: 1.6;
}

.help-card .btn {
    background: white;
    color: #2196F3;
    border: none;
}

.help-card .btn:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
}

/* ==================== 联系我们区域 ==================== */
.contact {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    margin-top: 60px;
}

.contact-info {
    display: grid;
    gap: 30px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 20px;
    background: white;
    padding: 30px;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.contact-details h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: #1a1a1a;
}

.contact-details p {
    color: #666;
    margin: 0;
}

.contact-form {
    background: white;
    padding: 40px;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.form-group {
    margin-bottom: 24px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #2196F3;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* ==================== 下载区域 ==================== */
.download {
    padding: 120px 0;
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.download::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.download-content {
    display: grid;
    grid-template-columns: 1.2fr 1fr;
    gap: 80px;
    align-items: center;
    position: relative;
    z-index: 1;
}

.download-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
}

.download-subtitle {
    font-size: 1.3rem;
    margin-bottom: 50px;
    opacity: 0.9;
    line-height: 1.5;
}

/* 下载选项布局 */
.download-options {
    display: flex;
    gap: 40px;
    margin-bottom: 40px;
    align-items: flex-start;
}

.download-primary {
    flex: 1;
}

.download-alternative {
    flex: 1;
}

.download-btn img {
    height: 70px;
    width: auto;
    transition: all 0.3s ease;
    filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.2));
}

.download-btn:hover img {
    transform: scale(1.05) translateY(-2px);
    filter: drop-shadow(0 8px 20px rgba(0, 0, 0, 0.3));
}

.ios-requirements {
    margin-top: 15px;
}

.requirements-text {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.95rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 12px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    width: fit-content;
    margin: 0;
    color: rgba(255, 255, 255, 0.9);
}

.requirements-text i {
    font-size: 1.1rem;
}

/* 二维码区域 */
.qr-section {
    display: flex;
    align-items: center;
    gap: 15px;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 16px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.qr-code img {
    width: 80px;
    height: 80px;
    border-radius: 12px;
    background: white;
    padding: 8px;
    flex-shrink: 0;
}

.qr-text {
    flex: 1;
}

.qr-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 4px;
    margin: 0;
}

.qr-subtitle {
    font-size: 0.85rem;
    opacity: 0.8;
    margin: 0;
}

/* 功能亮点 */
.download-features {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
}

.feature-highlight {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    opacity: 0.9;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 16px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.feature-highlight:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.feature-highlight i {
    font-size: 1rem;
}

/* 应用预览区域 */
.download-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.app-preview {
    position: relative;
    max-width: 350px;
}

.phone-frame {
    position: relative;
    background: linear-gradient(145deg, #f0f0f0, #ffffff);
    border-radius: 30px;
    padding: 20px;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.2),
        0 0 0 8px rgba(255, 255, 255, 0.1),
        inset 0 2px 4px rgba(255, 255, 255, 0.8);
}

.phone-frame::before {
    content: '';
    position: absolute;
    top: 8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: #333;
    border-radius: 2px;
}

.app-screen {
    width: 100%;
    height: auto;
    border-radius: 20px;
    display: block;
}

/* 浮动元素 */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.float-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    color: #333;
    padding: 12px 16px;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.85rem;
    font-weight: 600;
    animation: float 3s ease-in-out infinite;
}

.card-download {
    top: 20%;
    right: -20px;
    animation-delay: 0s;
}

.card-users {
    bottom: 25%;
    left: -25px;
    animation-delay: 1.5s;
}

.float-card i {
    color: #2196F3;
    font-size: 1rem;
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(-10px);
    }
}

/* ==================== 页脚区域 ==================== */
.footer {
    background: #1a1a1a;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 40px;
    margin-bottom: 40px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.footer-logo img {
    width: 32px;
    height: 32px;
    border-radius: 6px;
}

.footer-description {
    color: #ccc;
    line-height: 1.6;
    margin-bottom: 24px;
}

.social-links {
    display: flex;
    gap: 16px;
}

.social-link {
    width: 40px;
    height: 40px;
    background: #333;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: #2196F3;
    transform: translateY(-2px);
}

.footer-section h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: white;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 12px;
}

.footer-links a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #2196F3;
}

.footer-bottom {
    border-top: 1px solid #333;
    padding-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.footer-copyright,
.footer-version {
    color: #999;
    font-size: 0.9rem;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 1200px) {
    .step-item {
        grid-template-columns: 80px 1.5fr 280px;
        gap: 30px;
        padding: 40px 30px;
    }

    .steps-container {
        gap: 60px;
    }
}

@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .hero-title {
        font-size: 3rem;
    }

    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }

    .step-item {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 40px;
        padding: 40px 30px;
    }

    /* 移动端保持一致布局 */

    .step-content {
        padding: 0;
    }

    .step-content h3 {
        font-size: 1.6rem;
    }

    .step-number {
        width: 70px;
        height: 70px;
        font-size: 1.6rem;
        margin: 0 auto;
    }

    .steps-container {
        gap: 50px;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .help-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .download-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .download-title {
        font-size: 2.5rem;
    }

    .download-options {
        flex-direction: column;
        gap: 30px;
    }

    .qr-section {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .download-features {
        justify-content: center;
        gap: 20px;
    }

    .app-preview {
        max-width: 280px;
    }

    .float-card {
        position: static;
        margin: 10px auto;
        animation: none;
    }

    .floating-elements {
        position: static;
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-top: 20px;
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 20px 0;
        gap: 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-item {
        margin: 16px 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active .bar:nth-child(1) {
        transform: translateY(7px) rotate(45deg);
    }

    .hamburger.active .bar:nth-child(3) {
        transform: translateY(-7px) rotate(-45deg);
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-stats {
        justify-content: center;
    }

    .section-title {
        font-size: 2rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .screenshots-slider {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 16px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .feature-card {
        padding: 30px 20px;
    }

    .step-item {
        padding: 30px 20px;
        gap: 30px;
    }

    .step-content h3 {
        font-size: 1.4rem;
    }

    .step-content p {
        font-size: 1rem;
    }

    .step-number {
        width: 60px;
        height: 60px;
        font-size: 1.4rem;
    }

    .steps-container {
        gap: 40px;
    }

    .screenshots-slider {
        grid-template-columns: 1fr;
    }

    .download {
        padding: 80px 0;
    }

    .download-title {
        font-size: 2rem;
    }

    .download-subtitle {
        font-size: 1.1rem;
    }

    .download-options {
        gap: 20px;
    }

    .qr-section {
        padding: 15px;
    }

    .qr-code img {
        width: 60px;
        height: 60px;
    }

    .download-features {
        gap: 15px;
    }

    .feature-highlight {
        font-size: 0.8rem;
        padding: 6px 12px;
    }

    .app-preview {
        max-width: 240px;
    }

    .phone-frame {
        padding: 15px;
        border-radius: 25px;
    }

    .float-card {
        font-size: 0.75rem;
        padding: 8px 12px;
    }

    .contact-form {
        padding: 30px 20px;
    }
}

/* ==================== 工具类 ==================== */
.text-center {
    text-align: center;
}

.mb-0 {
    margin-bottom: 0;
}

.mb-1 {
    margin-bottom: 8px;
}

.mb-2 {
    margin-bottom: 16px;
}

.mb-3 {
    margin-bottom: 24px;
}

.mb-4 {
    margin-bottom: 32px;
}

.mb-5 {
    margin-bottom: 40px;
}

.mt-0 {
    margin-top: 0;
}

.mt-1 {
    margin-top: 8px;
}

.mt-2 {
    margin-top: 16px;
}

.mt-3 {
    margin-top: 24px;
}

.mt-4 {
    margin-top: 32px;
}

.mt-5 {
    margin-top: 40px;
}

/* ==================== 滚动条样式 ==================== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #2196F3;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #1976D2;
}
# 图片资源说明

这个目录包含了iOS版KcalFit网站所需的所有图片资源。由于实际图片文件较大，这里提供了所需图片的详细说明和建议规格。

## 📸 必需的图片文件

### 品牌相关
- **logo.png** (32x32px) - 网站Logo，建议使用PNG格式，透明背景
- **favicon.ico** (16x16px, 32x32px) - 网站图标，ICO格式
- **apple-touch-icon.png** (180x180px) - iOS设备图标

### iOS应用截图
- **app-screenshot-1.png** (400x800px) - 主要iOS应用截图，用于英雄区域
- **screenshot-1.png** (250x500px) - iOS首页界面截图
- **screenshot-2.png** (250x500px) - iOS饮食记录界面截图
- **screenshot-3.png** (250x500px) - iOS数据分析界面截图
- **screenshot-4.png** (250x500px) - iOS个人中心界面截图

### 功能说明图片
- **step-1.png** (300x200px) - 完善个人信息步骤图
- **step-2.png** (300x200px) - 记录饮食步骤图
- **step-3.png** (300x200px) - 查看分析报告步骤图

### 其他图片
- **about-us.png** (500x400px) - 关于我们页面配图
- **app-mockup.png** (400x600px) - iOS应用模型图，用于下载区域
- **qr-code.png** (120x120px) - iOS下载二维码

### 下载按钮
- **app-store.png** (135x40px) - App Store下载按钮

### 社交媒体
- **og-image.jpg** (1200x630px) - 社交媒体分享图片

## 🎨 设计建议

### 色彩规范
- **主色调**：#2196F3 (蓝色)
- **辅助色**：#1976D2 (深蓝色)
- **背景色**：#F8F9FA (浅灰色)
- **文字色**：#333333 (深灰色)

### 设计风格
- **简洁现代**：采用扁平化设计风格
- **圆角元素**：使用8-16px的圆角
- **阴影效果**：使用柔和的阴影增加层次感
- **一致性**：保持所有图片的设计风格一致

### 图片质量要求
- **分辨率**：至少2倍分辨率，支持高清屏幕
- **格式**：PNG用于透明背景，JPG用于照片
- **压缩**：在保证质量的前提下适当压缩文件大小
- **命名**：使用有意义的文件名，便于维护

## 📱 应用截图建议

### iOS截图内容
1. **首页界面** - 展示iOS应用主要功能入口和数据概览
2. **饮食记录** - 展示iOS记录界面和语音输入功能
3. **数据分析** - 展示iOS图表和营养分析结果
4. **个人中心** - 展示iOS用户信息和设置选项

### iOS截图技巧
- 使用iPhone真实数据，避免空白状态
- 选择最佳的iOS界面状态进行截图
- 确保iOS界面文字清晰可读
- 统一使用iPhone设备型号和屏幕比例
- 建议使用iPhone 12/13/14系列的屏幕比例

## 🔄 图片替换步骤

1. **准备图片**：按照上述规格准备所有图片
2. **命名文件**：使用指定的文件名
3. **上传文件**：将图片放入此目录
4. **测试显示**：检查网站中图片显示是否正常
5. **优化压缩**：使用工具压缩图片大小

## 🛠️ 推荐工具

### 图片编辑
- **Figma** - 界面设计和原型制作
- **Sketch** - Mac平台的设计工具
- **Adobe Photoshop** - 专业图片编辑
- **Canva** - 在线设计工具

### 图片优化
- **TinyPNG** - 在线PNG压缩
- **ImageOptim** - Mac平台图片优化
- **Squoosh** - Google的在线图片压缩工具

### iOS截图工具
- **iOS模拟器** - 获取iOS应用截图
- **Xcode Simulator** - 官方iOS模拟器
- **iPhone设备** - 直接在真机上截图
- **CleanMyMac** - Mac平台截图工具

## 📋 检查清单

在上传图片前，请确认：

- [ ] 所有必需的图片文件都已准备
- [ ] 图片尺寸符合规格要求
- [ ] 图片质量清晰，无模糊或失真
- [ ] 文件名与代码中的引用一致
- [ ] 图片风格与品牌形象一致
- [ ] 文件大小已优化，加载速度快

## 🚨 注意事项

1. **版权问题**：确保所有图片都有使用权限
2. **文件大小**：单个图片文件不超过500KB
3. **格式选择**：Logo使用PNG，照片使用JPG
4. **备份保存**：保留原始高分辨率文件作为备份
5. **定期更新**：随着应用更新及时更新截图

## 📞 需要帮助？

如果您在准备图片资源时遇到问题，请联系：
- **设计支持**：<EMAIL>
- **技术支持**：<EMAIL>

---

**提示**：您可以先使用占位符图片测试网站功能，然后逐步替换为实际的产品图片。

# KcalFit 产品官网

这是 KcalFit 智能饮食管理应用的官方产品网站，专为iOS平台设计，采用现代化的设计风格，提供完整的产品介绍和用户服务。

## 🌟 网站特色

### 设计特点
- **现代化设计**：简洁美观的界面，符合当前设计趋势
- **响应式布局**：完美适配桌面、平板和手机设备
- **流畅动画**：精心设计的交互动画，提升用户体验
- **蓝色主题**：与应用保持一致的品牌色彩

### 功能亮点
- **智能导航**：固定导航栏，滚动时自动高亮当前区域
- **产品展示**：全面展示应用的核心功能和特色
- **用户引导**：清晰的使用步骤说明
- **FAQ系统**：可展开的常见问题解答
- **联系表单**：完整的用户反馈系统
- **法律页面**：完善的隐私政策和用户协议

## 📁 项目结构

```
website/
├── index.html              # 主页
├── privacy.html            # 隐私政策页面
├── terms.html              # 用户协议页面
├── README.md               # 项目说明文档
├── assets/                 # 静态资源目录
│   ├── css/
│   │   └── style.css       # 主样式文件
│   ├── js/
│   │   └── main.js         # 主JavaScript文件
│   └── images/             # 图片资源目录
│       ├── logo.png        # 网站Logo
│       ├── favicon.ico     # 网站图标
│       ├── app-screenshot-1.png  # 应用截图
│       ├── step-1.png      # 使用步骤图片
│       ├── step-2.png
│       ├── step-3.png
│       ├── screenshot-1.png # 应用界面截图
│       ├── screenshot-2.png
│       ├── screenshot-3.png
│       ├── screenshot-4.png
│       ├── about-us.png    # 关于我们图片
│       ├── app-mockup.png  # 应用模型图
│       ├── app-store.png   # App Store下载按钮
│       ├── qr-code.png     # iOS下载二维码
│       ├── og-image.jpg    # 社交媒体分享图片
│       └── apple-touch-icon.png # iOS图标
```

## 🎨 页面结构

### 主页 (index.html)
1. **导航栏** - 固定顶部导航，包含Logo和主要页面链接
2. **英雄区域** - 产品主要卖点展示，包含统计数据和浮动卡片
3. **功能特性** - 三大核心功能详细介绍
4. **使用步骤** - 三步使用指南，图文并茂
5. **应用截图** - 应用界面展示
6. **关于我们** - 团队介绍和产品理念
7. **帮助中心** - 常见问题FAQ
8. **联系我们** - 联系方式和反馈表单
9. **下载区域** - 应用下载链接和二维码
10. **页脚** - 网站地图和版权信息

### 隐私政策页面 (privacy.html)
- 详细的隐私保护说明
- 信息收集、使用、存储和共享政策
- 用户权利说明
- 联系方式

### 用户协议页面 (terms.html)
- 完整的服务条款
- 用户责任和权利
- 免责声明
- 争议解决机制

## 🛠️ 技术栈

### 前端技术
- **HTML5** - 语义化标记
- **CSS3** - 现代样式，包含Grid和Flexbox布局
- **JavaScript (ES6+)** - 原生JavaScript，无框架依赖
- **Font Awesome** - 图标库
- **Google Fonts** - Inter字体

### 特色功能
- **响应式设计** - 移动优先的设计理念
- **CSS Grid & Flexbox** - 现代布局技术
- **Intersection Observer** - 滚动动画优化
- **CSS动画** - 流畅的过渡效果
- **表单验证** - 客户端表单验证
- **SEO优化** - 完整的meta标签和结构化数据

## 🚀 部署说明

### 本地开发
1. 克隆项目到本地
2. 使用任何HTTP服务器运行（如Live Server、Python SimpleHTTPServer等）
3. 访问 `http://localhost:端口号` 查看网站

### 生产部署
1. 将整个 `website` 目录上传到Web服务器
2. 确保服务器支持静态文件服务
3. 配置域名指向网站根目录
4. 建议启用HTTPS和Gzip压缩

### 推荐服务器配置
- **Nginx** - 高性能静态文件服务
- **Apache** - 传统Web服务器
- **CDN** - 建议使用CDN加速静态资源

## 📱 响应式断点

```css
/* 桌面端 */
@media (min-width: 1025px) { ... }

/* 平板端 */
@media (max-width: 1024px) { ... }

/* 手机端 */
@media (max-width: 768px) { ... }

/* 小屏手机 */
@media (max-width: 480px) { ... }
```

## 🎯 SEO优化

### 已实现的SEO特性
- ✅ 语义化HTML结构
- ✅ 完整的meta标签
- ✅ Open Graph标签（社交媒体分享）
- ✅ Twitter Card标签
- ✅ 结构化数据标记
- ✅ 图片alt属性
- ✅ 页面标题优化
- ✅ 内部链接结构

### 建议的SEO改进
- 添加sitemap.xml
- 添加robots.txt
- 实现结构化数据（JSON-LD）
- 添加面包屑导航
- 优化页面加载速度

## 🔧 自定义配置

### 修改品牌信息
1. 更新 `assets/images/` 目录中的Logo和图标
2. 修改HTML文件中的品牌名称和描述
3. 更新CSS中的品牌色彩变量

### 添加新页面
1. 创建新的HTML文件
2. 在导航栏中添加链接
3. 更新页脚的网站地图

### 修改样式
1. 主要样式在 `assets/css/style.css`
2. 使用CSS变量便于主题定制
3. 响应式断点已预设，可根据需要调整

## 📊 性能优化

### 已实现的优化
- ✅ 图片懒加载
- ✅ CSS和JS文件压缩
- ✅ 关键资源预加载
- ✅ 滚动事件节流
- ✅ 动画性能优化

### 建议的优化
- 图片格式优化（WebP）
- 启用浏览器缓存
- 使用CDN加速
- 代码分割和按需加载

## 🐛 浏览器兼容性

### 支持的浏览器
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ iOS Safari 12+
- ✅ iPad Safari 12+

### 不支持的特性
- ❌ Internet Explorer
- ❌ 旧版本移动浏览器

## 📞 技术支持

如果您在使用过程中遇到任何问题，请通过以下方式联系我们：

- **邮箱**：<EMAIL>
- **技术支持**：<EMAIL>
- **GitHub Issues**：[项目地址]

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

**KcalFit Team** - 让健康饮食管理更简单！

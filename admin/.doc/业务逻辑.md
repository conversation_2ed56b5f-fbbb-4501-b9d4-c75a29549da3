# 自定义消息推送-饮食分析结果
>*models.PushSendParams -> *push.SendParam
1.消息消费者收到消息后进行分析，分析后调用消息推送服务
```go
consumer.go  -> HandleMessage(){
    //构建消息推送参数对象
    msg := &models.PushSendParams{
		AnalyzeID:      message.AnalyzeID,
		RegistrationID: analysisData.RegistrationID,
		Content:        analysisData.Description,
		Extras:         analysisData.Result,
		Title:          message.AnalyzeID,
	}
	//调用消息推送服务
	service.JpushCustomSend(msg)  
}
```
2.构建消息推送参数对象，进行消息推送
```go
// 构建完整消息推送参数对象
param, err := buildCustomSendParams(msgParam, config)
// 调用消息推送服务
result, err := pushAPIv3.CustomSend(context.Background(), param)
```
# 饮食记录APP应用服务端




## 运行项目
```go
go mod tidy // 安装依赖or更新依赖

go run cmd/main.go // 运行项目-直接运行

go build -o bin/server cmd/main.go // 编译项目-编译后运行
./bin/server // 运行项目-编译后运行 

docker build -t kcal_fit . // 编译镜像
docker save -o build/kcal_fit.tar kcal_fit // 保存镜像

docker build -t 15565061502/kcalfit-admin:1.0.0 .
docker save -o build/kcal_fit.tar 15565061502/kcalfit-admin:1.0.0// 保存镜像
# 2. 登录 Docker Hub（如果未登录）
docker login -u 15565061502 -p ************************************
# 3. 推送镜像
docker push 15565061502/huiziqin:latest
docker pull 15565061502/huiziqin:latest


# ... existing code ...
docker run -d --name nsqlookupd -p 4160:4160 -p 4161:4161 --network nsq_network nsqio/nsq /nsqlookupd --broadcast-address=nsqlookupd
docker run -d --name nsqd -p 4150:4150 -p 4151:4151 --network nsq_network nsqio/nsq /nsqd --lookupd-tcp-address=nsqlookupd:4160 --broadcast-address=nsqd --data-path=/data
docker run -d --name nsqadmin -p 4171:4171 --network nsq_network nsqio/nsq /nsqadmin --lookupd-http-address=nsqlookupd:4161
# ... existing code ...


```
gin_project/
├── cmd/               # 主应用程序入口
│   └── main.go        # 主程序入口文件
├── internal/          # 私有应用程序代码
│   ├── controllers/   # 控制器层
│   ├── models/        # 数据模型
│   ├── repositories/  # 数据访问层
│   ├── services/      # 业务逻辑层
│   └── middleware/    # 中间件
├── pkg/               # 可复用的公共代码
├── configs/           # 配置文件
├── api/               # API定义文件(Swagger/OpenAPI)
├── scripts/           # 构建和安装脚本
├── test/              # 测试文件
├── go.mod             # Go模块定义
└── go.sum             # 依赖校验文件
```
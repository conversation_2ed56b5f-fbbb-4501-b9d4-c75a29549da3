# 第一阶段：构建阶段
FROM --platform=linux/amd64 golang:1.24-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制 go.mod 和 go.sum 文件
COPY go.mod go.sum ./

# 下载依赖前设置国内代理
RUN go env -w GOPROXY=https://goproxy.cn,direct
RUN go mod download

# 复制项目文件（包括配置文件）
COPY . .

# 构建应用(指定 main.go 路径)
RUN go build -o main ./cmd

# 第二阶段：运行阶段
FROM --platform=linux/amd64 alpine:latest

# 设置工作目录
WORKDIR /app

# 从构建阶段复制构建好的二进制文件和配置文件
COPY --from=builder /app/main .
COPY --from=builder /app/app_config_dev.yaml .
COPY --from=builder /app/app_config_prod.yaml .
COPY --from=builder /app/app_config_test.yaml .
COPY --from=builder /app/prompt/ ./prompt/


# 设置生产环境变量
ENV APP_ENV=prod
# 暴露端口
EXPOSE 8080

# 运行应用
CMD ["./main"]

package nsqRole

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strings"

	"github.com/go-redis/redis/v8"
	"github.com/huiziqin/gin_project/internal/config"
	"github.com/huiziqin/gin_project/internal/models"
	"github.com/huiziqin/gin_project/internal/service" // 添加services包导入
	"github.com/nsqio/go-nsq"
)

// 新增结构体用于组织Redis存储的数据
type DietAnalysisResult struct {
	Analysis  string `json:"analysis"`
	DietInfo  string `json:"dietInfo"`
	MessageID string `json:"messageId"`
	Timestamp int64  `json:"timestamp"`
}

type DietAnalysisHandler struct {
	deepseekService *service.DeepseekService
	redisClient     *redis.Client
}

func NewDietAnalysisHandler(deepseekService *service.DeepseekService, redisClient *redis.Client) *DietAnalysisHandler {
	return &DietAnalysisHandler{
		deepseekService: deepseekService,
		redisClient:     redisClient,
	}
}

// 处理NSQ消息
// 解析消息体到结构体 message
// 调用deepseekService.ChatCompletion进行分析
// 存储分析结果到Redis
// 推送分析结果到客户端
func (h *DietAnalysisHandler) HandleMessage(m *nsq.Message) error {
	log.Printf("消息ID: %s 消息: %s", string(m.ID[:]), string(m.Body))

	var message models.AnalysisRequest

	// 解析消息体到结构体 message
	if err := json.Unmarshal(m.Body, &message); err != nil {
		log.Printf("JSON解析失败: %v", err)
		return err
	}

	log.Printf("饮食信息: %s, 话题ID: %s, 注册ID:%s", message.AnalyzeID, message.Description, message.RegistrationID)

	log.Printf("开始分析...")
	prompt, err := config.LoadPromptTemplate(0)
	if err != nil {
		log.Printf("加载提示词模板失败: %v", err)
	}
	response, err := h.deepseekService.ChatCompletion(context.Background(), prompt, message.Description)
	if err != nil {
		log.Printf("处理饮食信息失败: %v", err)
		return err
	}
	// 删除 response 前后的 ```json 和 ``` 标记
	response = strings.TrimPrefix(response, "```json")
	response = strings.TrimSuffix(response, "```")
	log.Printf("分析结果: %s", response)

	var analysisResult models.AnalysisResult
	if err := json.Unmarshal([]byte(response), &analysisResult); err != nil {
		log.Printf("分析结果解析失败: %v", err)
		return err
	}

	//var analysisResult models.AnalysisResult 转 	map[string]interface{}
	var analysisResultMap map[string]interface{}
	data, err := json.Marshal(analysisResult)
	if err != nil {
		log.Printf("将分析结果转换为 JSON 字节数组失败: %v", err)
		return err
	}
	err = json.Unmarshal(data, &analysisResultMap)
	if err != nil {
		log.Printf("将 JSON 字节数组转换为 map[string]interface{} 失败: %v", err)
		return err
	}

	ctx := context.Background()
	analysisData := models.NewAnalysisRedisData(message.AnalyzeID, message.RegistrationID, message.Description, analysisResultMap)

	// 循环处理每个食物信息
	for _, food := range analysisResult.Foods {
		foodKey := fmt.Sprintf("food:%s:%.2f%s", food.Name, food.Quantity.Value, food.Quantity.Unit)
		foodData := models.NewFoodRedisData(food)
		err = h.redisClient.HSet(ctx, foodKey, map[string]interface{}{
			"name":          foodData.Name,
			"quantity":      foodData.Quantity.Value,
			"unit":          foodData.Quantity.Unit,
			"energy":        foodData.Macronutrients.Energy.Value,
			"protein":       foodData.Macronutrients.Protein.Value,
			"carbohydrates": foodData.Macronutrients.Carbohydrates.Value,
			"fat":           foodData.Macronutrients.Fat.Value,
		}).Err()
		if err != nil {
			log.Printf("存储食物信息到Redis失败: %v", err)
			return err
		}
	}
	log.Printf("食物信息已保存到Redis")

	// 创建全局微量元素统计映射
	allMicronutrientTotals := make(map[string]struct {
		Value float64
		Unit  string
	})

	// 创建宏量营养素总计
	macronutrientTotals := struct {
		Energy struct {
			Value float64
			Unit  string
		}
		Protein struct {
			Value float64
			Unit  string
		}
		Carbohydrates struct {
			Value float64
			Unit  string
		}
		Fat struct {
			Value float64
			Unit  string
		}
	}{
		Energy: struct {
			Value float64
			Unit  string
		}{Value: 0, Unit: "kcal"},
		Protein: struct {
			Value float64
			Unit  string
		}{Value: 0, Unit: "g"},
		Carbohydrates: struct {
			Value float64
			Unit  string
		}{Value: 0, Unit: "g"},
		Fat: struct {
			Value float64
			Unit  string
		}{Value: 0, Unit: "g"},
	}

	for _, food := range analysisResult.Foods {
		log.Printf("食物分析详情 - 名称: %s, 数量: %.2f%s, 热量: %.2f%s, 蛋白质: %.2f%s, 碳水: %.2f%s, 脂肪: %.2f%s",
			food.Name,
			food.Quantity.Value,
			food.Quantity.Unit,
			food.Macronutrients.Energy.Value,
			food.Macronutrients.Energy.Unit,
			food.Macronutrients.Protein.Value,
			food.Macronutrients.Protein.Unit,
			food.Macronutrients.Carbohydrates.Value,
			food.Macronutrients.Carbohydrates.Unit,
			food.Macronutrients.Fat.Value,
			food.Macronutrients.Fat.Unit)

		// 累加宏量营养素
		if food.Macronutrients.Energy.Unit == macronutrientTotals.Energy.Unit {
			macronutrientTotals.Energy.Value += food.Macronutrients.Energy.Value
		}
		if food.Macronutrients.Protein.Unit == macronutrientTotals.Protein.Unit {
			macronutrientTotals.Protein.Value += food.Macronutrients.Protein.Value
		}
		if food.Macronutrients.Carbohydrates.Unit == macronutrientTotals.Carbohydrates.Unit {
			macronutrientTotals.Carbohydrates.Value += food.Macronutrients.Carbohydrates.Value
		}
		if food.Macronutrients.Fat.Unit == macronutrientTotals.Fat.Unit {
			macronutrientTotals.Fat.Value += food.Macronutrients.Fat.Value
		}

		// 创建微量元素统计映射
		micronutrientTotals := make(map[string]struct {
			Value float64
			Unit  string
		})

		for _, nutrient := range food.Micronutrients {
			log.Printf("微量营养素 - 名称: %s, 含量: %.2f%s, 每日建议量: %s",
				nutrient.Name,
				nutrient.Value,
				nutrient.Unit,
				nutrient.DailyValue)

			// 累加到食物的微量元素总量
			if total, exists := micronutrientTotals[nutrient.Name]; exists {
				// 确保单位相同才累加
				if total.Unit == nutrient.Unit {
					micronutrientTotals[nutrient.Name] = struct {
						Value float64
						Unit  string
					}{
						Value: total.Value + nutrient.Value,
						Unit:  nutrient.Unit,
					}
				}
			} else {
				micronutrientTotals[nutrient.Name] = struct {
					Value float64
					Unit  string
				}{
					Value: nutrient.Value,
					Unit:  nutrient.Unit,
				}
			}

			// 同时累加到全局微量元素总量
			if total, exists := allMicronutrientTotals[nutrient.Name]; exists {
				// 确保单位相同才累加
				if total.Unit == nutrient.Unit {
					allMicronutrientTotals[nutrient.Name] = struct {
						Value float64
						Unit  string
					}{
						Value: total.Value + nutrient.Value,
						Unit:  nutrient.Unit,
					}
				}
			} else {
				allMicronutrientTotals[nutrient.Name] = struct {
					Value float64
					Unit  string
				}{
					Value: nutrient.Value,
					Unit:  nutrient.Unit,
				}
			}
		}

		// 输出微量元素总和
		log.Printf("食物 %s 的微量元素总和:", food.Name)
		for name, total := range micronutrientTotals {
			log.Printf("  - %s: %.2f%s", name, total.Value, total.Unit)
		}
	}

	// 将全局微量元素总和添加到分析结果中
	micronutrientTotalsMap := make(map[string]map[string]interface{})
	for name, total := range allMicronutrientTotals {
		micronutrientTotalsMap[name] = map[string]interface{}{
			"value": total.Value,
			"unit":  total.Unit,
		}
	}

	// 将宏量营养素总计添加到分析结果中
	macronutrientTotalsMap := map[string]map[string]interface{}{
		"energy": {
			"value": macronutrientTotals.Energy.Value,
			"unit":  macronutrientTotals.Energy.Unit,
		},
		"protein": {
			"value": macronutrientTotals.Protein.Value,
			"unit":  macronutrientTotals.Protein.Unit,
		},
		"carbohydrates": {
			"value": macronutrientTotals.Carbohydrates.Value,
			"unit":  macronutrientTotals.Carbohydrates.Unit,
		},
		"fat": {
			"value": macronutrientTotals.Fat.Value,
			"unit":  macronutrientTotals.Fat.Unit,
		},
	}

	// 将微量元素总和添加到分析结果中
	analysisData.Result["micronutrientTotals"] = micronutrientTotalsMap
	// 将宏量营养素总计添加到分析结果中
	analysisData.Result["macronutrientTotals"] = macronutrientTotalsMap

	// 保存主分析结果
	resultJson, err := json.Marshal(analysisData.Result)
	if err != nil {
		log.Printf("将分析结果转换为 JSON 字符串失败: %v", err)
		return err
	}

	err = h.redisClient.HSet(ctx, "diet_analysis:"+message.AnalyzeID, map[string]interface{}{
		"id":              analysisData.ID,
		"description":     analysisData.Description,
		"result":          string(resultJson),
		"created_at":      analysisData.CreatedAt,
		"registration_id": analysisData.RegistrationID,
	}).Err()
	if err != nil {
		log.Printf("存储到Redis失败: %v", err)
		return err
	}
	log.Printf("分析结果已保存到Redis")

	// 推送分析结果到客户端
	msg := &models.PushSendParams{
		AnalyzeID:      message.AnalyzeID,
		RegistrationID: analysisData.RegistrationID,
		Content:        analysisData.Description,
		Extras:         analysisData.Result,
		Title:          message.AnalyzeID,
	}

	service.JpushCustomSend(msg)

	return nil
}

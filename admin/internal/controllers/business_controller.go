package controllers

import (
	"encoding/json"
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/huiziqin/gin_project/internal/config"
	"github.com/huiziqin/gin_project/internal/models"
	"github.com/huiziqin/gin_project/internal/service"
	"github.com/mitchellh/mapstructure"
)

type BusinessController struct {
	nsqService      *service.NSQService
	redisClient     *redis.Client
	dingTalkService service.DingTalkServiceInterface
}

func NewBusinessController(nsqService *service.NSQService, redisClient *redis.Client, dingTalkService service.DingTalkServiceInterface) *BusinessController {
	return &BusinessController{
		nsqService:      nsqService,
		redisClient:     redisClient,
		dingTalkService: dingTalkService,
	}
}

// @Summary 提交饮食信息
// @Description 接收并发布饮食信息到NSQ
func (c *BusinessController) SubmitDietInfo(ctx *gin.Context) {
	var req models.AnalysisRequest

	// 绑定请求数据到结构体
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 序列化请求数据
	message, err := json.Marshal(req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "请求数据序列化失败"})
		return
	}

	// 发布消息到NSQ 主题为 "diet-analyzed"
	config, err := config.LoadConfig()
	err = c.nsqService.Publish(config.Nsq.Topic, message)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "消息发布失败"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "饮食信息已接收并发布",
		"data":    message,
		"req":     req,
	})
}

// @Summary 获取分析结果
// @Description 根据analyzeId从Redis中获取分析结果
// @Param analyzeId query string true "分析ID"
func (c *BusinessController) GetRedisData(ctx *gin.Context) {
	analyzeId := ctx.Query("analyzeId")
	if analyzeId == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "analyzeId参数不能为空"})
		return
	}

	val, err := c.redisClient.HGetAll(ctx, "diet_analysis:"+analyzeId).Result()
	if err == redis.Nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "未找到对应数据"})
		return
	} else if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 转换为AnalysisRedisData结构体
	var analysisData models.AnalysisRedisData
	if err := mapstructure.Decode(val, &analysisData); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "数据结构转换失败"})
		return
	}

	// 直接使用 analysisData.Result，因为它已经是 map[string]interface{} 类型
	// 这里假设不需要额外的类型转换
	ctx.JSON(http.StatusOK, gin.H{"data": analysisData.Result})
}

// @Summary 提交意见反馈
// @Description 接收用户反馈并发送到钉钉群
func (c *BusinessController) SubmitFeedback(ctx *gin.Context) {
	var req models.FeedbackRequest

	// 绑定请求数据到结构体
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	log.Printf("收到反馈：%+v", req)

	// 发送反馈到钉钉群
	err := c.dingTalkService.SendFeedbackMessage(req.Content, req.Contact, req.DeviceInfo, req.AppVersion)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "反馈发送失败: " + err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "反馈已成功提交，感谢您的宝贵意见！",
	})
}

package controllers

import (
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/huiziqin/gin_project/internal/config"
	"github.com/huiziqin/gin_project/internal/service"
)

type AIController struct {
	deepseekService *service.DeepseekService
}

// NewAIController 创建新的AIController实例
func NewAIController(deepseekService *service.DeepseekService) *AIController {
	return &AIController{
		deepseekService: deepseekService,
	}
}

// Chat 聊天接口 (未使用)
func (c *AIController) Chat(ctx *gin.Context) {
	var req struct {
		Message string `json:"message" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	log.Printf("提交信息: %+v", req.Message)

	response, err := c.deepseekService.ChatCompletion(ctx.Request.Context(), "", req.Message)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"response": response})
}

// StreamChat 流式聊天接口 (未使用)
func (c *AIController) StreamChat(ctx *gin.Context) {
	var req struct {
		Message string `json:"message" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	ctx.Writer.Header().Set("Content-Type", "text/event-stream")
	ctx.Writer.Header().Set("Cache-Control", "no-cache")
	ctx.Writer.Header().Set("Connection", "keep-alive")

	if err := c.deepseekService.StreamChatCompletion(ctx.Request.Context(), "", req.Message, ctx.Writer); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
	}
}

// ***********接口********************************
// AnalysisStreamChat 分析结果流式聊天接口
func (c *AIController) AnalysisDiteStreamChat(ctx *gin.Context) {
	var req struct {
		Message string `json:"message" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	ctx.Writer.Header().Set("Content-Type", "text/event-stream")
	ctx.Writer.Header().Set("Cache-Control", "no-cache")
	ctx.Writer.Header().Set("Connection", "keep-alive")

	prompt, err := config.LoadPromptTemplate(1)
	if err != nil {
		log.Printf("加载提示词模板失败: %v", err)
	}

	if err := c.deepseekService.StreamChatCompletion(ctx.Request.Context(), prompt, req.Message, ctx.Writer); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
	}
}

// AnalysisChat 分析结果聊天接口
func (c *AIController) AnalysisDiteJsonChat(ctx *gin.Context) {
	var req struct {
		Message string `json:"message" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	log.Printf("提交信息: %+v", req.Message)

	prompt, err := config.LoadPromptTemplate(0)
	if err != nil {
		log.Printf("加载提示词模板失败: %v", err)
	}
	response, err := c.deepseekService.ChatCompletion(ctx.Request.Context(), prompt, req.Message)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"response": response})
}

package service

import (
	"context"
	"fmt"
	"sync"

	"github.com/calvinit/jiguang-sdk-go/api/jpush/device/platform"
	"github.com/calvinit/jiguang-sdk-go/api/jpush/push"
	"github.com/calvinit/jiguang-sdk-go/api/jpush/push/notification/alert"
	"github.com/calvinit/jiguang-sdk-go/jiguang"
	"github.com/huiziqin/gin_project/internal/config"
	"github.com/huiziqin/gin_project/internal/models"
)

//推送任务-广播接口(同时向应用所有注册用户推送一条通知消息) 10/天
//推送任务-RID接口(通过极光RID选择单个或多个目标对象，并推送一条通知消息) 共享200万/天
//推送任务-别名接口(通过Alias别名选择所绑定的单个或多个目标对象，并推送一条通知消息) 共享200万/天
//定时任务数量 50个

var cachedConfig *config.Config
var configOnce sync.Once

// GetConfig 加载并缓存配置
func GetConfig() (*config.Config, error) {
	var err error
	configOnce.Do(func() {
		cachedConfig, err = config.LoadConfig()
	})
	return cachedConfig, err
}

// getJPushConfig 统一获取配置和推送 API 实例
func getJPushConfig() (push.APIv3, *config.Config, error) {
	config, err := GetConfig()
	if err != nil {
		return nil, nil, fmt.Errorf("加载配置文件失败: %w", err)
	}

	pushAPIv3, err := push.NewAPIv3Builder().
		SetAppKey(config.Jpush.AppKey).
		SetMasterSecret(config.Jpush.MasterSecret).
		Build()
	if err != nil {
		return nil, nil, fmt.Errorf("构建极光推送 API 实例失败: %w", err)
	}

	return pushAPIv3, config, nil
}

// BuildIOSNotification 构建 iOS 通知内容
func BuildIOSNotification(analysisData *models.AnalysisRedisData) *push.IosNotification {
	return &push.IosNotification{
		Alert: &alert.IosAlert{
			Title:        analysisData.RegistrationID,
			ActionLocKey: "PLAY",
			LocKey:       analysisData.Description,
			LocArgs:      []string{"Jenna", "Frank"},
		},
		Sound: &alert.IosSound{
			Critical: 1,
			Name:     "sound.caf",
			Volume:   1,
		},
		Badge:             jiguang.String("+1"),
		ThreadID:          "default",
		Extras:            map[string]interface{}{"newsid": 321},
		InterruptionLevel: alert.IosInterruptionLevelActive,
	}
}

// BuildPushParams 构建推送参数
func BuildPushParams(analysisData *models.AnalysisRedisData) (*push.SendParam, error) {
	config, err := GetConfig()
	if err != nil {
		return nil, fmt.Errorf("加载配置文件失败: %w", err)
	}

	audience := &push.Audience{
		RegistrationIDs: []string{analysisData.RegistrationID},
	}

	ios := BuildIOSNotification(analysisData)

	options := &push.Options{
		TimeToLive:     jiguang.Int64(int64(config.Jpush.TimeToLive)),
		ApnsProduction: jiguang.Bool(config.Jpush.ApnsProduction),
	}

	return &push.SendParam{
		Platform: platform.IOS,
		Audience: audience,
		Notification: &push.Notification{
			Alert: "Hello, JPush!",
			IOS:   ios,
		},
		Options: options,
		InApp:   &push.InAppMessage{Enabled: config.Jpush.InAppMessageFlag},
	}, nil
}

// 发送极光推送-普通推送
func JpushSend(analysisData *models.AnalysisRedisData) error {
	pushAPIv3, _, err := getJPushConfig()
	if err != nil {
		return err
	}

	fmt.Printf("RegistrationID: %v\n", analysisData.RegistrationID)

	param, err := BuildPushParams(analysisData)
	if err != nil {
		return err
	}

	result, err := pushAPIv3.Send(context.Background(), param)
	return handlePushResult(result, err)
}

// 构建自定义推送参数
func buildCustomSendParams(msgParam *models.PushSendParams, config *config.Config) (*push.SendParam, error) {
	param := &push.SendParam{}
	param.Platform = platform.IOS

	// 目标人群使用传入的 RegistrationID
	audience := &push.Audience{}
	audience.RegistrationIDs = []string{msgParam.RegistrationID}
	param.Audience = audience

	// 自定义消息内容
	message := &push.CustomMessage{}
	message.Content = msgParam.Content
	message.ContentType = "analysisData"
	message.Title = msgParam.AnalyzeID
	message.Extras = msgParam.Extras
	param.CustomMessage = message

	param.Options = &push.Options{
		TimeToLive:     jiguang.Int64(int64(config.Jpush.TimeToLive)),
		ApnsProduction: jiguang.Bool(config.Jpush.ApnsProduction),
	}

	return param, nil
}

// 发送极光推送-自定义推送
// func JpushCustomSend(analysisData *models.AnalysisRedisData) error {
// 	pushAPIv3, config, err := getJPushConfig()
// 	if err != nil {
// 		return err
// 	}

// 	fmt.Printf("RegistrationID: %v\n", analysisData.RegistrationID)

// 	param, err := buildCustomSendParams(analysisData, config)
// 	if err != nil {
// 		return err
// 	}

// 	result, err := pushAPIv3.CustomSend(context.Background(), param)
// 	return handlePushResult(result, err)
// }

// 处理推送结果
func handlePushResult(result *push.SendResult, err error) error {
	if err != nil {
		return fmt.Errorf("发送推送请求失败: %w", err)
	}

	if result.IsSuccess() {
		fmt.Printf("Send success, MsgID: %s, SendNo: %s,Response:%s\n", result.MsgID, result.SendNo, result.Response)
	} else {
		return fmt.Errorf("Send failed: %s", result.Error)
	}

	return nil
}

// 发送极光推送-自定义推送
func JpushCustomSend(msgParam *models.PushSendParams) error {
	pushAPIv3, config, err := getJPushConfig()
	if err != nil {
		return err
	}

	fmt.Printf("推送给设备: %v\n", msgParam.RegistrationID)

	param, err := buildCustomSendParams(msgParam, config)
	if err != nil {
		return err
	}

	result, err := pushAPIv3.CustomSend(context.Background(), param)
	return handlePushResult(result, err)
}

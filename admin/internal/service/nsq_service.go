package service

import (
	"github.com/nsqio/go-nsq"
)

type NSQService struct {
	producer *nsq.Producer
	consumer *nsq.Consumer
}

func NewNSQService(nsqdAddr string) (*NSQService, error) {
	config := nsq.NewConfig()

	// 初始化生产者
	producer, err := nsq.NewProducer(nsqdAddr, config)
	if err != nil {
		return nil, err
	}

	return &NSQService{
		producer: producer,
	}, nil
}

// Publish 发布消息到指定主题
func (s *NSQService) Publish(topic string, body []byte) error {
	return s.producer.Publish(topic, body)
}

func (s *NSQService) StartConsumer(addr, topic, channel string, handler nsq.Handler) error {
	config := nsq.NewConfig()
	consumer, err := nsq.NewConsumer(topic, channel, config)
	if err != nil {
		return err
	}

	consumer.AddHandler(handler)
	err = consumer.ConnectToNSQD(addr)
	if err != nil {
		return err
	}

	s.consumer = consumer
	return nil
}

func (s *NSQService) Stop() {
	if s.producer != nil {
		s.producer.Stop()
	}
	if s.consumer != nil {
		s.consumer.Stop()
	}
}

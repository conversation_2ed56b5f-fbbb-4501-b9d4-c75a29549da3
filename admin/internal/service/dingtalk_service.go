package service

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"time"
)

// DingTalkServiceInterface 钉钉服务接口
type DingTalkServiceInterface interface {
	SendFeedbackMessage(content, contact, deviceInfo, appVersion string) error
}

// DingTalkService 钉钉机器人服务
type DingTalkService struct {
	accessToken string // 机器人访问令牌
	secret      string // 机器人加签密钥
}

// NewDingTalkService 创建钉钉服务实例
func NewDingTalkService(accessToken, secret string) *DingTalkService {
	return &DingTalkService{
		accessToken: accessToken,
		secret:      secret,
	}
}

// DingTalkMessage 钉钉消息结构
type DingTalkMessage struct {
	MsgType string `json:"msgtype"`
	Text    struct {
		Content string `json:"content"`
	} `json:"text"`
	At struct {
		AtMobiles []string `json:"atMobiles,omitempty"`
		IsAtAll   bool     `json:"isAtAll,omitempty"`
	} `json:"at,omitempty"`
}

// DingTalkResponse 钉钉响应结构
type DingTalkResponse struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
}

// generateSign 生成钉钉机器人签名
func (d *DingTalkService) generateSign() (string, string) {
	timestamp := strconv.FormatInt(time.Now().UnixMilli(), 10)
	stringToSign := timestamp + "\n" + d.secret

	h := hmac.New(sha256.New, []byte(d.secret))
	h.Write([]byte(stringToSign))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))

	return timestamp, url.QueryEscape(signature)
}

// SendTextMessage 发送文本消息到钉钉群
func (d *DingTalkService) SendTextMessage(content string) error {
	// 生成签名
	timestamp, sign := d.generateSign()

	// 构建请求URL
	requestURL := fmt.Sprintf("https://oapi.dingtalk.com/robot/send?access_token=%s&timestamp=%s&sign=%s",
		d.accessToken, timestamp, sign)

	// 构建消息体
	message := DingTalkMessage{
		MsgType: "text",
	}
	message.Text.Content = content

	// 序列化消息
	messageBytes, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("序列化消息失败: %w", err)
	}

	// 发送HTTP请求
	resp, err := http.Post(requestURL, "application/json", bytes.NewBuffer(messageBytes))
	if err != nil {
		return fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %w", err)
	}

	// 解析响应
	var dingResp DingTalkResponse
	if err := json.Unmarshal(body, &dingResp); err != nil {
		return fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查响应状态
	if dingResp.ErrCode != 0 {
		return fmt.Errorf("钉钉API返回错误: %s (错误码: %d)", dingResp.ErrMsg, dingResp.ErrCode)
	}

	return nil
}

// SendFeedbackMessage 发送格式化的反馈消息
func (d *DingTalkService) SendFeedbackMessage(content, contact, deviceInfo, appVersion string) error {
	// 构建格式化的反馈消息
	message := fmt.Sprintf("📝 用户意见反馈\n\n")
	message += fmt.Sprintf("反馈内容：\n%s\n\n", content)

	if contact != "" {
		message += fmt.Sprintf("联系方式：%s\n", contact)
	}

	if deviceInfo != "" {
		message += fmt.Sprintf("设备信息：%s\n", deviceInfo)
	}

	if appVersion != "" {
		message += fmt.Sprintf("应用版本：%s\n", appVersion)
	}

	message += fmt.Sprintf("提交时间：%s", time.Now().Format("2006-01-02 15:04:05"))

	return d.SendTextMessage(message)
}

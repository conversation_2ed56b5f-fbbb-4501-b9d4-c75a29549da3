package service

import (
	"context"
	"fmt"
	"io"
	"net/http"

	"github.com/go-deepseek/deepseek"
	"github.com/go-deepseek/deepseek/request"
)

// DeepseekService 实现 ChatService 接口
type DeepseekService struct {
	client deepseek.Client
}

// NewDeepseekService 创建 DeepseekService 实例
func NewDeepseekService(apiKey string) (*DeepseekService, error) {
	client, err := deepseek.NewClient(apiKey)
	if err != nil {
		return nil, fmt.Errorf("failed to create deepseek client: %w", err)
	}
	return &DeepseekService{
		client: client,
		// systemPrompt: systemPrompt,
	}, nil
}

// StreamChatCompletion 实现 StreamChatCompletion 接口 (流式聊天)
func (s *DeepseekService) StreamChatCompletion(ctx context.Context, prompt string, message string, writer io.Writer) error {

	req := &request.ChatCompletionsRequest{
		Model:  deepseek.DEEPSEEK_CHAT_MODEL,
		Stream: true,
		Messages: []*request.Message{
			{
				Role:    "system",
				Content: prompt,
			},
			{
				Role:    "user",
				Content: message,
			},
		},
		StreamOptions: &request.StreamOptions{
			IncludeUsage: true, // 启用使用量统计
		},
	}

	// 获取流式响应
	stream, err := s.client.StreamChatCompletionsChat(ctx, req)
	if err != nil {
		return fmt.Errorf("failed to create stream: %w", err)
	}

	// 设置流式响应头
	if f, ok := writer.(http.Flusher); ok {
		f.Flush()
	}

	// 处理流式响应
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			// 读取流式响应
			resp, err := stream.Read()
			if err != nil {
				if err == io.EOF {
					return nil
				}
				return fmt.Errorf("stream read error: %w", err)
			}

			// 写入响应内容
			if resp.Choices != nil && len(resp.Choices) > 0 && resp.Choices[0].Delta.Content != "" {
				if _, err := writer.Write([]byte(resp.Choices[0].Delta.Content)); err != nil {
					return fmt.Errorf("write error: %w", err)
				}
				if f, ok := writer.(http.Flusher); ok {
					f.Flush()
				}
			}
			if resp.Usage != nil {
				fmt.Printf("Prompt tokens: %d\nCompletion tokens: %d\nTotal tokens: %d\n",
					resp.Usage.PromptTokens,
					resp.Usage.CompletionTokens,
					resp.Usage.TotalTokens)
			}
		}
	}
}

// ChatCompletion 实现 ChatCompletion 接口 (普通聊天)
func (s *DeepseekService) ChatCompletion(ctx context.Context, prompt string, message string) (string, error) {
	req := &request.ChatCompletionsRequest{
		Model:  deepseek.DEEPSEEK_CHAT_MODEL,
		Stream: false,
		Messages: []*request.Message{
			{
				Role:    "system",
				Content: prompt,
			},
			{
				Role:    "user",
				Content: message,
			},
		},
	}

	// 使用正确的接口方法 CallChatCompletionsChat
	resp, err := s.client.CallChatCompletionsChat(ctx, req)
	if err != nil {
		return "", fmt.Errorf("failed to create chat completion: %w", err)
	}

	if len(resp.Choices) == 0 {
		return "", fmt.Errorf("no response from API")
	}
	if resp.Usage != nil {
		fmt.Printf("Prompt tokens: %d\nCompletion tokens: %d\nTotal tokens: %d\n",
			resp.Usage.PromptTokens,
			resp.Usage.CompletionTokens,
			resp.Usage.TotalTokens)
	}
	return resp.Choices[0].Message.Content, nil
}

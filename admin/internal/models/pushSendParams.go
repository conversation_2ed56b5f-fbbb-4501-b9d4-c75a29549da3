// This file was generated from JSON Schema using quicktype, do not modify it directly.
// To parse and unparse this JSON data, add this code to your project and do:
//
//    pushSendParams, err := UnmarshalPushSendParams(bytes)
//    bytes, err = pushSendParams.Marshal()

package models

import "encoding/json"

// UnmarshalPushSendParams 反序列化结构体
func UnmarshalPushSendParams(data []byte) (PushSendParams, error) {
	var r PushSendParams
	err := json.Unmarshal(data, &r)
	return r, err
}

// Marshal 序列化结构体
func (r *PushSendParams) Marshal() ([]byte, error) {
	return json.Marshal(r)
}

// PushSendParams 推送消息参数
type PushSendParams struct {
	Title          string                 `json:"title,omitempty"`        // 【可选】消息标题
	Content        string                 `json:"content"`                // 【必填】消息内容本身
	ContentType    string                 `json:"content_type,omitempty"` // 【可选】消息内容类型，开发者可根据自身业务定义具体类型
	Extras         map[string]interface{} `json:"extras,omitempty"`       // 【可选】可选参数
	RegistrationID string                 `json:"registration_id"`        // 【必填】设备标识 Registration ID
	AnalyzeID      string                 `json:"analyze_id"`             // 【必填】分析标识 AnalyzeID ID
}

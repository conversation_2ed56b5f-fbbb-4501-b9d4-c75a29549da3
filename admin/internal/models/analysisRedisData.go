package models

import (
	"encoding/json"
	"time"
)

type AnalysisRedisData struct {
	ID             string                 `json:"id"`
	Description    string                 `json:"description"`
	Result         map[string]interface{} `json:"result"`
	CreatedAt      int64                  `json:"created_at"`
	RegistrationID string                 `json:"registration_id"`
}

func (a *AnalysisRedisData) MarshalBinary() ([]byte, error) {
	return json.Marshal(a)
}

func NewAnalysisRedisData(analyzeID, registrationID, description string, result map[string]interface{}) *AnalysisRedisData {
	return &AnalysisRedisData{
		ID:             analyzeID,
		RegistrationID: registrationID,
		Description:    description,
		Result:         result,
		CreatedAt:      time.Now().Unix(),
	}
}

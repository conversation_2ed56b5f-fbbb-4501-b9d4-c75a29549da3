package models

import (
	"encoding/json"
	"time"
)

// FoodRedisData 用于存储Redis中的食物数据
type FoodRedisData struct {
	Name           string         `json:"name"`           // 食物名称
	Quantity       Quantity       `json:"quantity"`       // 食物数量信息
	Macronutrients Macronutrients `json:"macronutrients"` // 食物的营养成分
	CreatedAt      int64          `json:"created_at"`     // 创建时间戳
}

// MarshalBinary 实现BinaryMarshaler接口，用于序列化FoodRedisData为二进制数据
func (f *FoodRedisData) MarshalBinary() ([]byte, error) {
	return json.Marshal(f)
}

// NewFoodRedisData 创建新的FoodRedisData实例
func NewFoodRedisData(food Food) *FoodRedisData {
	return &FoodRedisData{
		Name:           food.Name,
		Quantity:       food.Quantity,
		Macronutrients: food.Macronutrients,
		CreatedAt:      time.Now().Unix(),
	}
}

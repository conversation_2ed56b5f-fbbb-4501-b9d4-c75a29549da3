package models

import "encoding/json"

// UnmarshalAnalysisRequest 解析分析请求的JSON数据
func UnmarshalAnalysisRequest(data []byte) (AnalysisRequest, error) {
	var r AnalysisRequest
	err := json.Unmarshal(data, &r)
	return r, err
}

// Marshal 序列化分析请求为JSON数据
func (r *AnalysisRequest) Marshal() ([]byte, error) {
	return json.Marshal(r)
}

// 饮食热量分析请求参数
type AnalysisRequest struct {
	AnalyzeID      string `json:"analyzeId"`      // 分析ID
	Description    string `json:"description"`    // 分析描述
	RegistrationID string `json:"registrationId"` // 极光推送设备注册ID
}

// 意见反馈请求参数
type FeedbackRequest struct {
	Content    string `json:"content" binding:"required"` // 反馈内容
	Contact    string `json:"contact"`                    // 联系方式（可选）
	DeviceInfo string `json:"deviceInfo"`                 // 设备信息（可选）
	AppVersion string `json:"appVersion"`                 // 应用版本（可选）
}

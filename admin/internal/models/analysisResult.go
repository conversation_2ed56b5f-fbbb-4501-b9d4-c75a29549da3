package models

import "encoding/json"

// UnmarshalAnalysisResult 解析分析结果的JSON数据
func UnmarshalAnalysisResult(data []byte) (AnalysisResult, error) {
	var r AnalysisResult
	err := json.Unmarshal(data, &r)
	return r, err
}

// Marshal 序列化分析结果为JSON数据
func (r *AnalysisResult) Marshal() ([]byte, error) {
	return json.Marshal(r)
}

// 饮食热量及元素分析结果 LLMs返回的分析结果）
type AnalysisResult struct {
	Foods   []Food `json:"foods"`   // 食物列表
	Content string `json:"content"` // 分析内容
	Error   string `json:"error"`   // 错误信息
}

// 食物信息
type Food struct {
	Name           string          `json:"name"`           // 食物名称
	Quantity       Quantity        `json:"quantity"`       // 食物数量信息
	Macronutrients Macronutrients  `json:"macronutrients"` // 食物的营养成分
	Micronutrients []Micronutrient `json:"micronutrients"` // 食物的微量成分列表
}

// 食物的营养成分
type Macronutrients struct {
	Energy        Quantity `json:"energy"`        // 食物的能量值
	Protein       Quantity `json:"protein"`       // 食物的蛋白质值
	Carbohydrates Quantity `json:"carbohydrates"` // 食物的碳水化合物值
	Fat           Quantity `json:"fat"`           // 食物的脂肪值
}

// 食物的数量信息
type Quantity struct {
	Value float64 `json:"value"` // 食物数量值
	Unit  string  `json:"unit"`  // 食物数量单位
}

// 食物的微量成分信息
type Micronutrient struct {
	Name       string  `json:"name"`        // 微量成分名称
	Value      float64 `json:"value"`       // 微量成分值
	Unit       string  `json:"unit"`        // 微量成分单位
	DailyValue string  `json:"daily_value"` // 每日推荐值
}

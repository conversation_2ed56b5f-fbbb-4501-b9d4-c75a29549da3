package app

import (
	"github.com/go-redis/redis/v8"
	"github.com/huiziqin/gin_project/internal/config"
	"github.com/huiziqin/gin_project/internal/service"
)

type App struct {
	RedisClient     *redis.Client
	NSQService      *service.NSQService
	DeepseekService *service.DeepseekService
	DingTalkService *service.DingTalkService
}

var appInstance *App

func Initialize() (*App, error) {
	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		return nil, err
	}

	// 初始化Redis
	redisClient := redis.NewClient(&redis.Options{
		Addr:     cfg.Redis.Addr,
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
	})

	// 初始化NSQ服务
	nsqService, err := service.NewNSQService(cfg.Nsq.Addr)
	if err != nil {
		return nil, err
	}

	// 初始化Deepseek服务
	deepseekService, err := service.NewDeepseekService(cfg.Deepseek.APIKey)
	if err != nil {
		return nil, err
	}

	// 初始化钉钉服务
	dingTalkService := service.NewDingTalkService(cfg.DingTalk.AccessToken, cfg.DingTalk.Secret)

	// 初始化App实例
	appInstance = &App{
		RedisClient:     redisClient,
		NSQService:      nsqService,
		DeepseekService: deepseekService,
		DingTalkService: dingTalkService,
	}
	return appInstance, nil
}

// 获取App实例
func GetAppInstance() *App {
	return appInstance
}

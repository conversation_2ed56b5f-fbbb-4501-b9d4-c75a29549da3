package config

import (
	"fmt"
	"log"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

// Config 定义配置文件结构体
type Config struct {
	Deepseek struct {
		APIKey string `yaml:"DEEPSEEK_API_KEY"`
	} `yaml:"deepseek"`
	Redis struct {
		Addr     string `yaml:"ADDR"`
		Password string `yaml:"PASSWORD"`
		DB       int    `yaml:"DB"`
	} `yaml:"redis"`
	Nsq struct {
		Addr    string `yaml:"ADDR"`
		Topic   string `yaml:"TOPIC"`
		Channel string `yaml:"CHANNEL"`
	} `yaml:"nsq"`
	Jpush struct {
		AppKey           string `yaml:"APP_KEY"`
		MasterSecret     string `yaml:"MASTER_SECRET"`
		TimeToLive       int    `yaml:"TIME_TO_LIVE"`
		ApnsProduction   bool   `yaml:"APNS_PRODUCTION"`
		InAppMessageFlag bool   `yaml:"IN_APP_MESSAGE_FLAG"`
	} `yaml:"jpush"`
	Server struct {
		Port string `yaml:"PORT"`
	} `yaml:"server"`
	DingTalk struct {
		AccessToken string `yaml:"ACCESS_TOKEN"`
		Secret      string `yaml:"SECRET"`
	} `yaml:"dingtalk"`
}

// LoadConfig 根据环境加载配置文件
func LoadConfig() (*Config, error) {
	env := os.Getenv("APP_ENV")
	if env == "" {
		env = "dev" // 默认开发环境
		log.Println("未设置APP_ENV环境变量，默认使用开发环境配置")
	}

	configFile := fmt.Sprintf("app_config_%s.yaml", env)
	data, err := os.ReadFile(configFile)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	return &config, nil
}

// 在Go语言里，函数参数需要指定类型，原代码中 'type' 缺少类型定义，这里假设类型为 string
func LoadPromptTemplate(promptType int32) (string, error) {
	promptPath := ""
	data := []byte{}
	err := error(nil)

	rootDir, err := os.Getwd()
	if err != nil {
		return "", err
	}
	if promptType == 0 {
		promptPath = filepath.Join(rootDir, "prompt", "analyze_diet_json.md")
		data, err = os.ReadFile(promptPath)
	}
	if promptType == 1 {
		promptPath = filepath.Join(rootDir, "prompt", "analyze_diet.md")
		data, err = os.ReadFile(promptPath)
	}
	if err != nil {
		return "", err
	}
	return string(data), nil
}

package api

import (
	"github.com/gin-gonic/gin"
	"github.com/huiziqin/gin_project/internal/controllers"
)

func NewServer(aiController *controllers.AIController, businessController *controllers.BusinessController) *gin.Engine {
	r := gin.Default()

	api := r.Group("/api")
	{
		dietInfo := api.Group("/dietInfo") // 饮食信息相关接口
		{
			dietInfo.POST("/submit", businessController.SubmitDietInfo)       // 提交饮食信息
			dietInfo.GET("/getAnalysisData", businessController.GetRedisData) // 获取分析数据
		}

		chat := api.Group("/chat") // 聊天相关接口
		{
			analysisDitediet := chat.Group("/analysisDite") // 分析饮食相关接口
			{
				analysisDitediet.POST("/streamChat", aiController.AnalysisDiteStreamChat) // 流式分析饮食数据
				analysisDitediet.POST("/chat", aiController.AnalysisDiteJsonChat)         // 分析饮食数据
			}

		}
		app := api.Group("/app") // app相关接口
		{
			app.POST("/submitFeedback", businessController.SubmitFeedback) // 提交意见反馈
		}
	}

	return r
}

package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/huiziqin/gin_project/internal/api"
	"github.com/huiziqin/gin_project/internal/app"
	"github.com/huiziqin/gin_project/internal/config"
	"github.com/huiziqin/gin_project/internal/controllers"
	"github.com/huiziqin/gin_project/internal/nsqRole"
)

func main() {
	log.Println("正在启动服务器 初始化...")
	// 初始化应用
	app, err := app.Initialize()
	if err != nil {
		log.Fatalf("初始化应用程序失败: %v", err)
	}
	defer app.NSQService.Stop()
	log.Println("应用初始化成功")

	// 初始化控制器
	aiController := controllers.NewAIController(app.DeepseekService)
	businessController := controllers.NewBusinessController(app.NSQService, app.RedisClient, app.DingTalkService)
	log.Println("控制器初始化成功")

	// 启动NSQ消费者
	if err := startConsumer(app); err != nil {
		log.Fatalf("启动消费者失败: %v", err)
	}
	log.Println("NSQ消费者启动成功")

	// 启动服务器
	server := api.NewServer(aiController, businessController)
	go func() {
		// 从配置中获取端口号
		config, err := config.LoadConfig()
		if err != nil {
			log.Fatalf("加载配置失败: %v", err)
		}
		if err := server.Run(":" + config.Server.Port); err != nil {
			log.Fatalf("启动服务器失败: %v", err)
		}
	}()
	log.Println("服务器启动成功")

	// 优雅关闭
	handleGracefulShutdown()
}

// 启动NSQ消费者
func startConsumer(app *app.App) error {
	config, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}
	dietHandler := nsqRole.NewDietAnalysisHandler(app.DeepseekService, app.RedisClient)
	return app.NSQService.StartConsumer(config.Nsq.Addr, config.Nsq.Topic, config.Nsq.Channel, dietHandler)
}

// 优雅关闭
func handleGracefulShutdown() {
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("关闭服务器...")
	log.Println("服务器已退出")
}

package test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/huiziqin/gin_project/internal/controllers"
	"github.com/huiziqin/gin_project/internal/models"
	"github.com/stretchr/testify/assert"
)

// MockDingTalkService 模拟钉钉服务
type MockDingTalkService struct{}

func (m *MockDingTalkService) SendFeedbackMessage(content, contact, deviceInfo, appVersion string) error {
	// 模拟成功发送
	return nil
}

func TestSubmitFeedback(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建模拟的钉钉服务
	dingTalkService := &MockDingTalkService{}

	// 创建控制器（其他服务可以为nil，因为我们只测试反馈功能）
	businessController := controllers.NewBusinessController(nil, nil, dingTalkService)

	// 创建路由
	router := gin.New()
	api := router.Group("/api")
	app := api.Group("/app")
	app.POST("/submitFeedback", businessController.SubmitFeedback)

	// 准备测试数据
	feedbackReq := models.FeedbackRequest{
		Content:    "这是一个测试反馈",
		Contact:    "<EMAIL>",
		DeviceInfo: "iPhone 14 Pro",
		AppVersion: "1.0.0",
	}

	jsonData, _ := json.Marshal(feedbackReq)

	// 创建HTTP请求
	req, _ := http.NewRequest("POST", "/api/app/submitFeedback", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// 创建响应记录器
	w := httptest.NewRecorder()

	// 执行请求
	router.ServeHTTP(w, req)

	// 验证响应状态码
	assert.Equal(t, http.StatusOK, w.Code)

	// 解析响应
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// 验证响应内容
	assert.Equal(t, "success", response["status"])
	assert.Contains(t, response["message"], "反馈已成功提交")
}

func TestSubmitFeedbackWithMissingContent(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建模拟的钉钉服务
	dingTalkService := &MockDingTalkService{}

	// 创建控制器
	businessController := controllers.NewBusinessController(nil, nil, dingTalkService)

	// 创建路由
	router := gin.New()
	api := router.Group("/api")
	app := api.Group("/app")
	app.POST("/submitFeedback", businessController.SubmitFeedback)

	// 准备测试数据（缺少必需的content字段）
	feedbackReq := models.FeedbackRequest{
		Contact:    "<EMAIL>",
		DeviceInfo: "iPhone 14 Pro",
		AppVersion: "1.0.0",
	}

	jsonData, _ := json.Marshal(feedbackReq)

	// 创建HTTP请求
	req, _ := http.NewRequest("POST", "/api/app/submitFeedback", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// 创建响应记录器
	w := httptest.NewRecorder()

	// 执行请求
	router.ServeHTTP(w, req)

	// 验证响应状态码应该是400（Bad Request）
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

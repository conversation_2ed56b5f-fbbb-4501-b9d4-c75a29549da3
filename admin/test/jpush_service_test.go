package test

import (
	"testing"

	"github.com/huiziqin/gin_project/internal/models"
	"github.com/huiziqin/gin_project/internal/service"
)

// 模拟一个推送 API 实例，用于测试
type MockPushAPIv3 struct{}

func TestJpushSend(t *testing.T) {
	// 准备测试数据
	analysisData := &models.AnalysisRedisData{
		RegistrationID: "191e35f7e17dcf49910",
		Description:    "test_description",
	}
	err := service.JpushSend(analysisData)

	// 验证结果
	if err != nil {
		t.<PERSON><PERSON><PERSON>("DemoPush 测试失败: %v", err)
	}
	t.Log("DemoPush 测试通过")
}
func TestJpushCustomSend(t *testing.T) {
	// 准备测试数据
	analysisData := &models.PushSendParams{
		RegistrationID: "191e35f7e17dcf49910",
		Title:          "test_title",
		Content:        "test_content",
		Extras: map[string]interface{}{
			"key1": "value1",
			"key2": "value2",
		},
	}
	err := service.JpushCustomSend(analysisData)

	// // 验证结果
	if err != nil {
		t.<PERSON><PERSON>("DemoPush 测试失败: %v", err)
	}
	t.Log("DemoPush 测试通过")
}

package test

import (
	"testing"

	"github.com/huiziqin/gin_project/internal/service"
	"github.com/stretchr/testify/assert"
)

func TestDingTalkServiceSendMessage(t *testing.T) {
	// 使用真实的钉钉配置进行测试
	accessToken := "a618bc576377ad1b0baaeadea6a1a758be78fe0fa7a28756f10d5ba0097a3225"
	secret := "SEC388526bcfbccdafb65c2b3711385138af6fea2e4db78d7185b7fbf4ab83ba9ca"

	dingTalkService := service.NewDingTalkService(accessToken, secret)

	// 测试发送反馈消息
	err := dingTalkService.SendFeedbackMessage(
		"这是一个测试反馈消息",
		"<EMAIL>",
		"iPhone 14 Pro (iOS 17.0)",
		"1.0.0 (1)",
	)

	// 验证没有错误
	assert.NoError(t, err)
}

func TestDingTalkServiceSendSimpleMessage(t *testing.T) {
	// 使用真实的钉钉配置进行测试
	accessToken := "a618bc576377ad1b0baaeadea6a1a758be78fe0fa7a28756f10d5ba0097a3225"
	secret := "SEC388526bcfbccdafb65c2b3711385138af6fea2e4db78d7185b7fbf4ab83ba9ca"

	dingTalkService := service.NewDingTalkService(accessToken, secret)

	// 测试发送简单文本消息
	err := dingTalkService.SendTextMessage("🧪 钉钉服务测试消息")

	// 验证没有错误
	assert.NoError(t, err)
}

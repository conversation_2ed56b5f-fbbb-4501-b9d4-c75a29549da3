version: '3'

services:
  nsqlookupd:
    container_name: kcal_fit_nsqlookupd
    image: nsqio/nsq
    command: /nsqlookupd --broadcast-address=nsqlookupd
    ports:
      - "4160:4160"
      - "4161:4161"
    networks:
      - nsq_network

  nsqd:
    container_name: kcal_fit_nsqd
    image: nsqio/nsq
    command: /nsqd --lookupd-tcp-address=nsqlookupd:4160 --broadcast-address=nsqd
    ports:
      - "4150:4150"
      - "4151:4151"
    depends_on:
      - nsqlookupd
    networks:
      - nsq_network

  nsqadmin:
    container_name: kcal_fit_nsqadmin
    image: nsqio/nsq
    command: /nsqadmin --lookupd-http-address=nsqlookupd:4161
    ports:
      - "4171:4171"
    depends_on:
      - nsqlookupd
    networks:
      - nsq_network

networks:
  nsq_network:
    driver: bridge

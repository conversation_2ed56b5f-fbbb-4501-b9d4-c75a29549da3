#!/bin/bash

# 设置目标目录
TARGET_DIR="./kcal_fit/lib"
OUTPUT_FILE="lib_files_list.txt"

# 检查目录是否存在
if [ ! -d "$TARGET_DIR" ]; then
    echo "错误: 目录 $TARGET_DIR 不存在"
    exit 1
fi

# 生成文件列表
echo "正在生成 $TARGET_DIR 目录下的文件列表..."
find "$TARGET_DIR" -type f -name "*.dart" | sort > "$OUTPUT_FILE"

# 添加其他可能的文件类型
find "$TARGET_DIR" -type f -name "*.yaml" >> "$OUTPUT_FILE"
find "$TARGET_DIR" -type f -name "*.json" >> "$OUTPUT_FILE"

# 统计文件数量
FILE_COUNT=$(wc -l < "$OUTPUT_FILE")
echo "完成! 共找到 $FILE_COUNT 个文件，列表已保存到 $OUTPUT_FILE"

# 显示前10个文件作为预览
echo "文件列表预览:"
head -n 10 "$OUTPUT_FILE"
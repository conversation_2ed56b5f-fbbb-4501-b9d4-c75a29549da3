{"folders": [{"path": "admin", "name": "backend"}, {"path": "kcal_fit", "name": "frontend"}, {"path": "scripts", "name": "scripts"}, {"path": ".", "name": "root"}], "settings": {"go.toolsEnvVars": {"GOPROXY": "https://goproxy.cn,direct", "GONOPROXY": "none;"}, "dart.flutterSdkPath": "/Users/<USER>/development/flutter/bin", "editor.formatOnSave": true}, "launch": {"version": "0.2.0", "configurations": [{"type": "go", "request": "launch", "name": "Backend", "cwd": "${workspaceFolder:backend}", "program": "${workspaceFolder:backend}/cmd/main.go", "args": [], "env": {"APP_ENV": "dev"}}, {"type": "dart", "request": "launch", "name": "Flutter", "cwd": "${workspaceFolder:frontend}", "flutterMode": "debug"}], "compounds": [{"name": "Full Stack", "configurations": ["Backend", "Flutter"], "stopAll": true}]}}
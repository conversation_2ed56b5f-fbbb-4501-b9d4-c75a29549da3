./kcal_fit/lib/app/data/models/analysis_data.dart
./kcal_fit/lib/app/data/models/analysis_models.dart
./kcal_fit/lib/app/data/models/analysis_response_push_msg.dart
./kcal_fit/lib/app/data/models/analysis_response_push_msg.g.dart
./kcal_fit/lib/app/data/models/body_data.dart
./kcal_fit/lib/app/data/models/diet_analysis.dart
./kcal_fit/lib/app/data/models/food_analysis.dart
./kcal_fit/lib/app/data/models/jpush_message.dart
./kcal_fit/lib/app/data/models/microelement_nutrition_profile.dart
./kcal_fit/lib/app/data/models/micronutrient_item.dart
./kcal_fit/lib/app/data/models/models.dart
./kcal_fit/lib/app/data/models/nutrition_models.dart
./kcal_fit/lib/app/data/models/push_message_model.dart
./kcal_fit/lib/app/data/services/data_services.dart
./kcal_fit/lib/app/data/services/hive/hive_service.dart
./kcal_fit/lib/app/data/services/sqflite/config/database_config.dart
./kcal_fit/lib/app/data/services/sqflite/config/database_schema.dart
./kcal_fit/lib/app/data/services/sqflite/core/database_connection.dart
./kcal_fit/lib/app/data/services/sqflite/core/database_exception.dart
./kcal_fit/lib/app/data/services/sqflite/core/database_service.dart
./kcal_fit/lib/app/data/services/sqflite/dao/base/base_dao.dart
./kcal_fit/lib/app/data/services/sqflite/dao/impl/body_data_dao.dart
./kcal_fit/lib/app/data/services/sqflite/dao/impl/diet_analysis_dao.dart
./kcal_fit/lib/app/data/services/sqflite/dao/impl/food_analysis_dao.dart
./kcal_fit/lib/app/modules/about_us/bindings/about_us_binding.dart
./kcal_fit/lib/app/modules/about_us/controllers/about_us_controller.dart
./kcal_fit/lib/app/modules/about_us/views/about_us_view.dart
./kcal_fit/lib/app/modules/add_diet_record/bindings/add_diet_record_binding.dart
./kcal_fit/lib/app/modules/add_diet_record/controllers/add_diet_record_controller.dart
./kcal_fit/lib/app/modules/add_diet_record/views/add_diet_record_view.dart
./kcal_fit/lib/app/modules/ai_assistant/bindings/ai_assistant_binding.dart
./kcal_fit/lib/app/modules/ai_assistant/controllers/ai_assistant_controller.dart
./kcal_fit/lib/app/modules/ai_assistant/views/ai_assistant_view.dart
./kcal_fit/lib/app/modules/body_data_form/bindings/body_data_form_binding.dart
./kcal_fit/lib/app/modules/body_data_form/controllers/body_data_form_controller.dart
./kcal_fit/lib/app/modules/body_data_form/views/body_data_form_view.dart
./kcal_fit/lib/app/modules/goal_setting/bindings/goal_setting_binding.dart
./kcal_fit/lib/app/modules/goal_setting/controllers/goal_setting_controller.dart
./kcal_fit/lib/app/modules/goal_setting/views/goal_setting_view.dart
./kcal_fit/lib/app/modules/help_feedback/bindings/help_feedback_binding.dart
./kcal_fit/lib/app/modules/help_feedback/controllers/help_feedback_controller.dart
./kcal_fit/lib/app/modules/help_feedback/views/help_feedback_view.dart
./kcal_fit/lib/app/modules/home/<USER>/home_binding.dart
./kcal_fit/lib/app/modules/home/<USER>/home_controller.dart
./kcal_fit/lib/app/modules/home/<USER>/home_view.dart
./kcal_fit/lib/app/modules/index/bindings/index_binding.dart
./kcal_fit/lib/app/modules/index/controllers/index_controller.dart
./kcal_fit/lib/app/modules/index/views/index_view.dart
./kcal_fit/lib/app/modules/index/widgets/calories_detail_widget.dart
./kcal_fit/lib/app/modules/my/bindings/my_binding.dart
./kcal_fit/lib/app/modules/my/controllers/my_controller.dart
./kcal_fit/lib/app/modules/my/views/my_view.dart
./kcal_fit/lib/app/modules/my/views/weight_change_dialog.dart
./kcal_fit/lib/app/modules/personal_info/bindings/personal_info_binding.dart
./kcal_fit/lib/app/modules/personal_info/controllers/personal_info_controller.dart
./kcal_fit/lib/app/modules/personal_info/views/personal_info_view.dart
./kcal_fit/lib/app/modules/plan/bindings/plan_binding.dart
./kcal_fit/lib/app/modules/plan/controllers/plan_controller.dart
./kcal_fit/lib/app/modules/plan/views/plan_view.dart
./kcal_fit/lib/app/modules/privacy_setting/bindings/privacy_setting_binding.dart
./kcal_fit/lib/app/modules/privacy_setting/controllers/privacy_setting_controller.dart
./kcal_fit/lib/app/modules/privacy_setting/views/privacy_setting_view.dart
./kcal_fit/lib/app/modules/record/bindings/record_binding.dart
./kcal_fit/lib/app/modules/record/controllers/record_controller.dart
./kcal_fit/lib/app/modules/record/views/record_view.dart
./kcal_fit/lib/app/modules/reminder_setting/bindings/reminder_setting_binding.dart
./kcal_fit/lib/app/modules/reminder_setting/controllers/reminder_setting_controller.dart
./kcal_fit/lib/app/modules/reminder_setting/views/reminder_setting_view.dart
./kcal_fit/lib/app/modules/theme_setting/bindings/theme_setting_binding.dart
./kcal_fit/lib/app/modules/theme_setting/controllers/theme_setting_controller.dart
./kcal_fit/lib/app/modules/theme_setting/views/theme_setting_view.dart
./kcal_fit/lib/app/routes/app_pages.dart
./kcal_fit/lib/app/routes/app_routes.dart
./kcal_fit/lib/app/shared/controllers/base_controller.dart
./kcal_fit/lib/app/shared/controllers/theme_controller.dart
./kcal_fit/lib/app/shared/widgets/calendarCard/calendar_card.dart
./kcal_fit/lib/app/shared/widgets/cardIconText/card_icon_text_binding.dart
./kcal_fit/lib/app/shared/widgets/cardIconText/cord_icon_text.dart
./kcal_fit/lib/app/shared/widgets/cardIconTextVal/card_icon_text_val.dart
./kcal_fit/lib/app/shared/widgets/cardIconTextValProgress/card_icon_text_val_progress.dart
./kcal_fit/lib/app/shared/widgets/circularProgress/circular_progress.dart
./kcal_fit/lib/app/shared/widgets/common_app_bar.dart
./kcal_fit/lib/app/shared/widgets/common_button.dart
./kcal_fit/lib/app/shared/widgets/common_card.dart
./kcal_fit/lib/app/shared/widgets/common_input.dart
./kcal_fit/lib/app/shared/widgets/demo.dart
./kcal_fit/lib/app/shared/widgets/loading_widget.dart
./kcal_fit/lib/app/shared/widgets/typewriter_text.dart
./kcal_fit/lib/app/shared/widgets/widgets.dart
./kcal_fit/lib/core/config/app_config.dart
./kcal_fit/lib/core/constants/constants.dart
./kcal_fit/lib/core/constants/health_goals.dart
./kcal_fit/lib/core/constants/locales.g.dart
./kcal_fit/lib/core/init/app_initializer.dart
./kcal_fit/lib/core/services/data_preloader_service.dart
./kcal_fit/lib/core/services/http/http_api.dart
./kcal_fit/lib/core/services/http/http_error.dart
./kcal_fit/lib/core/services/http/http_error_event.dart
./kcal_fit/lib/core/services/http/http_util.dart
./kcal_fit/lib/core/services/jpush/database_field_constants.dart
./kcal_fit/lib/core/services/jpush/nutrition_data_processor.dart
./kcal_fit/lib/core/services/jpush/push_service.dart
./kcal_fit/lib/core/services/services.dart
./kcal_fit/lib/core/services/stt/speech_to_text.dart
./kcal_fit/lib/core/services/theme_service.dart
./kcal_fit/lib/core/services/user_guide_service.dart
./kcal_fit/lib/core/theme/app_theme.dart
./kcal_fit/lib/core/utils/date_picker_util.dart
./kcal_fit/lib/core/utils/logger_util.dart
./kcal_fit/lib/core/utils/message_util.dart
./kcal_fit/lib/core/utils/performance_monitor.dart
./kcal_fit/lib/core/utils/permission_utils.dart
./kcal_fit/lib/core/utils/state_manager.dart
./kcal_fit/lib/core/widgets/user_data_input_widgets.dart
./kcal_fit/lib/main.dart
